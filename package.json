{"name": "restaurant-inventory", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "vercel-build": "NODE_OPTIONS=--max_old_space_size=4096 next build"}, "dependencies": {"@supabase/supabase-js": "^2.49.8", "chart.js": "^4.4.9", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "next": "14.1.0", "next-auth": "^4.24.11", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@eslint/eslintrc": "^2.1.4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/mongodb": "^4.0.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "ignore-loader": "^0.1.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.35", "postcss-nesting": "^12.0.2", "serve": "^14.2.4", "tailwindcss": "^3.4.1", "typescript": "^5"}}