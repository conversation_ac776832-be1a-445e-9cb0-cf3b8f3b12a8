-- =====================================================
-- DELETE ALL LOCAL ITEMS SQL SCRIPT
-- =====================================================
-- 
-- This script ONLY deletes items from your LOCAL Supabase database.
-- It does NOT affect your Square catalog in any way.
-- 
-- SAFETY: Your Square POS and catalog remain completely untouched.
-- 
-- Use this script in your Supabase SQL Editor:
-- https://supabase.com/dashboard/project/YOUR_PROJECT/sql
-- 
-- =====================================================

-- Step 1: Delete all item variants (due to foreign key constraints)
-- This removes all variants associated with items
DELETE FROM item_variants;

-- Step 2: Delete all item ingredients 
-- This removes all ingredient relationships
DELETE FROM item_ingredients;

-- Step 3: Delete all items
-- This removes all menu items from your local database
DELETE FROM items;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================
-- Run these after deletion to verify everything is clean:

-- Check item variants count (should be 0)
SELECT COUNT(*) as variant_count FROM item_variants;

-- Check item ingredients count (should be 0)  
SELECT COUNT(*) as ingredient_count FROM item_ingredients;

-- Check items count (should be 0)
SELECT COUNT(*) as item_count FROM items;

-- =====================================================
-- WHAT THIS SCRIPT DOES:
-- =====================================================
-- ✅ Deletes all records from item_variants table
-- ✅ Deletes all records from item_ingredients table  
-- ✅ Deletes all records from items table
-- ✅ Clears your local menu database completely
-- ✅ Prepares for fresh sync from Square catalog
--
-- WHAT THIS SCRIPT DOES NOT DO:
-- ❌ Does NOT touch Square catalog
-- ❌ Does NOT affect Square POS
-- ❌ Does NOT delete Square items
-- ❌ Does NOT make any API calls to Square
-- ❌ Does NOT affect inventory_items table
-- ❌ Does NOT affect users or other tables
-- 
-- =====================================================
-- AFTER RUNNING THIS SCRIPT:
-- =====================================================
-- 1. Go to /square/catalog in your app
-- 2. Select Square items to sync
-- 3. Click "Sync Selected Items"  
-- 4. Add ingredients to synced items
-- 
-- Your Square catalog will be completely unaffected!
-- =====================================================
