-- =====================================================
-- BACKUP SCRIPT - RUN BEFORE DELETION
-- =====================================================
-- 
-- This script creates backup tables of your current data
-- Run this BEFORE deleting anything for safety
-- 
-- =====================================================

-- Create backup tables with current timestamp
-- Replace 'YYYYMMDD' with today's date (e.g., 20241231)

-- BACKUP ITEMS
CREATE TABLE items_backup_YYYYMMDD AS 
SELECT * FROM items;

-- BACKUP ITEM VARIANTS  
CREATE TABLE item_variants_backup_YYYYMMDD AS
SELECT * FROM item_variants;

-- BACKUP ITEM INGREDIENTS
CREATE TABLE item_ingredients_backup_YYYYMMDD AS  
SELECT * FROM item_ingredients;

-- =====================================================
-- VERIFY BACKUPS CREATED
-- =====================================================

SELECT 'BACKUP VERIFICATION' as status;

SELECT 
  (SELECT COUNT(*) FROM items) as original_items,
  (SELECT COUNT(*) FROM items_backup_YYYYMMDD) as backup_items;

SELECT 
  (SELECT COUNT(*) FROM item_variants) as original_variants,
  (SELECT COUNT(*) FROM item_variants_backup_YYYYMMDD) as backup_variants;

SELECT 
  (SELECT COUNT(*) FROM item_ingredients) as original_ingredients,
  (SELECT COUNT(*) FROM item_ingredients_backup_YYYYMMDD) as backup_ingredients;

-- =====================================================
-- RESTORE SCRIPT (IF NEEDED)
-- =====================================================
-- 
-- If you need to restore your data later, use these commands:
-- 
-- RESTORE ITEMS:
-- INSERT INTO items SELECT * FROM items_backup_YYYYMMDD;
-- 
-- RESTORE VARIANTS:
-- INSERT INTO item_variants SELECT * FROM item_variants_backup_YYYYMMDD;
-- 
-- RESTORE INGREDIENTS:
-- INSERT INTO item_ingredients SELECT * FROM item_ingredients_backup_YYYYMMDD;
-- 
-- =====================================================

SELECT 'BACKUP COMPLETE - SAFE TO PROCEED WITH DELETION' as result;

-- =====================================================
-- CLEANUP BACKUPS (OPTIONAL - RUN LATER)
-- =====================================================
-- 
-- After you're satisfied with the new setup, you can remove backups:
-- 
-- DROP TABLE items_backup_YYYYMMDD;
-- DROP TABLE item_variants_backup_YYYYMMDD;  
-- DROP TABLE item_ingredients_backup_YYYYMMDD;
-- 
-- =====================================================
