-- =====================================================
-- STEP-BY-STEP SAFE DELETION SCRIPT
-- =====================================================
-- 
-- Run each step individually in Supabase SQL Editor
-- Check results after each step before proceeding
-- 
-- =====================================================

-- STEP 1: CHECK CURRENT STATE
-- =====================================================
-- See what you currently have before deletion

SELECT 'CURRENT STATE BEFORE DELETION' as status;

SELECT COUNT(*) as current_items FROM items;
SELECT COUNT(*) as current_variants FROM item_variants;  
SELECT COUNT(*) as current_ingredients FROM item_ingredients;

-- List all current items (optional - comment out if too many)
-- SELECT id, name, category, square_item_id FROM items LIMIT 10;

-- =====================================================
-- STEP 2: DELETE ITEM VARIANTS (SAFEST FIRST)
-- =====================================================
-- Run this first due to foreign key constraints

-- Check variants before deletion
SELECT COUNT(*) as variants_to_delete FROM item_variants;

-- Delete all variants
DELETE FROM item_variants;

-- Verify deletion
SELECT COUNT(*) as remaining_variants FROM item_variants;

-- =====================================================
-- STEP 3: DELETE ITEM INGREDIENTS  
-- =====================================================

-- Check ingredients before deletion
SELECT COUNT(*) as ingredients_to_delete FROM item_ingredients;

-- Delete all item ingredients
DELETE FROM item_ingredients;

-- Verify deletion
SELECT COUNT(*) as remaining_ingredients FROM item_ingredients;

-- =====================================================
-- STEP 4: DELETE ITEMS (FINAL STEP)
-- =====================================================

-- Check items before deletion
SELECT COUNT(*) as items_to_delete FROM items;

-- Delete all items
DELETE FROM items;

-- Verify deletion
SELECT COUNT(*) as remaining_items FROM items;

-- =====================================================
-- STEP 5: FINAL VERIFICATION
-- =====================================================

SELECT 'FINAL STATE AFTER DELETION' as status;

SELECT 
  (SELECT COUNT(*) FROM items) as items_count,
  (SELECT COUNT(*) FROM item_variants) as variants_count,
  (SELECT COUNT(*) FROM item_ingredients) as ingredients_count;

-- =====================================================
-- STEP 6: VERIFY OTHER TABLES UNTOUCHED
-- =====================================================
-- These should remain unchanged

SELECT 
  (SELECT COUNT(*) FROM inventory_items) as inventory_items_count,
  (SELECT COUNT(*) FROM users) as users_count,
  (SELECT COUNT(*) FROM square_config) as square_config_count;

-- =====================================================
-- SUCCESS MESSAGE
-- =====================================================

SELECT 'LOCAL ITEMS DELETED SUCCESSFULLY - SQUARE CATALOG UNAFFECTED' as result;

-- =====================================================
-- NEXT STEPS:
-- =====================================================
-- 1. Go to your app: /square/catalog
-- 2. Select Square items to sync
-- 3. Click "Sync Selected Items"
-- 4. Add ingredients to synced items
-- 
-- Your Square catalog is completely safe! 🎉
-- =====================================================
