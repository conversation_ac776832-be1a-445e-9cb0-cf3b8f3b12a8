# Restaurant Inventory Management System

A comprehensive inventory management system for Vietnamese restaurants, built with Next.js, Supabase (PostgreSQL), and TypeScript. Features a modern backend API architecture with server-side operations for optimal performance and security.

## 🚀 Quick Start

### Default Admin Credentials
- **Email:** `<EMAIL>`
- **Password:** `admin123`

**⚠️ IMPORTANT:** Change this password after first login!

### Live Demo
The application is deployed and accessible at: **[Your Vercel URL]**

## 🏗️ Architecture

This application uses a **modern backend API architecture**:

- **Frontend:** Next.js 14 with TypeScript and Tailwind CSS
- **Backend:** Next.js API Routes (server-side operations)
- **Database:** Supabase (PostgreSQL) with Row Level Security
- **Authentication:** Supabase Auth
- **Deployment:** Vercel (recommended) or EC2 with PM2

### Backend API Architecture
All major operations go through dedicated backend API routes:
- **`/api/inventory/items`** - Inventory management
- **`/api/items/menu`** - Menu item operations
- **`/api/square/*`** - Square integration (catalog, sales, sync)
- **Client → Backend API → Supabase** (secure, fast, reliable)

## ✨ Features

### 📦 Inventory Management
- Track inventory items with quantities, costs, and storage locations
- Record transactions (purchases, usage, waste, adjustments)
- Low stock alerts and automated notifications
- Multi-location storage tracking

### 🍽️ Menu Item Management
- Define menu items with ingredients and recipes
- Track item production and costs
- Calculate profit margins and analyze profitability
- Multi-language support (English/Vietnamese)

### 👥 User Management
- **Role-based access control:** Admin, Manager, Staff, Kitchen
- **Supabase Authentication** with secure session management
- **Staff-specific features:** Dedicated inventory input interface

### 📊 Reporting & Analytics
- Comprehensive inventory valuation reports
- Usage analysis and cost tracking
- Waste monitoring and optimization
- Visual charts and data visualization

### 🔗 Square Integration
- **Real-time catalog sync** with Square POS
- **Sales data integration** from Square Orders API
- **Automated item synchronization**
- **Backend API architecture** for reliable data processing

### 🛡️ Security & Performance
- **Row Level Security (RLS)** with Supabase
- **Server-side API operations** for better security
- **Optimized backend architecture** prevents timeouts
- **CORS-free operations** through dedicated API routes

## 📋 Prerequisites

- **Node.js** (v18 or higher)
- **Supabase account** (free at [supabase.com](https://supabase.com))
- **Vercel account** (free at [vercel.com](https://vercel.com)) - recommended for deployment

## 🛠️ Setup Instructions

### 🚀 Quick Deploy to Vercel (Recommended)

1. **Fork this repository** to your GitHub account

2. **Set up Supabase:**
   - Create a new project at [Supabase](https://supabase.com)
   - Go to **Settings → API** to get your project URL and anon key
   - Run the database migrations in **SQL Editor** (see `supabase/migrations/`)

3. **Deploy to Vercel:**
   - Connect your GitHub repository to Vercel
   - Add environment variables in Vercel dashboard:
     ```
     NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
     NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
     ```
   - Deploy automatically with Vercel

4. **Create admin user** in Supabase Auth dashboard:
   - Email: `<EMAIL>`
   - Password: `admin123`
   - Confirm email: ✅

### 💻 Local Development Setup

1. **Clone the repository:**
   ```bash
   git clone https://github.com/cocobryan91/inventory-management.git
   cd inventory-management
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Create `.env.local` file:**
   ```bash
   # Supabase connection
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. **Start development server:**
   ```bash
   npm run dev
   ```

5. **Access the application:**
   - Open [http://localhost:3000](http://localhost:3000)
   - Login with admin credentials above

## 📁 Project Structure

```
restaurant-inventory/
├── src/
│   ├── app/                  # Next.js 14 app router
│   │   ├── api/              # Backend API routes
│   │   ├── auth/             # Authentication pages
│   │   ├── inventory/        # Inventory management pages
│   │   ├── items/            # Menu items pages
│   │   ├── square/           # Square integration pages
│   │   └── ...
│   ├── components/           # React components
│   │   ├── auth/             # Authentication components
│   │   ├── inventory/        # Inventory components
│   │   ├── layout/           # Layout components
│   │   └── ...
│   ├── pages/api/            # Backend API routes (Next.js API)
│   │   ├── inventory/        # Inventory API endpoints
│   │   ├── items/            # Menu items API endpoints
│   │   └── square/           # Square integration API endpoints
│   ├── services/             # Business logic and API services
│   ├── types/                # TypeScript type definitions
│   └── lib/                  # Utility libraries (Supabase client)
├── supabase/
│   └── migrations/           # Database migrations
├── public/                   # Static assets
└── .env.local                # Environment variables
```

## 🔧 Environment Variables

Create a `.env.local` file with the following variables:

```bash
# Supabase Configuration (Required)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Square Integration (Optional)
SQUARE_ACCESS_TOKEN=your_square_access_token
SQUARE_LOCATION_ID=your_square_location_id
```

## 🔌 Backend API Architecture

The application uses a **modern backend API architecture** with dedicated server-side routes for optimal performance and security.

### 🔐 Authentication
**Supabase Auth** handles all authentication operations:

```typescript
import { supabase } from '@/lib/supabase';

// Login
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password'
});

// Register
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'password'
});
```

### 📦 Inventory Management API

#### **`/api/inventory/items`**
- **GET** - Get all inventory items
  - Query params: `category`, `search`, `belowThreshold`, `supplierId`
  - Response: Array of inventory items

- **POST** - Create new inventory item
  - Request: `{ name, category, unit, quantity, costPerUnit, minThreshold, storage }`
  - Response: Created inventory item

#### **`/api/inventory/items/[id]`**
- **GET** - Get specific inventory item
- **PATCH** - Update inventory item
- **DELETE** - Delete inventory item

### 🍽️ Menu Items API

#### **`/api/items/menu`**
- **GET** - Get all menu items
  - Query params: `category`, `search`
  - Response: Array of menu items with variants

- **POST** - Create new menu item
  - Request: `{ name, vietnameseName, category, description, preparationTime }`
  - Response: Created menu item

### 🔗 Square Integration API

#### **`/api/square/config`**
- **GET** - Check Square API configuration
- **POST** - Save Square access token
  - Request: `{ accessToken }`

#### **`/api/square/catalog`**
- **GET** - Get Square catalog items
  - Headers: `Authorization: Bearer {accessToken}`

#### **`/api/square/sync`**
- **POST** - Sync Square catalog to database
  - Request: `{ items, overwrite }`
  - Response: `{ total, synced, skipped, errors }`

#### **`/api/square/sales`**
- **POST** - Get Square sales data
  - Request: `{ startDate, endDate?, catalogItemId?, locationId? }`
  - Response: Sales data with order details

#### **`/api/square/locations`**
- **GET** - Get Square locations
  - Headers: `Authorization: Bearer {accessToken}`

#### **`/api/square/orders`**
- **POST** - Get Square orders
  - Request: `{ locationIds, startDate, endDate? }`

### 🛡️ Security Features

- **Server-side operations** - All API calls go through backend
- **Access token protection** - Square tokens stored securely in Supabase
- **Row Level Security** - Database-level access control
- **CORS-free** - No cross-origin issues with backend architecture
- **Error handling** - Comprehensive error logging and responses

## Supabase Database Tables

The system uses the following Supabase (PostgreSQL) tables:

- **inventory_items**: Inventory items (ingredients and supplies)
- **transactions**: Inventory transactions (purchases, usage, waste, adjustments)
- **items**: Menu items with ingredients and variants
- **item_variants**: Variants of menu items (different sizes, types)
- **item_ingredients**: Junction table for item-ingredient relationships
- **productions**: Records of item production
- **users**: User accounts with roles and permissions (managed by Supabase Auth)
- **alerts**: System alerts for low stock, etc.
- **suppliers**: Supplier information
- **storage**: Storage locations for inventory items

## 🚀 Deployment Options

### Vercel (Recommended)

**Vercel** provides the best deployment experience with automatic builds, global CDN, and seamless integration.

#### Quick Deploy Steps:

1. **Fork this repository** to your GitHub account

2. **Set up Supabase:**
   - Create project at [Supabase](https://supabase.com)
   - Run database migrations from `supabase/migrations/`
   - Get your project URL and anon key from Settings → API

3. **Deploy to Vercel:**
   - Connect your GitHub repo to [Vercel](https://vercel.com)
   - Add environment variables:
     ```
     NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
     NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
     ```
   - Deploy automatically

4. **Create admin user** in Supabase Auth:
   - Email: `<EMAIL>`
   - Password: `admin123`

### Alternative: EC2 with PM2

For self-hosted deployment, use the `manage-app.sh` script:

```bash
# Clone repository
git clone https://github.com/cocobryan91/inventory-management.git
cd inventory-management

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your Supabase credentials

# Start with PM2
./manage-app.sh start
```

**Management commands:**
- `./manage-app.sh start` - Start application
- `./manage-app.sh stop` - Stop application
- `./manage-app.sh restart` - Restart application
- `./manage-app.sh status` - Check status
- `./manage-app.sh logs` - View logs

## License

[MIT](LICENSE)
