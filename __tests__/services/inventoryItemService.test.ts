import { ObjectId } from 'mongodb';
import {
  getAllInventoryItems,
  getInventoryItemById,
  addInventoryItem,
  updateInventoryItem,
  deleteInventoryItem,
  adjustInventory,
  getLowStockItems,
  calculateInventoryValue,
  getItemsByStorage,
  getItemsByCategory
} from '../../src/services/inventoryItemService';
import { getInventoryItemsCollection, getTransactionsCollection } from '../../src/services/db';
import { addTransaction } from '../../src/services/inventoryTransactionService';
import { InventoryItem } from '../../src/types/inventory';

// Mock the database and transaction service
jest.mock('../../src/services/db');
jest.mock('../../src/services/inventoryTransactionService');

describe('inventoryItemService', () => {
  // Mock data
  const mockInventoryItems: InventoryItem[] = [
    {
      id: '1',
      name: 'Rice',
      vietnameseName: 'Cơm',
      quantity: 50,
      unit: 'kg',
      minThreshold: 10,
      costPerUnit: 2.5,
      category: 'Dry Goods',
      storage: 'pantry',
      supplierId: 'supplier1',
      lastRestockDate: new Date('2023-01-01')
    },
    {
      id: '2',
      name: 'Chicken',
      vietnameseName: 'Gà',
      quantity: 5,
      unit: 'kg',
      minThreshold: 10,
      costPerUnit: 8.5,
      category: 'Meat',
      storage: 'fridge',
      supplierId: 'supplier2',
      lastRestockDate: new Date('2023-01-02')
    },
    {
      id: '3',
      name: 'Onions',
      vietnameseName: 'Hành tây',
      quantity: 15,
      unit: 'kg',
      minThreshold: 5,
      costPerUnit: 1.5,
      category: 'Produce',
      storage: 'pantry',
      supplierId: 'supplier3',
      lastRestockDate: new Date('2023-01-03')
    }
  ];

  // Mock collection methods
  const mockFind = jest.fn();
  const mockFindOne = jest.fn();
  const mockInsertOne = jest.fn();
  const mockUpdateOne = jest.fn();
  const mockDeleteOne = jest.fn();
  const mockToArray = jest.fn();

  // Setup mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock the find method to return a chainable object with toArray
    mockFind.mockReturnValue({ toArray: mockToArray });
    mockToArray.mockResolvedValue(mockInventoryItems);
    
    // Mock other collection methods
    mockFindOne.mockImplementation((query) => {
      const item = mockInventoryItems.find(item => item.id === query.id);
      return Promise.resolve(item || null);
    });
    
    mockInsertOne.mockImplementation((item) => {
      return Promise.resolve({ insertedId: new ObjectId() });
    });
    
    mockUpdateOne.mockResolvedValue({ modifiedCount: 1 });
    mockDeleteOne.mockResolvedValue({ deletedCount: 1 });
    
    // Mock the getInventoryItemsCollection function
    (getInventoryItemsCollection as jest.Mock).mockResolvedValue({
      find: mockFind,
      findOne: mockFindOne,
      insertOne: mockInsertOne,
      updateOne: mockUpdateOne,
      deleteOne: mockDeleteOne
    });
    
    // Mock the addTransaction function
    (addTransaction as jest.Mock).mockImplementation((transactionData) => {
      return Promise.resolve({
        id: new ObjectId().toString(),
        ...transactionData
      });
    });
  });

  describe('getAllInventoryItems', () => {
    it('should return all inventory items when no filter is provided', async () => {
      const result = await getAllInventoryItems();
      
      expect(mockFind).toHaveBeenCalledWith({});
      expect(mockToArray).toHaveBeenCalled();
      expect(result).toEqual(mockInventoryItems);
    });

    it('should filter by category when provided', async () => {
      await getAllInventoryItems({ category: 'Meat' });
      
      expect(mockFind).toHaveBeenCalledWith({ category: 'Meat' });
    });

    it('should filter by belowThreshold when true', async () => {
      await getAllInventoryItems({ belowThreshold: true });
      
      expect(mockFind).toHaveBeenCalledWith({ 
        $expr: { $lte: ['$quantity', '$minThreshold'] } 
      });
    });

    it('should filter by search term when provided', async () => {
      await getAllInventoryItems({ search: 'rice' });
      
      expect(mockFind).toHaveBeenCalledWith({ 
        $or: [
          { name: { $regex: 'rice', $options: 'i' } },
          { vietnameseName: { $regex: 'rice', $options: 'i' } },
          { category: { $regex: 'rice', $options: 'i' } }
        ]
      });
    });
  });

  describe('getInventoryItemById', () => {
    it('should return an inventory item when found', async () => {
      const result = await getInventoryItemById('1');
      
      expect(mockFindOne).toHaveBeenCalledWith({ id: '1' });
      expect(result).toEqual(mockInventoryItems[0]);
    });

    it('should return null when item is not found', async () => {
      mockFindOne.mockResolvedValueOnce(null);
      
      const result = await getInventoryItemById('999');
      
      expect(mockFindOne).toHaveBeenCalledWith({ id: '999' });
      expect(result).toBeNull();
    });
  });

  describe('addInventoryItem', () => {
    it('should add a new inventory item and return it', async () => {
      const newItem = {
        name: 'Garlic',
        vietnameseName: 'Tỏi',
        quantity: 10,
        unit: 'kg',
        minThreshold: 2,
        costPerUnit: 3.5,
        category: 'Produce',
        storage: 'pantry',
        supplierId: 'supplier3'
      };
      
      const result = await addInventoryItem(newItem);
      
      expect(mockInsertOne).toHaveBeenCalled();
      expect(result).toMatchObject(newItem);
      expect(result.id).toBeDefined();
    });
  });

  describe('updateInventoryItem', () => {
    it('should update an existing inventory item and return it', async () => {
      const updates = { quantity: 60, costPerUnit: 3.0 };
      
      const result = await updateInventoryItem('1', updates);
      
      expect(mockUpdateOne).toHaveBeenCalledWith(
        { id: '1' },
        { $set: updates }
      );
      expect(mockFindOne).toHaveBeenCalledTimes(2); // Once to check existence, once to get updated item
      expect(result).toBeDefined();
    });

    it('should return null when item to update is not found', async () => {
      mockFindOne.mockResolvedValueOnce(null);
      
      const result = await updateInventoryItem('999', { quantity: 60 });
      
      expect(mockUpdateOne).not.toHaveBeenCalled();
      expect(result).toBeNull();
    });
  });

  describe('deleteInventoryItem', () => {
    it('should delete an inventory item and return true', async () => {
      const result = await deleteInventoryItem('1');
      
      expect(mockDeleteOne).toHaveBeenCalledWith({ id: '1' });
      expect(result).toBe(true);
    });

    it('should return false when item to delete is not found', async () => {
      mockDeleteOne.mockResolvedValueOnce({ deletedCount: 0 });
      
      const result = await deleteInventoryItem('999');
      
      expect(mockDeleteOne).toHaveBeenCalledWith({ id: '999' });
      expect(result).toBe(false);
    });
  });

  describe('adjustInventory', () => {
    it('should increase quantity for purchase transactions', async () => {
      const result = await adjustInventory('1', 10, 'purchase', 'Restocking');
      
      expect(result.success).toBe(true);
      expect(result.inventoryItem).toBeDefined();
      expect(result.transaction).toBeDefined();
      expect(mockUpdateOne).toHaveBeenCalledWith(
        { id: '1' },
        { $set: { quantity: 60, lastRestockDate: expect.any(Date) } }
      );
    });

    it('should decrease quantity for usage transactions', async () => {
      const result = await adjustInventory('1', 10, 'usage', 'Used in kitchen');
      
      expect(result.success).toBe(true);
      expect(result.inventoryItem).toBeDefined();
      expect(result.transaction).toBeDefined();
      expect(mockUpdateOne).toHaveBeenCalledWith(
        { id: '1' },
        { $set: { quantity: 40, lastRestockDate: expect.any(Date) } }
      );
    });

    it('should fail when trying to use more than available quantity', async () => {
      const result = await adjustInventory('1', 100, 'usage', 'Too much');
      
      expect(result.success).toBe(false);
      expect(mockUpdateOne).not.toHaveBeenCalled();
    });
  });

  describe('getLowStockItems', () => {
    it('should return items below threshold', async () => {
      await getLowStockItems();
      
      expect(mockFind).toHaveBeenCalledWith({ 
        $expr: { $lte: ['$quantity', '$minThreshold'] } 
      });
    });
  });

  describe('calculateInventoryValue', () => {
    it('should calculate total inventory value correctly', async () => {
      const result = await calculateInventoryValue();
      
      // Rice: 50 * 2.5 = 125
      // Chicken: 5 * 8.5 = 42.5
      // Onions: 15 * 1.5 = 22.5
      // Total: 125 + 42.5 + 22.5 = 190
      expect(result).toBe(190);
    });
  });

  describe('getItemsByStorage', () => {
    it('should return items by storage location', async () => {
      await getItemsByStorage('pantry');
      
      expect(mockFind).toHaveBeenCalledWith({ storage: 'pantry' });
    });
  });

  describe('getItemsByCategory', () => {
    it('should return items by category', async () => {
      await getItemsByCategory('Meat');
      
      expect(mockFind).toHaveBeenCalledWith({ category: 'Meat' });
    });
  });
});
