import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import InventoryItemsPage from '../../src/app/inventory/page';
import { inventoryItemsApi } from '../../src/services/apiService';
import { useAuth } from '../../src/contexts/AuthContext';

// Mock the next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
  useParams: () => ({}),
}));

// Mock the apiService
jest.mock('../../src/services/apiService', () => ({
  inventoryItemsApi: {
    getAll: jest.fn(),
    delete: jest.fn(),
  },
}));

// Mock the AuthContext
jest.mock('../../src/contexts/AuthContext', () => ({
  useAuth: jest.fn(),
}));

// Mock the MainLayout component
jest.mock('../../src/components/layout/MainLayout', () => {
  return {
    __esModule: true,
    default: ({ children }: { children: React.ReactNode }) => <div data-testid="main-layout">{children}</div>,
  };
});

describe('InventoryItemsPage', () => {
  const mockInventoryItems = [
    {
      id: '1',
      name: 'Rice',
      vietnameseName: 'Cơm',
      quantity: 50,
      unit: 'kg',
      minThreshold: 10,
      costPerUnit: 2.5,
      category: 'Dry Goods',
      storage: 'pantry',
      supplierId: 'supplier1',
      lastRestockDate: new Date('2023-01-01')
    },
    {
      id: '2',
      name: 'Chicken',
      vietnameseName: 'Gà',
      quantity: 5,
      unit: 'kg',
      minThreshold: 10,
      costPerUnit: 8.5,
      category: 'Meat',
      storage: 'fridge',
      supplierId: 'supplier2',
      lastRestockDate: new Date('2023-01-02')
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock the auth context
    (useAuth as jest.Mock).mockReturnValue({
      user: { id: 'user1', username: 'testuser', role: 'admin' },
      isAuthenticated: true,
    });

    // Mock the API response
    (inventoryItemsApi.getAll as jest.Mock).mockResolvedValue(mockInventoryItems);
    (inventoryItemsApi.delete as jest.Mock).mockResolvedValue({ success: true });

    // Mock window.confirm
    window.confirm = jest.fn().mockImplementation(() => true);
  });

  it('renders the inventory items page with items', async () => {
    render(<InventoryItemsPage />);

    // Check that the page is rendered within the layout
    expect(screen.getByTestId('main-layout')).toBeInTheDocument();

    // Check that the "Add New Inventory Item" button is rendered
    expect(screen.getByText(/Add New Inventory Item/i)).toBeInTheDocument();

    // Wait for the items to be loaded
    await waitFor(() => {
      expect(screen.getByText('Rice')).toBeInTheDocument();
      expect(screen.getByText('Chicken')).toBeInTheDocument();
    });

    // Check that the API was called with no filters
    expect(inventoryItemsApi.getAll).toHaveBeenCalledWith({
      category: undefined,
      belowThreshold: undefined,
      search: undefined
    });
  });

  it('filters items by category', async () => {
    render(<InventoryItemsPage />);

    // Wait for the items to be loaded
    await waitFor(() => {
      expect(screen.getByText('Rice')).toBeInTheDocument();
    });

    // Select a category filter
    fireEvent.change(screen.getByLabelText(/Category/i), { target: { value: 'Meat' } });

    // Check that the API was called with the category filter
    await waitFor(() => {
      expect(inventoryItemsApi.getAll).toHaveBeenCalledWith({
        category: 'Meat',
        belowThreshold: undefined,
        search: undefined
      });
    });
  });

  it('filters items by search term', async () => {
    render(<InventoryItemsPage />);

    // Wait for the items to be loaded
    await waitFor(() => {
      expect(screen.getByText('Rice')).toBeInTheDocument();
    });

    // Enter a search term
    fireEvent.change(screen.getByLabelText(/Search/i), { target: { value: 'rice' } });

    // Check that the API was called with the search filter
    await waitFor(() => {
      expect(inventoryItemsApi.getAll).toHaveBeenCalledWith({
        category: undefined,
        belowThreshold: undefined,
        search: 'rice'
      });
    });
  });

  it('filters items by below threshold', async () => {
    render(<InventoryItemsPage />);

    // Wait for the items to be loaded
    await waitFor(() => {
      expect(screen.getByText('Rice')).toBeInTheDocument();
    });

    // Check the below threshold checkbox
    const checkbox = screen.getByLabelText(/Show only items below threshold/i);
    fireEvent.click(checkbox);

    // Check that the API was called with the below threshold filter
    await waitFor(() => {
      expect(inventoryItemsApi.getAll).toHaveBeenCalledWith({
        category: undefined,
        belowThreshold: true,
        search: undefined
      });
    });
  });

  it('deletes an item when delete button is clicked', async () => {
    render(<InventoryItemsPage />);

    // Wait for the items to be loaded
    await waitFor(() => {
      expect(screen.getByText('Rice')).toBeInTheDocument();
    });

    // Find and click the delete button for the first item
    const deleteButtons = screen.getAllByText('Delete');
    fireEvent.click(deleteButtons[0]);

    // Check that the confirmation dialog was shown
    expect(window.confirm).toHaveBeenCalledWith('Are you sure you want to delete this inventory item?');

    // Check that the API was called to delete the item
    await waitFor(() => {
      expect(inventoryItemsApi.delete).toHaveBeenCalledWith('1');
    });

    // The component should update the state directly instead of calling the API again
    // This is a more efficient approach in React
    // So we'll just check that the item was removed from the state
    await waitFor(() => {
      expect(screen.queryByText('Rice')).not.toBeInTheDocument();
    });
  });

  it('shows "No inventory items found" when there are no items', async () => {
    // Mock empty response
    (inventoryItemsApi.getAll as jest.Mock).mockResolvedValueOnce([]);

    render(<InventoryItemsPage />);

    // Check that the "No inventory items found" message is displayed
    await waitFor(() => {
      expect(screen.getByText(/No inventory items found/i)).toBeInTheDocument();
    });
  });
});
