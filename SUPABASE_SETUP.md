# Supabase Setup Guide

This guide will help you set up Supabase for your Restaurant Inventory Management System.

## Step 1: Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and create a free account
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - Name: `restaurant-inventory`
   - Database Password: (generate a strong password)
   - Region: Choose closest to your users
5. Click "Create new project"

## Step 2: Get Your Project Credentials

1. Go to Settings > API in your Supabase dashboard
2. Copy the following values:
   - **Project URL** (starts with `https://`)
   - **Anon public key** (starts with `eyJ`)

## Step 3: Set Up Environment Variables

Create or update your `.env.local` file:

```bash
NEXT_PUBLIC_SUPABASE_URL=your_project_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
```

For Vercel deployment, add these same environment variables in your Vercel dashboard.

## Step 4: Run Database Migrations

Go to the SQL Editor in your Supabase dashboard and run the following SQL:

### Create Types and Tables

```sql
-- <PERSON>reate custom types
CREATE TYPE user_role AS ENUM ('admin', 'manager', 'staff');
CREATE TYPE transaction_type AS ENUM ('purchase', 'usage', 'waste', 'adjustment');
CREATE TYPE alert_type AS ENUM ('low_stock', 'expired', 'system');

-- Create users table (extends Supabase auth.users)
CREATE TABLE public.users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  username VARCHAR(50) UNIQUE,
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  role user_role DEFAULT 'staff',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create suppliers table
CREATE TABLE public.suppliers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  contact_person VARCHAR(255),
  email VARCHAR(255),
  phone VARCHAR(50),
  address TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create storage locations table
CREATE TABLE public.storage (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  temperature_controlled BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create inventory items table
CREATE TABLE public.inventory_items (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  vietnamese_name VARCHAR(255),
  category VARCHAR(100),
  quantity DECIMAL(10,3) DEFAULT 0,
  unit VARCHAR(50) NOT NULL,
  cost_per_unit DECIMAL(10,2) DEFAULT 0,
  min_threshold DECIMAL(10,3) DEFAULT 0,
  supplier_id UUID REFERENCES suppliers(id),
  storage_id UUID REFERENCES storage(id),
  last_restock_date DATE,
  expiry_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create items (menu items) table
CREATE TABLE public.items (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  vietnamese_name VARCHAR(255),
  description TEXT,
  category VARCHAR(100),
  preparation_time INTEGER, -- in minutes
  image_url TEXT,
  square_item_id VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create item variants table
CREATE TABLE public.item_variants (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  item_id UUID REFERENCES items(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  type VARCHAR(100), -- e.g., 'dine-in', 'takeaway'
  price DECIMAL(10,2) NOT NULL,
  square_variation_id VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create item ingredients junction table
CREATE TABLE public.item_ingredients (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  item_id UUID REFERENCES items(id) ON DELETE CASCADE,
  variant_id UUID REFERENCES item_variants(id) ON DELETE CASCADE,
  inventory_item_id UUID REFERENCES inventory_items(id) ON DELETE CASCADE,
  quantity_needed DECIMAL(10,3) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create transactions table
CREATE TABLE public.transactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  inventory_item_id UUID REFERENCES inventory_items(id) ON DELETE CASCADE,
  type transaction_type NOT NULL,
  quantity DECIMAL(10,3) NOT NULL,
  cost_per_unit DECIMAL(10,2),
  total_cost DECIMAL(10,2),
  notes TEXT,
  user_id UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create alerts table
CREATE TABLE public.alerts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  inventory_item_id UUID REFERENCES inventory_items(id) ON DELETE CASCADE,
  type alert_type NOT NULL,
  message TEXT NOT NULL,
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create productions table
CREATE TABLE public.productions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  item_id UUID REFERENCES items(id) ON DELETE CASCADE,
  variant_id UUID REFERENCES item_variants(id) ON DELETE CASCADE,
  quantity_produced INTEGER NOT NULL,
  user_id UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Enable Row Level Security

```sql
-- Enable Row Level Security
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.storage ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.inventory_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.item_variants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.item_ingredients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.productions ENABLE ROW LEVEL SECURITY;

-- Create policies (allow authenticated users to access all data for now)
CREATE POLICY "Allow authenticated users" ON public.users FOR ALL TO authenticated USING (true);
CREATE POLICY "Allow authenticated users" ON public.suppliers FOR ALL TO authenticated USING (true);
CREATE POLICY "Allow authenticated users" ON public.storage FOR ALL TO authenticated USING (true);
CREATE POLICY "Allow authenticated users" ON public.inventory_items FOR ALL TO authenticated USING (true);
CREATE POLICY "Allow authenticated users" ON public.items FOR ALL TO authenticated USING (true);
CREATE POLICY "Allow authenticated users" ON public.item_variants FOR ALL TO authenticated USING (true);
CREATE POLICY "Allow authenticated users" ON public.item_ingredients FOR ALL TO authenticated USING (true);
CREATE POLICY "Allow authenticated users" ON public.transactions FOR ALL TO authenticated USING (true);
CREATE POLICY "Allow authenticated users" ON public.alerts FOR ALL TO authenticated USING (true);
CREATE POLICY "Allow authenticated users" ON public.productions FOR ALL TO authenticated USING (true);
```

### Create Indexes and Triggers

```sql
-- Create indexes for better performance
CREATE INDEX idx_inventory_items_category ON inventory_items(category);
CREATE INDEX idx_inventory_items_supplier ON inventory_items(supplier_id);
CREATE INDEX idx_inventory_items_storage ON inventory_items(storage_id);
CREATE INDEX idx_transactions_item ON transactions(inventory_item_id);
CREATE INDEX idx_transactions_type ON transactions(type);
CREATE INDEX idx_transactions_date ON transactions(created_at);
CREATE INDEX idx_alerts_item ON alerts(inventory_item_id);
CREATE INDEX idx_alerts_unread ON alerts(is_read) WHERE is_read = false;
CREATE INDEX idx_item_variants_item ON item_variants(item_id);
CREATE INDEX idx_item_ingredients_item ON item_ingredients(item_id);
CREATE INDEX idx_item_ingredients_inventory ON item_ingredients(inventory_item_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_suppliers_updated_at BEFORE UPDATE ON suppliers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_storage_updated_at BEFORE UPDATE ON storage FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_inventory_items_updated_at BEFORE UPDATE ON inventory_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_items_updated_at BEFORE UPDATE ON items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_item_variants_updated_at BEFORE UPDATE ON item_variants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## Step 5: Create Your Admin User

1. Go to Authentication > Users in your Supabase dashboard
2. Click "Add user"
3. Enter:
   - Email: `<EMAIL>`
   - Password: `admin123`
   - Email Confirm: `true`
4. Click "Create user"

## Step 6: Add User Profile

After creating the auth user, run this SQL to add the profile:

```sql
-- Insert admin user profile (replace the UUID with your actual user ID from auth.users)
INSERT INTO public.users (id, username, first_name, last_name, role, is_active)
VALUES (
  (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
  'admin',
  'Admin',
  'User',
  'admin',
  true
);
```

## Step 7: Test Your Setup

1. Deploy your application to Vercel
2. Try logging in with `<EMAIL>` / `admin123`
3. Visit `/api/test-db` to verify the Supabase connection

## Next Steps

- Set up more restrictive Row Level Security policies based on user roles
- Add sample data for testing
- Configure email templates in Supabase Auth settings
- Set up database backups

Your Restaurant Inventory Management System is now ready to use with Supabase!
