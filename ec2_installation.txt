# Restaurant Inventory Management System - EC2 Installation Guide
# For Ubuntu 64-bit x86 Architecture

## 1. Initial Server Setup

# Connect to your EC2 instance
# Replace with your key and IP address
ssh -i your-key.pem ubuntu@your-ec2-public-ip

# Update the system
sudo apt update
sudo apt upgrade -y

# Install essential tools
sudo apt install -y git curl wget unzip htop

## 2. Set Up Git with SSH Key

# Generate SSH key (if you don't already have one)
ssh-keygen -t ed25519 -C "<EMAIL>"

# Start the SSH agent
eval "$(ssh-agent -s)"

# Add your SSH key to the agent
ssh-add ~/.ssh/id_ed25519

# Display your public key to add to GitHub
cat ~/.ssh/id_ed25519.pub

# Now add this key to your GitHub account:
# 1. Go to GitHub → Settings → SSH and GPG keys → New SSH key
# 2. Paste the key and give it a title (e.g., "EC2 Server")
# 3. Click "Add SSH key"

# Test your SSH connection
ssh -T **************

## 3. Install Docker and Docker Compose

# Install prerequisites
sudo apt install -y apt-transport-https ca-certificates curl software-properties-common

# Add Docker's official GPG key
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -

# Add Docker repository
sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"

# Update package database
sudo apt update

# Install Docker CE
sudo apt install -y docker-ce docker-ce-cli containerd.io

# Start and enable Docker
sudo systemctl start docker
sudo systemctl enable docker

# Add your user to the docker group to run Docker without sudo
sudo usermod -aG docker ubuntu

# IMPORTANT: You MUST log out and log back in for the group changes to take effect
# Without this step, you'll get "permission denied" errors when running docker commands
echo "Log out and log back in now for Docker permissions to take effect"
exit

# After reconnecting via SSH, verify Docker permissions
docker ps
# If you still get permission errors, try the following:
# sudo chmod 666 /var/run/docker.sock

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# Apply executable permissions
sudo chmod +x /usr/local/bin/docker-compose

# Verify installation
docker-compose --version

## 4. Clone and Configure the Application

# Clone the repository using SSH
<NAME_EMAIL>:cocobryan91/inventory-management.git
cd inventory-management

# Create .env file for production
cat > .env << EOL
MONGODB_URI=mongodb://mongodb:27017/restaurant-inventory
JWT_SECRET=$(openssl rand -hex 32)  # Generates a secure random secret
NODE_ENV=production
# Add your Square access token if you have one
SQUARE_ACCESS_TOKEN=your-square-production-token
EOL

# Create a custom mongod.conf file
cat > mongod.conf << EOL
storage:
  wiredTiger:
    engineConfig:
      cacheSizeGB: 0.25  # Limit cache to 250MB
systemLog:
  destination: file
  path: /var/log/mongodb/mongod.log
  logAppend: true
net:
  bindIp: 0.0.0.0
  port: 27017
processManagement:
  timeZoneInfo: /usr/share/zoneinfo
EOL

# Create docker-compose.mongodb.yaml for MongoDB
cat > docker-compose.mongodb.yaml << EOL
services:
  mongodb:
    build:
      context: .
      dockerfile: DockerfileMongodb
    container_name: restaurant-inventory-mongodb
    restart: always
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongod.conf:/etc/mongod.conf
    command: ["mongod", "--config", "/etc/mongod.conf"]
    environment:
      - MONGO_INITDB_DATABASE=restaurant-inventory
    networks:
      - mongodb-network

networks:
  mongodb-network:
    driver: bridge

volumes:
  mongodb_data:
    driver: local
EOL

# Create docker-compose.prod.yaml for production
cat > docker-compose.prod.yaml << EOL
# Note: The 'version' attribute is obsolete in newer Docker Compose versions and can be removed

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.prod
    restart: always
    ports:
      - "3000:3000"  # We'll use Nginx to proxy to this port
    environment:
      # Connect to external MongoDB (specify in .env or environment variables)
      - MONGODB_URI=\${MONGODB_URI:-mongodb://localhost:27017/restaurant-inventory}
      - MONGODB_DB_NAME=\${MONGODB_DB_NAME:-restaurant-inventory}
      - JWT_SECRET=\${JWT_SECRET:-defaultsecretkey}
      - SQUARE_ACCESS_TOKEN=\${SQUARE_ACCESS_TOKEN:-}
      - SQUARE_LOCATION_ID=\${SQUARE_LOCATION_ID:-}
      - NODE_ENV=production
EOL

## 5. Build and Start the Application

# For a setup with MongoDB on the same server:
# 1. Start MongoDB first
docker-compose -f docker-compose.mongodb.yaml up -d

# 2. Then start the application
docker-compose -f docker-compose.prod.yaml up -d

# For a setup with MongoDB on a separate server:
# 1. Make sure your .env file has the correct MONGODB_URI pointing to your MongoDB server
# 2. Start only the application
docker-compose -f docker-compose.prod.yaml up -d --build

# If you get permission errors, try running with sudo:
# sudo docker-compose -f docker-compose.mongodb.yaml up -d
# sudo docker-compose -f docker-compose.prod.yaml up -d --build
# OR fix permissions with: sudo chmod 666 /var/run/docker.sock

# Check if containers are running
docker ps

# Check logs if needed
docker-compose -f docker-compose.prod.yaml logs -f
# For MongoDB logs
docker-compose -f docker-compose.mongodb.yaml logs -f

## 6. Set Up Nginx as a Reverse Proxy

# Install Nginx
sudo apt install -y nginx

# Create Nginx configuration
sudo tee /etc/nginx/sites-available/restaurant-inventory > /dev/null << EOL
server {
    listen 80;
    server_name your-domain.com;  # Replace with your domain or EC2 public IP

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOL

# Enable the site
sudo ln -s /etc/nginx/sites-available/restaurant-inventory /etc/nginx/sites-enabled/

# Remove default site if needed
sudo rm /etc/nginx/sites-enabled/default

# Test Nginx configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx

## 7. Set Up SSL with Let's Encrypt (If You Have a Domain)

# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Get SSL certificate (replace with your domain)
sudo certbot --nginx -d your-domain.com

# Follow the prompts to complete the setup

## 8. Set Up Firewall

# Allow SSH, HTTP, and HTTPS
sudo ufw allow ssh
sudo ufw allow http
sudo ufw allow https

# Enable firewall
sudo ufw enable

## 9. Set Up MongoDB Backups

# Create a backup script
cat > backup_mongodb.sh << EOL
#!/bin/bash

# Set variables
TIMESTAMP=\$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="/home/<USER>/backups"

# Create backup directory if it doesn't exist
mkdir -p \$BACKUP_DIR

# Backup MongoDB
docker exec \$(docker ps -qf "name=mongodb") mongodump --out=/data/backup

# Copy backup from container to host
docker cp \$(docker ps -qf "name=mongodb"):/data/backup \$BACKUP_DIR/mongodb_\$TIMESTAMP

# Compress backup
tar -zcvf \$BACKUP_DIR/mongodb_\$TIMESTAMP.tar.gz \$BACKUP_DIR/mongodb_\$TIMESTAMP

# Remove uncompressed backup
rm -rf \$BACKUP_DIR/mongodb_\$TIMESTAMP

# Keep only the last 7 backups
ls -tp \$BACKUP_DIR/*.tar.gz | grep -v '/$' | tail -n +8 | xargs -I {} rm -- {}
EOLc

# Make the script executable
chmod +x backup_mongodb.sh

# Schedule daily backups at 2 AM
(crontab -l 2>/dev/null; echo "0 2 * * * /home/<USER>/inventory-management/restaurant-inventory/backup_mongodb.sh") | crontab -

## 10. Create a Deployment Script for Updates

# Create deployment script
cat > deploy.sh << EOL
#!/bin/bash

# Pull latest changes
git pull

# For a setup with MongoDB on the same server:
# Rebuild and restart MongoDB (only if there are changes to MongoDB configuration)
# docker-compose -f docker-compose.mongodb.yaml down
# docker-compose -f docker-compose.mongodb.yaml up -d --build

# Rebuild and restart the application
docker-compose -f docker-compose.prod.yaml down
docker-compose -f docker-compose.prod.yaml up -d --build

# Check if containers are running
docker ps
EOL

# Make the script executable
chmod +x deploy.sh

## 11. Set Up Log Rotation

# Configure Docker log rotation
sudo tee /etc/logrotate.d/docker-container-logs > /dev/null << EOL
/var/lib/docker/containers/*/*.log {
    rotate 7
    daily
    compress
    missingok
    delaycompress
    copytruncate
}
EOL

## 12. Verify the Deployment

# 1. Open your EC2 public IP or domain in a browser
# 2. Ensure the application loads correctly
# 3. Test key functionality

## 13. Troubleshooting Tips

# If the application doesn't start:
# Check container logs
# docker-compose logs app

# Check MongoDB logs
# docker-compose logs mongodb

# Check if containers are running
# docker ps

# Check Nginx logs
# sudo tail -f /var/log/nginx/error.log

# If you can't connect to the application:
# Check if the application is running on port 3000
# curl localhost:3000

# Check if Nginx is properly proxying
# sudo systemctl status nginx

# Check EC2 security groups in AWS console
# Ensure ports 22, 80, and 443 are open

## 14. Updating the Application

# When you need to update the application:
# cd inventory-management/restaurant-inventory
# git pull
# ./deploy.sh

## 15. Backup and Restore

# Manual backup:
# ./backup_mongodb.sh

# Restore from backup:
# Extract backup
# tar -zxvf /home/<USER>/backups/mongodb_YYYYMMDD_HHMMSS.tar.gz -C /tmp/

# Restore to MongoDB
# docker exec -it $(docker ps -qf "name=mongodb") mongorestore --drop /data/backup

## 16. Security Best Practices

# Keep your system updated:
# sudo apt update && sudo apt upgrade -y

# Monitor for suspicious activity:
# sudo tail -f /var/log/auth.log

# Set up automatic security updates:
sudo apt install -y unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades

# Check if swap exists
sudo swapon --show

# Create a 2GB swap file
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# Make swap permanent
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab

# Optimize swap settings
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
echo 'vm.vfs_cache_pressure=50' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
