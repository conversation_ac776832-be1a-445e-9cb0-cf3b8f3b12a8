{"objects": [{"type": "ITEM", "id": "PYFFMFSBCQ7WYL4RCOSQHVEH", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-07-19T03:33:35.812Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "Charcoal chicken with broken rice (Cơm gà nướng)", "description": "Indulge in the exquisite flavors of Cơm gà nướng, a Vietnamese delicacy featuring succulent grilled chicken served with fragrant broken rice. Elevate your dining experience with this flavorful and satisfying dish.", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "modifier_list_info": [{"modifier_list_id": "FCLRZEJVQ452DYC5P2OSHSNJ", "enabled": true, "ordinal": 1, "allow_quantities": "NOT_SET", "is_conversational": "NOT_SET", "hidden_from_customer_override": "NOT_SET"}, {"modifier_list_id": "6XXSIBYTMOLVKJXOVOMU2ZU5", "min_selected_modifiers": -1, "max_selected_modifiers": -1, "enabled": true, "hidden_from_customer": false, "ordinal": 0, "allow_quantities": "NOT_SET", "is_conversational": "NOT_SET", "hidden_from_customer_override": "NOT_SET"}], "variations": [{"type": "ITEM_VARIATION", "id": "ARS3TZD7Z3EG2LHDRK5BJ6GG", "updated_at": "2024-11-06T13:06:07.229Z", "created_at": "2024-07-19T03:33:35.812Z", "version": 1730898367229, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "PYFFMFSBCQ7WYL4RCOSQHVEH", "name": "Regular", "ordinal": 0, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1750, "currency": "AUD"}, "inventory_alert_type": "NONE", "price_description": "", "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_uri": "https://vietstreat.square.site/product/charcoal-chicken-with-broken-rice-c-m-g-n-ng-/3", "ecom_available": true, "ecom_visibility": "VISIBLE", "categories": [{"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251765453946880}, {"id": "WT6E6LJZ3P5WX6CLXGVX4INA", "ordinal": 2}, {"id": "CXZBESSOVBM6J4PPBGNCNUCJ", "ordinal": 2}], "description_html": "Indulge in the exquisite flavors of Cơm gà nướng, a Vietnamese delicacy featuring succulent grilled chicken served with fragrant broken rice. Elevate your dining experience with this flavorful and satisfying dish.", "description_plaintext": "Indulge in the exquisite flavors of Cơm gà nướng, a Vietnamese delicacy featuring succulent grilled chicken served with fragrant broken rice. Elevate your dining experience with this flavorful and satisfying dish.", "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251765453946880}}}, {"type": "ITEM", "id": "J6CQYYLKPK76TY45XR6FO2VS", "updated_at": "2025-05-11T12:51:34.746Z", "created_at": "2024-07-19T03:37:14.101Z", "version": 1746967894746, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "<PERSON>rc<PERSON><PERSON> (Cơm sư<PERSON> heo)", "description": "Grilled pork chop marinated in traditional Vietnamese spices, served with fragrant broken rice for a delightful combination of smoky and savory flavors.", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "modifier_list_info": [{"modifier_list_id": "FCLRZEJVQ452DYC5P2OSHSNJ", "min_selected_modifiers": -1, "max_selected_modifiers": -1, "enabled": true, "ordinal": 1, "allow_quantities": "NOT_SET", "is_conversational": "NOT_SET", "hidden_from_customer_override": "NOT_SET"}], "variations": [{"type": "ITEM_VARIATION", "id": "4P5YFVOOXILO62JOBQR67NVC", "updated_at": "2024-12-06T08:50:33.94Z", "created_at": "2024-07-19T03:37:14.101Z", "version": 1733475033940, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "J6CQYYLKPK76TY45XR6FO2VS", "name": "Regular", "ordinal": 0, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1750, "currency": "AUD"}, "location_overrides": [{"location_id": "LXRJ28YWPJNBZ", "track_inventory": false}], "track_inventory": false, "inventory_alert_type": "NONE", "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_image_uris": ["https://vietstreat.square.site/uploads/1/3/3/8/*********/s198382238489620829_p4_i1_w512.jpeg"], "ecom_available": false, "ecom_visibility": "UNAVAILABLE", "image_ids": ["32B7LYTNXGAUCS2U6ERF6BUL"], "categories": [{"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251748274077696}, {"id": "WT6E6LJZ3P5WX6CLXGVX4INA", "ordinal": 0}, {"id": "CXZBESSOVBM6J4PPBGNCNUCJ", "ordinal": 0}], "description_html": "Grilled pork chop marinated in traditional Vietnamese spices, served with fragrant broken rice for a delightful combination of smoky and savory flavors.", "description_plaintext": "Grilled pork chop marinated in traditional Vietnamese spices, served with fragrant broken rice for a delightful combination of smoky and savory flavors.", "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251748274077696}, "is_alcoholic": false}}, {"type": "ITEM", "id": "N5VOGSN62LZCM2G6A64XMECC", "updated_at": "2024-10-29T09:15:21.071Z", "created_at": "2024-07-19T03:39:35.306Z", "version": 1730193321071, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "Charcoal pork chop Combo (<PERSON><PERSON><PERSON> tấm bì sườn chả trứng)", "description": "Experience the authentic Vietnamese flavors with our Cơm Tấm Bì Sườn <PERSON>, a delectable pork chop combo that will tantalize your taste buds. Indulge in this traditional dish and savor every bite of its savory goodness.", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "modifier_list_info": [{"modifier_list_id": "FCLRZEJVQ452DYC5P2OSHSNJ", "min_selected_modifiers": -1, "max_selected_modifiers": -1, "enabled": true, "hidden_from_customer": false, "ordinal": 1, "allow_quantities": "NOT_SET", "is_conversational": "NOT_SET", "hidden_from_customer_override": "NOT_SET"}], "variations": [{"type": "ITEM_VARIATION", "id": "F6EYIBORYDYR5XKOQ4PHYOG6", "updated_at": "2024-07-19T03:49:33.596Z", "created_at": "2024-07-19T03:39:35.306Z", "version": 1721360973596, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "N5VOGSN62LZCM2G6A64XMECC", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 2250, "currency": "AUD"}, "location_overrides": [{"location_id": "LXRJ28YWPJNBZ", "track_inventory": false}], "track_inventory": false, "inventory_alert_type": "NONE", "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_uri": "https://vietstreat.square.site/product/charcoal-pork-chop-combo-c-m-t-m-b-s-n-ch-tr-ng-/5", "ecom_available": true, "ecom_visibility": "VISIBLE", "categories": [{"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251739684143104}], "description_html": "Experience the authentic Vietnamese flavors with our Cơm Tấm Bì Sườn <PERSON>, a delectable pork chop combo that will tantalize your taste buds. Indulge in this traditional dish and savor every bite of its savory goodness.", "description_plaintext": "Experience the authentic Vietnamese flavors with our Cơm Tấm Bì Sườn <PERSON>, a delectable pork chop combo that will tantalize your taste buds. Indulge in this traditional dish and savor every bite of its savory goodness.", "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251739684143104}}}, {"type": "ITEM", "id": "6LZDTSPYOHYXZFNFWQ6ACZNS", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-07-19T03:44:40.91Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "<PERSON><PERSON> (Bún chả Hà Nội)", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "modifier_list_info": [{"modifier_list_id": "WJOQWJPR7I7AW3WSGDSVYMFL", "min_selected_modifiers": -1, "max_selected_modifiers": -1, "enabled": true, "hidden_from_customer": false, "ordinal": 1, "allow_quantities": "NOT_SET", "is_conversational": "NOT_SET", "hidden_from_customer_override": "NOT_SET"}], "variations": [{"type": "ITEM_VARIATION", "id": "XN26JPNMEXTCJJH6O7P4D6F5", "updated_at": "2024-07-19T03:49:33.596Z", "created_at": "2024-07-19T03:44:40.91Z", "version": 1721360973596, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "6LZDTSPYOHYXZFNFWQ6ACZNS", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1850, "currency": "AUD"}, "inventory_alert_type": "NONE", "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_uri": "https://vietstreat.square.site/product/h-n-i-vermicelli-b-n-ch-h-n-i-/6", "ecom_image_uris": ["https://vietstreat.square.site/uploads/1/3/3/8/*********/s198382238489620829_p6_i2_w3115.jpeg"], "ecom_available": true, "ecom_visibility": "VISIBLE", "image_ids": ["CATKN2QLW7DI7A76GCGWDOY4"], "categories": [{"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251799813685248}, {"id": "WT6E6LJZ3P5WX6CLXGVX4INA", "ordinal": 7}, {"id": "CXZBESSOVBM6J4PPBGNCNUCJ", "ordinal": 7}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251799813685248}}}, {"type": "ITEM", "id": "NQ46EZ3PCL6UZC5X5HW7K7BJ", "updated_at": "2024-11-01T09:13:47.526Z", "created_at": "2024-07-19T03:50:15.139Z", "version": 1730452427526, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "Phở gà (Chicken Pho)", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "modifier_list_info": [{"modifier_list_id": "P3SOSO4KCJE3JDXCT6CYDEMU", "min_selected_modifiers": -1, "max_selected_modifiers": -1, "enabled": true, "hidden_from_customer": false, "ordinal": 1, "allow_quantities": "NOT_SET", "is_conversational": "NOT_SET", "hidden_from_customer_override": "NOT_SET"}], "variations": [{"type": "ITEM_VARIATION", "id": "MFJQ4PZR66BJYNMGJJPD2A5U", "updated_at": "2024-07-19T04:08:29.718Z", "created_at": "2024-07-19T03:50:15.139Z", "version": 1721362109718, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "NQ46EZ3PCL6UZC5X5HW7K7BJ", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1750, "currency": "AUD"}, "inventory_alert_type": "NONE", "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_available": false, "ecom_visibility": "UNAVAILABLE", "categories": [{"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251688144535552}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": true, "reporting_category": {"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251688144535552}}}, {"type": "ITEM", "id": "4PJUWHJRNCPJOY2K6RX6DUPD", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-07-19T03:50:36.676Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "Phở bò (<PERSON><PERSON>)", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "G36Z2AAEAGQFM3NPMBQGCBQN", "updated_at": "2024-12-23T07:29:27.451Z", "created_at": "2024-07-19T03:50:36.676Z", "version": 1734938967451, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "4PJUWHJRNCPJOY2K6RX6DUPD", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1850, "currency": "AUD"}, "location_overrides": [{"location_id": "LXRJ28YWPJNBZ", "sold_out": true}], "inventory_alert_type": "NONE", "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_uri": "https://vietstreat.square.site/product/ph-b-beef-pho-/7", "ecom_image_uris": ["https://vietstreat.square.site/uploads/1/3/3/8/*********/s198382238489620829_p7_i1_w512.jpeg"], "ecom_available": true, "ecom_visibility": "VISIBLE", "image_ids": ["4NK4EYWTUTKLAJK6JVMQSQRN"], "categories": [{"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251705324404736}, {"id": "WT6E6LJZ3P5WX6CLXGVX4INA", "ordinal": 4}, {"id": "CXZBESSOVBM6J4PPBGNCNUCJ", "ordinal": 4}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251705324404736}, "is_alcoholic": false}}, {"type": "ITEM", "id": "RZW5LDZNAXA4YEBSXURVUZVA", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-07-19T03:51:44.746Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "Combination Pho (Phờ đặc biệt)", "description": "A savory broth simmered with aromatic spices, tender slices of beef, and silky rice noodles, creating a harmonious bowl of traditional Vietnamese pho.", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "modifier_list_info": [{"modifier_list_id": "JH32VO44Q65MTSI3P7R3ATEO", "min_selected_modifiers": -1, "max_selected_modifiers": -1, "enabled": true, "hidden_from_customer": false, "ordinal": 1, "allow_quantities": "NOT_SET", "is_conversational": "NOT_SET", "hidden_from_customer_override": "NOT_SET"}], "variations": [{"type": "ITEM_VARIATION", "id": "TBRDSRUFSXJ6I7Y634ZYB4DP", "updated_at": "2024-12-23T07:29:11.07Z", "created_at": "2024-07-19T03:51:44.746Z", "version": 1734938951070, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "RZW5LDZNAXA4YEBSXURVUZVA", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1950, "currency": "AUD"}, "location_overrides": [{"location_id": "LXRJ28YWPJNBZ", "sold_out": true}], "inventory_alert_type": "NONE", "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_uri": "https://vietstreat.square.site/product/combination-pho-ph-c-bi-t-/9", "ecom_image_uris": ["https://vietstreat.square.site/uploads/1/3/3/8/*********/s198382238489620829_p9_i1_w512.jpeg"], "ecom_available": true, "ecom_visibility": "VISIBLE", "image_ids": ["RNWQQYKMUVOJ4PSZCB2GWRLK"], "categories": [{"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251679554600960}, {"id": "WT6E6LJZ3P5WX6CLXGVX4INA", "ordinal": 6}, {"id": "CXZBESSOVBM6J4PPBGNCNUCJ", "ordinal": 6}], "description_html": "A savory broth simmered with aromatic spices, tender slices of beef, and silky rice noodles, creating a harmonious bowl of traditional Vietnamese pho.", "description_plaintext": "A savory broth simmered with aromatic spices, tender slices of beef, and silky rice noodles, creating a harmonious bowl of traditional Vietnamese pho.", "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251679554600960}, "is_alcoholic": false}}, {"type": "ITEM", "id": "3RNSVVZLVEZPN33AVIJRI7A3", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-07-19T04:03:35.105Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "5 pcs Pork Spring Rolls (<PERSON><PERSON> giò heo)", "description": "Indulge in the crispy and savory goodness of our 5-piece Chả giò heo (Pork Spring Rolls). These delectable rolls are the perfect combination of flavorful pork and crunchy perfection, sure to elevate any meal with a touch of Vietnamese culinary excellence.", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "DC4BFSABXOBNVJVWI2ETCSLI", "updated_at": "2024-07-19T04:08:14.391Z", "created_at": "2024-07-19T04:03:35.105Z", "version": 1721362094391, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "3RNSVVZLVEZPN33AVIJRI7A3", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1050, "currency": "AUD"}, "inventory_alert_type": "NONE", "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/5-pcs-pork-spring-rolls-ch-gi-heo-/10", "ecom_image_uris": ["https://vietstreat.square.site/uploads/1/3/3/8/*********/s198382238489620829_p10_i1_w512.jpeg"], "ecom_available": true, "ecom_visibility": "VISIBLE", "image_ids": ["QQC7TTX6JEUPG5UIZ2LT4A5U"], "categories": [{"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251765453946880}, {"id": "FVVQMWJET6HWNJN5XV2J6BXD", "ordinal": 1}, {"id": "G25FQBHDWHA2BGICH2ULMCON", "ordinal": 1}], "description_html": "Indulge in the crispy and savory goodness of our 5-piece Chả giò heo (Pork Spring Rolls). These delectable rolls are the perfect combination of flavorful pork and crunchy perfection, sure to elevate any meal with a touch of Vietnamese culinary excellence.", "description_plaintext": "Indulge in the crispy and savory goodness of our 5-piece Chả giò heo (Pork Spring Rolls). These delectable rolls are the perfect combination of flavorful pork and crunchy perfection, sure to elevate any meal with a touch of Vietnamese culinary excellence.", "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251765453946880}}}, {"type": "ITEM", "id": "7QFI76CGP5H6UMBFTZWOHWEY", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-07-19T04:06:30.098Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "4 pcs Prawn Spring Rolls (Chả giò tôm)", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "N7NLNPFHF5ZLSD3KQVWFKIVY", "updated_at": "2024-07-19T04:08:14.391Z", "created_at": "2024-07-19T04:06:30.098Z", "version": 1721362094391, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "7QFI76CGP5H6UMBFTZWOHWEY", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1050, "currency": "AUD"}, "track_inventory": false, "inventory_alert_type": "NONE", "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/4-pcs-prawn-spring-rolls-ch-gi-t-m-/11", "ecom_image_uris": ["https://vietstreat.square.site/uploads/1/3/3/8/*********/s198382238489620829_p11_i2_w7008.jpeg"], "ecom_available": true, "ecom_visibility": "VISIBLE", "image_ids": ["RWJA5RRVTOYC6TSTMWNW5BLB"], "categories": [{"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251748274077696}, {"id": "FVVQMWJET6HWNJN5XV2J6BXD", "ordinal": 0}, {"id": "G25FQBHDWHA2BGICH2ULMCON", "ordinal": 0}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251748274077696}}}, {"type": "ITEM", "id": "KGS553B5YCCX2UBIBONGEAH4", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-07-19T04:07:23.299Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "6 pcs chicken spare ribs (Sườn gà rang muối)", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "CX7MFLPF3LX5WOU6PUYHRG7T", "updated_at": "2024-07-19T04:08:14.391Z", "created_at": "2024-07-19T04:07:23.299Z", "version": 1721362094391, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "KGS553B5YCCX2UBIBONGEAH4", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1450, "currency": "AUD"}, "track_inventory": false, "inventory_alert_type": "NONE", "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/6-pcs-chicken-spare-ribs-s-n-g-rang-mu-i-/12", "ecom_image_uris": ["https://vietstreat.square.site/uploads/1/3/3/8/*********/s198382238489620829_p12_i1_w512.jpeg"], "ecom_available": true, "ecom_visibility": "VISIBLE", "image_ids": ["RS7X2TWQNXLBFKJXPV63N2PB"], "categories": [{"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251799813685248}, {"id": "FVVQMWJET6HWNJN5XV2J6BXD", "ordinal": 2}, {"id": "G25FQBHDWHA2BGICH2ULMCON", "ordinal": 2}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251799813685248}}}, {"type": "ITEM", "id": "POVUODSKGYQHCF6X4RVVRT2C", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-10-19T11:29:19.159Z", "version": 1743632309931, "is_deleted": false, "catalog_v1_ids": [{"catalog_v1_id": "5278aec6-f388-4b33-8e58-408ebb1b4411", "location_id": "LXRJ28YWPJNBZ"}], "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_data": {"name": "Coke", "abbreviation": "co", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "UXFBOISXB5RDGIXJAHVD3WIW", "updated_at": "2024-10-19T11:29:19.095Z", "created_at": "2024-10-19T11:29:19.159Z", "version": 1729337359095, "is_deleted": false, "catalog_v1_ids": [{"catalog_v1_id": "6fafed43-795a-4937-91a1-2db9a1312a75", "location_id": "LXRJ28YWPJNBZ"}], "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_variation_data": {"item_id": "POVUODSKGYQHCF6X4RVVRT2C", "name": "Regular", "ordinal": 0, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 350, "currency": "AUD"}, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_uri": "https://vietstreat.square.site/product/coke/14", "ecom_image_uris": ["https://vietstreat.square.site/uploads/1/3/3/8/*********/s198382238489620829_p14_i1_w2000.png"], "ecom_available": true, "ecom_visibility": "VISIBLE", "image_ids": ["SLSYY2RWABFT32BF73JJS3CN"], "categories": [{"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2251731094208512}, {"id": "U7OZVYPXN74GW6DZOOWDK2YV", "ordinal": 0}, {"id": "WKDJ42JBEBOXMI4GVULK3OUN", "ordinal": 0}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2251731094208512}}}, {"type": "ITEM", "id": "KRJ5LBSJXH62YSDKJHLAUXNI", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-10-19T12:05:53.962Z", "version": 1743632309931, "is_deleted": false, "catalog_v1_ids": [{"catalog_v1_id": "0649dbe4-d6bc-4bd6-8a6b-65387514b8a4", "location_id": "LXRJ28YWPJNBZ"}], "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_data": {"name": "Vietnamese Iced coffee", "abbreviation": "Vi", "label_color": "9DA2A6", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "7MUYYZTBFTUZNR3WQGD37PVU", "updated_at": "2024-12-23T07:29:53.265Z", "created_at": "2024-10-19T12:05:53.962Z", "version": *********3265, "is_deleted": false, "catalog_v1_ids": [{"catalog_v1_id": "0fa466a8-71c7-440c-8c75-1c0d66b11f89", "location_id": "LXRJ28YWPJNBZ"}], "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_variation_data": {"item_id": "KRJ5LBSJXH62YSDKJHLAUXNI", "name": "Small", "ordinal": 0, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 750, "currency": "AUD"}, "location_overrides": [{"location_id": "LXRJ28YWPJNBZ", "track_inventory": false}], "track_inventory": false, "price_description": "", "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}, {"type": "ITEM_VARIATION", "id": "MDROG7DURXJFJUKJYMTLE5ZE", "updated_at": "2024-12-23T07:29:53.265Z", "created_at": "2024-10-19T12:05:53.962Z", "version": *********3265, "is_deleted": false, "catalog_v1_ids": [{"catalog_v1_id": "#c4f8b6ad-ec04-4b04-80d0-cc4120c6b14d", "location_id": "LXRJ28YWPJNBZ"}], "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_variation_data": {"item_id": "KRJ5LBSJXH62YSDKJHLAUXNI", "name": "Large", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 850, "currency": "AUD"}, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_available": false, "ecom_visibility": "UNAVAILABLE", "categories": [{"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2251662374731776}, {"id": "U7OZVYPXN74GW6DZOOWDK2YV", "ordinal": 7}, {"id": "WKDJ42JBEBOXMI4GVULK3OUN", "ordinal": 7}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2251662374731776}}}, {"type": "ITEM", "id": "FHA32WAIQY2AKDSJ4FBJMG2G", "updated_at": "2024-12-12T09:18:59.588Z", "created_at": "2024-10-21T02:43:04.198Z", "version": 1733995139588, "is_deleted": false, "catalog_v1_ids": [{"catalog_v1_id": "b3163c5b-a197-4557-9028-22a3af68b837", "location_id": "LXRJ28YWPJNBZ"}], "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_data": {"name": "Peach Ice Tea", "abbreviation": "Pe", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "Q6JBTBIBG7NSSMRH7FGFKSSP", "updated_at": "2024-10-21T02:43:04.119Z", "created_at": "2024-10-21T02:43:04.198Z", "version": 1729478584119, "is_deleted": false, "catalog_v1_ids": [{"catalog_v1_id": "3f9c473e-3e82-4acb-adf5-8327532134b0", "location_id": "LXRJ28YWPJNBZ"}], "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_variation_data": {"item_id": "FHA32WAIQY2AKDSJ4FBJMG2G", "name": "Regular", "ordinal": 0, "pricing_type": "VARIABLE_PRICING", "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_available": false, "ecom_visibility": "UNAVAILABLE", "categories": [{"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2251593655255040}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": true, "reporting_category": {"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2251593655255040}}}, {"type": "ITEM", "id": "43CN5I4STUFXOZ3WHBTPLALG", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-10-23T07:53:45.887Z", "version": 1743632309931, "is_deleted": false, "catalog_v1_ids": [{"catalog_v1_id": "f77ef133-7541-47ed-b47b-597009516585", "location_id": "LXRJ28YWPJNBZ"}], "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_data": {"name": "Peach Iced Tea Orange & Lemongrass (Trà đào cam sả)", "abbreviation": "Pe", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "PSAKBBLMUNPW44D3X25XEB4D", "updated_at": "2024-10-24T05:57:40.535Z", "created_at": "2024-10-23T07:53:45.887Z", "version": 1729749460535, "is_deleted": false, "catalog_v1_ids": [{"catalog_v1_id": "eea4e441-7ddd-4930-a4d9-ea1b42529993", "location_id": "LXRJ28YWPJNBZ"}], "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_variation_data": {"item_id": "43CN5I4STUFXOZ3WHBTPLALG", "name": "Small", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 750, "currency": "AUD"}, "track_inventory": false, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}, {"type": "ITEM_VARIATION", "id": "C4XTLDOHSNQHGFDYW66NFDTI", "updated_at": "2024-10-24T05:59:13.15Z", "created_at": "2024-10-23T07:53:45.887Z", "version": 1729749553150, "is_deleted": false, "catalog_v1_ids": [{"catalog_v1_id": "#b681c2f1-9a51-4ade-bd23-d36370cfb3c5", "location_id": "LXRJ28YWPJNBZ"}], "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_variation_data": {"item_id": "43CN5I4STUFXOZ3WHBTPLALG", "name": "Large", "ordinal": 2, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 850, "currency": "AUD"}, "track_inventory": false, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_uri": "https://vietstreat.square.site/product/peach-iced-tea-orange-lemongrass-tr-o-cam-s-/17", "ecom_image_uris": ["https://vietstreat.square.site/uploads/1/3/3/8/*********/s198382238489620829_p17_i1_w3115.jpeg"], "ecom_available": true, "ecom_visibility": "VISIBLE", "image_ids": ["CNV3QY4OWMW6XKTOCBVHBCX2"], "categories": [{"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2251524935778304}, {"id": "U7OZVYPXN74GW6DZOOWDK2YV", "ordinal": 4}, {"id": "WKDJ42JBEBOXMI4GVULK3OUN", "ordinal": 4}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2251524935778304}}}, {"type": "ITEM", "id": "UUTAA5F2JCIS54T6U5A352M4", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-10-24T05:49:43.43Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "<PERSON><PERSON><PERSON><PERSON> with chicken (Bún gà)", "description": "Tender vermicelli noodles topped with marinated chicken seasoned with aromatic lemongrass. This dish combines the delicate texture of the noodles with the savory, citrusy notes of lemongrass-infused chicken.", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "modifier_list_info": [{"modifier_list_id": "P3SOSO4KCJE3JDXCT6CYDEMU", "min_selected_modifiers": -1, "max_selected_modifiers": -1, "enabled": true, "hidden_from_customer": false, "ordinal": 1, "allow_quantities": "NOT_SET", "is_conversational": "NOT_SET", "hidden_from_customer_override": "NOT_SET"}], "variations": [{"type": "ITEM_VARIATION", "id": "NFZTLUIUVJGPJC2KQVY37D7A", "updated_at": "2024-10-24T05:49:43.844Z", "created_at": "2024-10-24T05:49:43.43Z", "version": 1729748983844, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "UUTAA5F2JCIS54T6U5A352M4", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1750, "currency": "AUD"}, "track_inventory": false, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/vermicelli-with-chicken-b-n-g-/18", "ecom_available": true, "ecom_visibility": "VISIBLE", "categories": [{"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251542115647488}, {"id": "WT6E6LJZ3P5WX6CLXGVX4INA", "ordinal": 8}, {"id": "CXZBESSOVBM6J4PPBGNCNUCJ", "ordinal": 8}], "description_html": "Tender vermicelli noodles topped with marinated chicken seasoned with aromatic lemongrass. This dish combines the delicate texture of the noodles with the savory, citrusy notes of lemongrass-infused chicken.", "description_plaintext": "Tender vermicelli noodles topped with marinated chicken seasoned with aromatic lemongrass. This dish combines the delicate texture of the noodles with the savory, citrusy notes of lemongrass-infused chicken.", "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251542115647488}}}, {"type": "ITEM", "id": "ABCRHRBGZKEXMWCD4X4ICCQO", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-10-24T05:51:12.407Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "<PERSON><PERSON><PERSON><PERSON> with Spring rolls (<PERSON><PERSON> chả chả giò)", "description": "<PERSON><PERSON><PERSON><PERSON> with Spring rolls (<PERSON><PERSON> chả chả giò)", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "modifier_list_info": [{"modifier_list_id": "P3SOSO4KCJE3JDXCT6CYDEMU", "min_selected_modifiers": -1, "max_selected_modifiers": -1, "enabled": true, "hidden_from_customer": false, "ordinal": 1, "allow_quantities": "NOT_SET", "is_conversational": "NOT_SET", "hidden_from_customer_override": "NOT_SET"}, {"modifier_list_id": "2PEO526FN2TTSAOVHXL4CWCO", "min_selected_modifiers": -1, "max_selected_modifiers": -1, "enabled": true, "hidden_from_customer": false, "ordinal": 0, "allow_quantities": "NOT_SET", "is_conversational": "NOT_SET", "hidden_from_customer_override": "NOT_SET"}], "variations": [{"type": "ITEM_VARIATION", "id": "7FUO4AKVNNI6QYY5TPQ56DGO", "updated_at": "2024-10-24T05:51:38.389Z", "created_at": "2024-10-24T05:51:12.407Z", "version": 1729749098389, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "ABCRHRBGZKEXMWCD4X4ICCQO", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1850, "currency": "AUD"}, "track_inventory": false, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/vermicelli-with-spring-rolls-b-n-ch-ch-gi-/19", "ecom_available": true, "ecom_visibility": "VISIBLE", "categories": [{"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251473396170752}, {"id": "WT6E6LJZ3P5WX6CLXGVX4INA", "ordinal": 9}, {"id": "CXZBESSOVBM6J4PPBGNCNUCJ", "ordinal": 9}], "description_html": "<p><PERSON><PERSON><PERSON><PERSON> with Spring rolls (Bún chả chả giò)</p>", "description_plaintext": "<PERSON><PERSON><PERSON><PERSON> with Spring rolls (<PERSON><PERSON> chả chả giò)", "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251473396170752}}}, {"type": "ITEM", "id": "LNEJJZMFB4BQ57ZSVVP363OT", "updated_at": "2024-11-01T10:14:39.402Z", "created_at": "2024-10-24T05:53:52.21Z", "version": 1730456079402, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "Chicken & Beef <PERSON>o (Phở bò, gà)", "description": "A hearty bowl of rice noodles, tender slices of chicken and beef, immersed in a fragrant broth seasoned with star anise, cloves, and cinnamon, garnished with fresh herbs and bean sprouts.", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "modifier_list_info": [{"modifier_list_id": "JH32VO44Q65MTSI3P7R3ATEO", "min_selected_modifiers": -1, "max_selected_modifiers": -1, "enabled": true, "hidden_from_customer": false, "ordinal": 0, "allow_quantities": "NOT_SET", "is_conversational": "NOT_SET", "hidden_from_customer_override": "NOT_SET"}], "variations": [{"type": "ITEM_VARIATION", "id": "YI6MV466TN4GRSWLRRYYKGBL", "updated_at": "2024-10-24T05:53:52.614Z", "created_at": "2024-10-24T05:53:52.21Z", "version": 1729749232614, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "LNEJJZMFB4BQ57ZSVVP363OT", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1750, "currency": "AUD"}, "track_inventory": false, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_image_uris": ["https://vietstreat.square.site/uploads/1/3/3/8/*********/s198382238489620829_p20_i1_w512.jpeg"], "ecom_available": false, "ecom_visibility": "UNAVAILABLE", "image_ids": ["62E6UJMZB3MZ2L6BMNLWUKT2"], "categories": [{"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251610835124224}], "description_html": "A hearty bowl of rice noodles, tender slices of chicken and beef, immersed in a fragrant broth seasoned with star anise, cloves, and cinnamon, garnished with fresh herbs and bean sprouts.", "description_plaintext": "A hearty bowl of rice noodles, tender slices of chicken and beef, immersed in a fragrant broth seasoned with star anise, cloves, and cinnamon, garnished with fresh herbs and bean sprouts.", "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": true, "reporting_category": {"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251610835124224}}}, {"type": "ITEM", "id": "LXN4Q5SOK5BGLLFMP5TS67FV", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-10-24T06:01:04.91Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "Water", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "4GTTSUDUKSDV5PGT2MXKKKPJ", "updated_at": "2024-10-24T06:01:05.357Z", "created_at": "2024-10-24T06:01:04.91Z", "version": 1729749665357, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "LXN4Q5SOK5BGLLFMP5TS67FV", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 350, "currency": "AUD"}, "track_inventory": false, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/water/21", "ecom_image_uris": ["https://vietstreat.square.site/uploads/1/3/3/8/*********/s198382238489620829_p21_i1_w2000.png"], "ecom_available": true, "ecom_visibility": "VISIBLE", "image_ids": ["QC6G5V6GGEXY3TRGYP2ADACK"], "categories": [{"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2251456216301568}, {"id": "U7OZVYPXN74GW6DZOOWDK2YV", "ordinal": 1}, {"id": "WKDJ42JBEBOXMI4GVULK3OUN", "ordinal": 1}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2251456216301568}}}, {"type": "ITEM", "id": "ZHKGYYR6HATU7GEWK6BKW6SQ", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-10-24T06:01:40.604Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "Fanta", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "EALERY5OEWKDJYWEEGTKG6K7", "updated_at": "2024-10-24T06:02:07.254Z", "created_at": "2024-10-24T06:01:40.604Z", "version": 1729749727254, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "ZHKGYYR6HATU7GEWK6BKW6SQ", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 350, "currency": "AUD"}, "track_inventory": false, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/fanta/22", "ecom_image_uris": ["https://vietstreat.square.site/uploads/1/3/3/8/*********/s198382238489620829_p22_i1_w2000.png"], "ecom_available": true, "ecom_visibility": "VISIBLE", "image_ids": ["HZSI47OKD2KI6RAMQRWF2MD2"], "categories": [{"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2251387496824832}, {"id": "U7OZVYPXN74GW6DZOOWDK2YV", "ordinal": 2}, {"id": "WKDJ42JBEBOXMI4GVULK3OUN", "ordinal": 2}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2251387496824832}}}, {"type": "ITEM", "id": "TWRULZ3R5Y7YNFACTSVQRUF5", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-10-24T06:24:51.701Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_data": {"name": "Mountain Dew", "abbreviation": "co", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "A4KVG5EPV72AY35CQFA6MGWQ", "updated_at": "2024-10-24T06:25:23.105Z", "created_at": "2024-10-24T06:24:51.701Z", "version": 1729751123105, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_variation_data": {"item_id": "TWRULZ3R5Y7YNFACTSVQRUF5", "name": "Regular", "ordinal": 0, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 350, "currency": "AUD"}, "location_overrides": [{"location_id": "LXRJ28YWPJNBZ", "track_inventory": false}], "track_inventory": false, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_uri": "https://vietstreat.square.site/product/mountain-dew/24", "ecom_image_uris": ["https://vietstreat.square.site/uploads/1/3/3/8/*********/s198382238489620829_p24_i1_w500.jpeg"], "ecom_available": true, "ecom_visibility": "VISIBLE", "image_ids": ["4RZ4SSWIAETUGZS5OJYG36FF"], "categories": [{"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2251250057871360}, {"id": "U7OZVYPXN74GW6DZOOWDK2YV", "ordinal": 6}, {"id": "WKDJ42JBEBOXMI4GVULK3OUN", "ordinal": 6}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2251250057871360}}}, {"type": "ITEM", "id": "TB6EWP2V5QS7DSLGGV4BC52D", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-10-24T06:26:03.334Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_data": {"name": "Sprite", "abbreviation": "co", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "J27CWFGZWIZU5IHTQHZCESC2", "updated_at": "2024-10-24T06:26:26.692Z", "created_at": "2024-10-24T06:26:03.334Z", "version": 1729751186692, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_variation_data": {"item_id": "TB6EWP2V5QS7DSLGGV4BC52D", "name": "Regular", "ordinal": 0, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 350, "currency": "AUD"}, "location_overrides": [{"location_id": "LXRJ28YWPJNBZ", "track_inventory": false}], "track_inventory": false, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_uri": "https://vietstreat.square.site/product/sprite/25", "ecom_available": true, "ecom_visibility": "VISIBLE", "categories": [{"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2251181338394624}, {"id": "U7OZVYPXN74GW6DZOOWDK2YV", "ordinal": 5}, {"id": "WKDJ42JBEBOXMI4GVULK3OUN", "ordinal": 5}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2251181338394624}}}, {"type": "ITEM", "id": "V2HT7765LSX7EFXHDBC3B4BJ", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_data": {"name": "Charcoal Pork Chop Combo", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "modifier_list_info": [{"modifier_list_id": "WQPY2U5LPKX5FLF24YJOY5SE", "min_selected_modifiers": 0, "max_selected_modifiers": 2, "enabled": true, "ordinal": 0, "allow_quantities": "NOT_SET", "is_conversational": "NOT_SET", "hidden_from_customer_override": "NOT_SET"}], "variations": [{"type": "ITEM_VARIATION", "id": "CPD7YUKPSBQCTRDYBLAV7EV4", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_variation_data": {"item_id": "V2HT7765LSX7EFXHDBC3B4BJ", "name": "Regular", "ordinal": 0, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 2250, "currency": "AUD"}, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_uri": "https://vietstreat.square.site/product/charcoal-pork-chop-combo/27", "ecom_image_uris": ["https://vietstreat.square.site/uploads/1/3/3/8/*********/s198382238489620829_p27_i1_w4134.jpeg"], "ecom_available": true, "ecom_visibility": "VISIBLE", "image_ids": ["DXBMUR6CF3E5OJJELJNFY7CI"], "categories": [{"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251404676694016}, {"id": "WT6E6LJZ3P5WX6CLXGVX4INA", "ordinal": 1}, {"id": "CXZBESSOVBM6J4PPBGNCNUCJ", "ordinal": 1}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251404676694016}}}, {"type": "ITEM", "id": "DANNDGCZ45R43QNQ6XPAMWXA", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_data": {"name": "Chicken Phở", "description": "Tender chicken simmered in a fragrant broth of aromatic spices, served with silky rice noodles, and garnished with fresh herbs and lime.", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "modifier_list_info": [{"modifier_list_id": "ILDHC5LZJGOZTVRUINAOE2LH", "min_selected_modifiers": 0, "max_selected_modifiers": 1, "enabled": true, "ordinal": 0, "allow_quantities": "NOT_SET", "is_conversational": "NOT_SET", "hidden_from_customer_override": "NOT_SET"}], "variations": [{"type": "ITEM_VARIATION", "id": "AW6NCTJ3D4VS6B3RPYWWYSFD", "updated_at": "2024-12-23T07:29:00.225Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1734938940225, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_variation_data": {"item_id": "DANNDGCZ45R43QNQ6XPAMWXA", "name": "Regular", "ordinal": 0, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1750, "currency": "AUD"}, "location_overrides": [{"location_id": "LXRJ28YWPJNBZ", "sold_out": true}], "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_uri": "https://vietstreat.square.site/product/chicken-ph-/32", "ecom_image_uris": ["https://vietstreat.square.site/uploads/1/3/3/8/*********/s198382238489620829_p32_i2_w6069.jpeg"], "ecom_available": true, "ecom_visibility": "VISIBLE", "image_ids": ["T5YFX27RAUTVV3HCU6F4IASF"], "categories": [{"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251267237740544}, {"id": "WT6E6LJZ3P5WX6CLXGVX4INA", "ordinal": 3}, {"id": "CXZBESSOVBM6J4PPBGNCNUCJ", "ordinal": 3}], "description_html": "Tender chicken simmered in a fragrant broth of aromatic spices, served with silky rice noodles, and garnished with fresh herbs and lime.", "description_plaintext": "Tender chicken simmered in a fragrant broth of aromatic spices, served with silky rice noodles, and garnished with fresh herbs and lime.", "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251267237740544}, "is_alcoholic": false}}, {"type": "ITEM", "id": "O5TY25XFROGUKJF4MPJDFRO7", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_data": {"name": "Chicken & Beef Phở", "description": "Tender slices of chicken and beef served in a fragrant Vietnamese broth, enriched with herbs and spices, garnished with fresh cilantro, bean sprouts, and lime wedges.", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "modifier_list_info": [{"modifier_list_id": "UVHU6JCHXHJODBFZRJAJQ4TU", "min_selected_modifiers": 0, "max_selected_modifiers": 1, "enabled": true, "ordinal": 0, "allow_quantities": "NOT_SET", "is_conversational": "NOT_SET", "hidden_from_customer_override": "NOT_SET"}], "variations": [{"type": "ITEM_VARIATION", "id": "OQQ6OJVHUOIKHMNLDUGX22AI", "updated_at": "2024-12-23T07:28:46.303Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1734938926303, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_variation_data": {"item_id": "O5TY25XFROGUKJF4MPJDFRO7", "name": "Regular", "ordinal": 0, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1750, "currency": "AUD"}, "location_overrides": [{"location_id": "LXRJ28YWPJNBZ", "sold_out": true}], "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_uri": "https://vietstreat.square.site/product/chicken-beef-ph-/33", "ecom_image_uris": ["https://vietstreat.square.site/uploads/1/3/3/8/*********/s198382238489620829_p33_i1_w512.jpeg"], "ecom_available": true, "ecom_visibility": "VISIBLE", "image_ids": ["62E6UJMZB3MZ2L6BMNLWUKT2"], "categories": [{"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251198518263808}, {"id": "WT6E6LJZ3P5WX6CLXGVX4INA", "ordinal": 5}, {"id": "CXZBESSOVBM6J4PPBGNCNUCJ", "ordinal": 5}], "description_html": "Tender slices of chicken and beef served in a fragrant Vietnamese broth, enriched with herbs and spices, garnished with fresh cilantro, bean sprouts, and lime wedges.", "description_plaintext": "Tender slices of chicken and beef served in a fragrant Vietnamese broth, enriched with herbs and spices, garnished with fresh cilantro, bean sprouts, and lime wedges.", "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251198518263808}, "is_alcoholic": false}}, {"type": "ITEM", "id": "AAHVABO23ARY4JXBK7KMD4SM", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_data": {"name": "Sunkist", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "UOLG2BL3Q272MDANJWYAAFJL", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_variation_data": {"item_id": "AAHVABO23ARY4JXBK7KMD4SM", "name": "Regular", "ordinal": 0, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 350, "currency": "AUD"}, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_uri": "https://vietstreat.square.site/product/sunkist/40", "ecom_available": true, "ecom_visibility": "VISIBLE", "categories": [{"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2250975179964416}, {"id": "U7OZVYPXN74GW6DZOOWDK2YV", "ordinal": 3}, {"id": "WKDJ42JBEBOXMI4GVULK3OUN", "ordinal": 3}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2250975179964416}}}, {"type": "ITEM", "id": "UNEDGKXEXRJNS5V35GB7JDB5", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-11-17T10:41:57Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "Black Beans Dessert (Chè đỗ đen)", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "2US62DAZB3H2KOWZOMJGY5EB", "updated_at": "2024-11-17T10:41:57.419Z", "created_at": "2024-11-17T10:41:57Z", "version": 1731840117419, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "UNEDGKXEXRJNS5V35GB7JDB5", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 750, "currency": "AUD"}, "track_inventory": false, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/black-beans-dessert-ch-en-/41", "ecom_available": true, "ecom_visibility": "VISIBLE", "categories": [{"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251679554600960}, {"id": "FVVQMWJET6HWNJN5XV2J6BXD", "ordinal": 3}, {"id": "G25FQBHDWHA2BGICH2ULMCON", "ordinal": 3}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251679554600960}}}, {"type": "ITEM", "id": "NQ7BDWN3P3D3JBAC2BM6E7AS", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-11-17T10:43:31.712Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "<PERSON><PERSON> sâm b<PERSON>", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "OBD6U7GALIVWUSOX7XJPJ737", "updated_at": "2024-12-23T07:28:41.74Z", "created_at": "2024-11-17T10:43:31.712Z", "version": 1734938921740, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "NQ7BDWN3P3D3JBAC2BM6E7AS", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 750, "currency": "AUD"}, "location_overrides": [{"location_id": "LXRJ28YWPJNBZ", "track_inventory": false, "sold_out": true}], "track_inventory": false, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/ch-s-m-b-l-ng/42", "ecom_image_uris": ["https://vietstreat.square.site/uploads/1/3/3/8/*********/s198382238489620829_p42_i1_w1187.jpeg"], "ecom_available": true, "ecom_visibility": "HIDDEN", "image_ids": ["ZKGQ2U5JQJQTHP3O43LQC5QR"], "categories": [{"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251610835124224}, {"id": "FVVQMWJET6HWNJN5XV2J6BXD", "ordinal": 4}, {"id": "G25FQBHDWHA2BGICH2ULMCON", "ordinal": 4}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251610835124224}, "is_alcoholic": false}}, {"type": "ITEM", "id": "AQRXGDJ6T57EFO37A2I4I2KU", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-11-17T10:44:43.28Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "⁠⁠<PERSON>ân gà sốt thái (Chicken feet)", "description": "Chicken feet are marinated and simmered in a spicy and savory Thai-inspired sauce, delivering a unique texture and bold flavor.", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "RC4SU76OIEXLV3EF3H2LN5IT", "updated_at": "2024-11-28T13:15:33.603Z", "created_at": "2024-11-17T10:44:43.28Z", "version": 1732799733603, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "AQRXGDJ6T57EFO37A2I4I2KU", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1750, "currency": "AUD"}, "track_inventory": false, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/-ch-n-g-s-t-th-i-chicken-feet-/43", "ecom_image_uris": ["https://vietstreat.square.site/uploads/1/3/3/8/*********/s198382238489620829_p43_i1_w1242.jpeg"], "ecom_available": true, "ecom_visibility": "VISIBLE", "image_ids": ["7SEUQOF7HOXRUZUVT645CWIJ"], "categories": [{"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251542115647488}, {"id": "FVVQMWJET6HWNJN5XV2J6BXD", "ordinal": 5}, {"id": "G25FQBHDWHA2BGICH2ULMCON", "ordinal": 5}], "description_html": "Chicken feet are marinated and simmered in a spicy and savory Thai-inspired sauce, delivering a unique texture and bold flavor.", "description_plaintext": "Chicken feet are marinated and simmered in a spicy and savory Thai-inspired sauce, delivering a unique texture and bold flavor.", "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251542115647488}, "is_alcoholic": false}}, {"type": "ITEM", "id": "TNUG67LKNEXKH56QQ2JT3Y6W", "updated_at": "2025-02-03T13:14:18.896Z", "created_at": "2024-11-17T10:45:24.067Z", "version": 1738588458896, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "<PERSON><PERSON><PERSON><PERSON> (Coconut Jelly)", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "GXASUHAFUWBSOIB3TNHCBP7D", "updated_at": "2024-11-28T13:16:03.219Z", "created_at": "2024-11-17T10:45:24.067Z", "version": 1732799763219, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "TNUG67LKNEXKH56QQ2JT3Y6W", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1200, "currency": "AUD"}, "track_inventory": false, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/th-ch-dua-coconut-jelly-/44", "ecom_image_uris": ["https://vietstreat.square.site/uploads/1/3/3/8/*********/s198382238489620829_p44_i1_w1290.jpeg"], "ecom_available": true, "ecom_visibility": "VISIBLE", "image_ids": ["L4W4EORTDCP6VHBS3J5PSENR"], "categories": [{"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251473396170752}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251473396170752}}}, {"type": "ITEM", "id": "HFBOVZSJAFEDFFG5K5HAXA3H", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-11-17T10:48:43.912Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "<PERSON><PERSON><PERSON> hột vịt bắc thảo (Congee with century egg)", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "6P4UEZDXAQU3MTXM2VCBWF6C", "updated_at": "2025-01-06T10:24:46.638Z", "created_at": "2024-11-17T10:48:43.912Z", "version": 1736159086638, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "HFBOVZSJAFEDFFG5K5HAXA3H", "name": "Regular", "ordinal": 0, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1650, "currency": "AUD"}, "location_overrides": [{"location_id": "LXRJ28YWPJNBZ", "track_inventory": false}], "track_inventory": false, "price_description": "", "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/ch-o-h-t-v-t-b-c-th-o-congee-with-century-egg-/45", "ecom_available": true, "ecom_visibility": "VISIBLE", "categories": [{"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251129798787072}, {"id": "WT6E6LJZ3P5WX6CLXGVX4INA", "ordinal": 10}, {"id": "CXZBESSOVBM6J4PPBGNCNUCJ", "ordinal": 10}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251129798787072}}}, {"type": "ITEM", "id": "S5F4UXJ2JX6J4S4QMOP56YVU", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-11-17T10:49:41.512Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "<PERSON><PERSON><PERSON> vị<PERSON> lộn ( 2 pcs Balut)", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "ZRWI7HUU5SX3AUGYLFBBN4NY", "updated_at": "2024-11-17T10:49:41.859Z", "created_at": "2024-11-17T10:49:41.512Z", "version": 1731840581859, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "S5F4UXJ2JX6J4S4QMOP56YVU", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1450, "currency": "AUD"}, "track_inventory": false, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/h-t-v-t-l-n-2-pcs-balut-/46", "ecom_available": true, "ecom_visibility": "VISIBLE", "categories": [{"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251198518263808}, {"id": "FVVQMWJET6HWNJN5XV2J6BXD", "ordinal": 7}, {"id": "G25FQBHDWHA2BGICH2ULMCON", "ordinal": 7}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251198518263808}}}, {"type": "ITEM", "id": "KIVJ4DBS5OK4J64W53ZBBO7V", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-11-17T10:50:29.37Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "b<PERSON>h tr<PERSON>g n<PERSON> ( 2pcs Grilled Rice Paper Rolls)", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "WLOA62RODZIMIB6RZPJBUNEY", "updated_at": "2024-11-17T10:50:29.731Z", "created_at": "2024-11-17T10:50:29.37Z", "version": 1731840629731, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "KIVJ4DBS5OK4J64W53ZBBO7V", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1790, "currency": "AUD"}, "track_inventory": false, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/b-nh-tr-ng-n-ng-2pcs-grilled-rice-paper-rolls-/47", "ecom_available": true, "ecom_visibility": "VISIBLE", "categories": [{"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251267237740544}, {"id": "FVVQMWJET6HWNJN5XV2J6BXD", "ordinal": 8}, {"id": "G25FQBHDWHA2BGICH2ULMCON", "ordinal": 8}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251267237740544}}}, {"type": "ITEM", "id": "XQD5AUPO22YMLI5EHJHICJDO", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-11-17T10:51:26.199Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "<PERSON><PERSON><PERSON> (2pcs Vietnames Crispy Dumplings)", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "HQ4OT3BNOMWFUZRWJQ65DQT6", "updated_at": "2024-12-23T07:28:29.663Z", "created_at": "2024-11-17T10:51:26.199Z", "version": 1734938909663, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "XQD5AUPO22YMLI5EHJHICJDO", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1450, "currency": "AUD"}, "location_overrides": [{"location_id": "LXRJ28YWPJNBZ", "track_inventory": false, "sold_out": true}], "track_inventory": false, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/b-nh-g-i-2pcs-vietnames-crispy-dumplings-/48", "ecom_available": true, "ecom_visibility": "HIDDEN", "categories": [{"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251404676694016}, {"id": "FVVQMWJET6HWNJN5XV2J6BXD", "ordinal": 9}, {"id": "G25FQBHDWHA2BGICH2ULMCON", "ordinal": 9}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251404676694016}, "is_alcoholic": false}}, {"type": "ITEM", "id": "IK6TXH66IOY72G7U4MMJJTNE", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-11-17T10:52:30.82Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "Sườn gà rang muối voi chips (Spare Ribs with Chips)", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "D7MSL55LSKPBDRJW4BBB25ZJ", "updated_at": "2024-11-17T10:52:31.169Z", "created_at": "2024-11-17T10:52:30.82Z", "version": 1731840751169, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "IK6TXH66IOY72G7U4MMJJTNE", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1750, "currency": "AUD"}, "track_inventory": false, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/s-n-g-rang-mu-i-voi-chips-spare-ribs-with-chips-/49", "ecom_available": true, "ecom_visibility": "VISIBLE", "categories": [{"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251061079310336}, {"id": "WT6E6LJZ3P5WX6CLXGVX4INA", "ordinal": 11}, {"id": "CXZBESSOVBM6J4PPBGNCNUCJ", "ordinal": 11}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "5SLA4ZMYJ6UJF772PILIVEQE", "ordinal": -2251061079310336}}}, {"type": "ITEM", "id": "LISOPCKXBAQ7GTU7GTJ6TFZY", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-11-30T10:38:50.176Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_data": {"name": "bò lá lốt", "label_color": "9da2a6", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "7HDRRPGYGFTZ763Z3U2GBRZK", "updated_at": "2024-11-30T10:38:50.133Z", "created_at": "2024-11-30T10:38:50.176Z", "version": 1732963130133, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_variation_data": {"item_id": "LISOPCKXBAQ7GTU7GTJ6TFZY", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1450, "currency": "AUD"}, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/b-l-l-t/50", "ecom_available": true, "ecom_visibility": "VISIBLE", "categories": [{"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251335957217280}, {"id": "FVVQMWJET6HWNJN5XV2J6BXD", "ordinal": 10}, {"id": "G25FQBHDWHA2BGICH2ULMCON", "ordinal": 10}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251335957217280}}}, {"type": "ITEM", "id": "WTXJYKD3VUKIY4FEIRN3MT3Q", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-11-30T10:41:12.41Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_data": {"name": "Fresh Coconut (dừa tươi)", "label_color": "9da2a6", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "TTSLMJIK7SW3BECEM2YHXKGY", "updated_at": "2024-11-30T10:41:12.37Z", "created_at": "2024-11-30T10:41:12.41Z", "version": 1732963272370, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_variation_data": {"item_id": "WTXJYKD3VUKIY4FEIRN3MT3Q", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1050, "currency": "AUD"}, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/fresh-coconut-d-a-t-i-/51", "ecom_available": true, "ecom_visibility": "VISIBLE", "categories": [{"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2250906460487680}, {"id": "U7OZVYPXN74GW6DZOOWDK2YV", "ordinal": 8}, {"id": "WKDJ42JBEBOXMI4GVULK3OUN", "ordinal": 8}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "NZJRKTKUXHM4LOURVKJ43WXX", "ordinal": -2250906460487680}}}, {"type": "ITEM", "id": "O6MLQLLU457TPG2KL7BB3DVQ", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-12-13T14:10:34.318Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "Mango (Xoài)", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "AOWHS2VIHUGGLWGHWEH6PDSO", "updated_at": "2024-12-13T14:10:34.739Z", "created_at": "2024-12-13T14:10:34.318Z", "version": 1734099034739, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "O6MLQLLU457TPG2KL7BB3DVQ", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 750, "currency": "AUD"}, "track_inventory": false, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/mango-xo-i-/53", "ecom_available": true, "ecom_visibility": "HIDDEN", "categories": [{"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251129798787072}, {"id": "FVVQMWJET6HWNJN5XV2J6BXD", "ordinal": 11}, {"id": "G25FQBHDWHA2BGICH2ULMCON", "ordinal": 11}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251129798787072}, "is_alcoholic": false}}, {"type": "ITEM", "id": "C3H5CQFEMH3USRDSWBT2I5BO", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2024-12-13T14:11:20.207Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "Pineapple (khốm)", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "STRCU5E2I5AGJLAP3PXYIUFY", "updated_at": "2024-12-13T14:11:20.549Z", "created_at": "2024-12-13T14:11:20.207Z", "version": 1734099080549, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "C3H5CQFEMH3USRDSWBT2I5BO", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 750, "currency": "AUD"}, "track_inventory": false, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/pineapple-kh-m-/54", "ecom_available": true, "ecom_visibility": "HIDDEN", "categories": [{"id": "FVVQMWJET6HWNJN5XV2J6BXD", "ordinal": 12}, {"id": "G25FQBHDWHA2BGICH2ULMCON", "ordinal": 12}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "is_alcoholic": false}}, {"type": "ITEM", "id": "BQISQDPHUYV2QN67CX2EKY26", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2025-01-01T10:54:06.779Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_data": {"name": "<PERSON><PERSON><PERSON> long", "label_color": "9da2a6", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "KFTY32RN7JXL5M4MCMTHQ22K", "updated_at": "2025-01-01T10:54:06.737Z", "created_at": "2025-01-01T10:54:06.779Z", "version": 1735728846737, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_variation_data": {"item_id": "BQISQDPHUYV2QN67CX2EKY26", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1850, "currency": "AUD"}, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_available": false, "ecom_visibility": "UNAVAILABLE", "categories": [{"id": "WT6E6LJZ3P5WX6CLXGVX4INA", "ordinal": 12}, {"id": "CXZBESSOVBM6J4PPBGNCNUCJ", "ordinal": 12}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false}}, {"type": "ITEM", "id": "A27RNGMS7ODXEEDU72KYMPRF", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2025-01-08T09:38:06.818Z", "version": 1743632309931, "is_deleted": false, "catalog_v1_ids": [{"catalog_v1_id": "758dd6c1-5a9a-4e5a-9e32-8d1c3a63352e", "location_id": "LXRJ28YWPJNBZ"}], "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_data": {"name": "<PERSON><PERSON>", "abbreviation": "Ne", "label_color": "ffe5457a", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "I2U35K7L5XNVKDR2NPGGQCFJ", "updated_at": "2025-01-10T10:16:02.565Z", "created_at": "2025-01-08T09:38:06.818Z", "version": 1736504162565, "is_deleted": false, "catalog_v1_ids": [{"catalog_v1_id": "d639466e-f406-4753-bf92-2a33c4f93075", "location_id": "LXRJ28YWPJNBZ"}], "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_variation_data": {"item_id": "A27RNGMS7ODXEEDU72KYMPRF", "name": "Regular", "ordinal": 0, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1450, "currency": "AUD"}, "price_description": "", "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_uri": "https://vietstreat.square.site/product/nem-nuong/56", "ecom_available": true, "ecom_visibility": "VISIBLE", "categories": [{"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251061079310336}, {"id": "YS52FU5UHKHU5JNUZ2U6OSCI", "ordinal": 0}], "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2251061079310336}}}, {"type": "ITEM", "id": "KD3BFJSBBTK6UZKI7ALROZ2P", "updated_at": "2025-05-17T06:10:07.006Z", "created_at": "2025-02-03T13:13:25.162Z", "version": 1747462207006, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_data": {"name": "<PERSON><PERSON> chua r<PERSON>", "description": "<PERSON><PERSON> chua r<PERSON>", "label_color": "9da2a6", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "U7TVTKLJ4Z5FWXIHD7MLX2LL", "updated_at": "2025-02-03T13:13:25.118Z", "created_at": "2025-02-03T13:13:25.162Z", "version": 1738588405118, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "item_variation_data": {"item_id": "KD3BFJSBBTK6UZKI7ALROZ2P", "name": "Regular", "ordinal": 1, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1450, "currency": "AUD"}, "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": true, "ecom_uri": "https://vietstreat.square.site/product/nem-chua-r-n/57", "ecom_available": true, "ecom_visibility": "VISIBLE", "categories": [{"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2250992359833600}, {"id": "FVVQMWJET6HWNJN5XV2J6BXD", "ordinal": 13}, {"id": "G25FQBHDWHA2BGICH2ULMCON", "ordinal": 13}], "description_html": "<p><PERSON><PERSON> chua r<PERSON></p>", "description_plaintext": "<PERSON><PERSON> chua r<PERSON>", "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "reporting_category": {"id": "QMK6JDT6WGXQFN5SYIGOMOH3", "ordinal": -2250992359833600}, "is_alcoholic": false}}, {"type": "ITEM", "id": "2ZNJ6KPKD2MPXYYHMDKE64AE", "updated_at": "2025-05-11T12:52:32.663Z", "created_at": "2025-05-11T12:52:07.356Z", "version": 1746967952663, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "k<PERSON>ai lang khen", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "PRMBZZIJRZ3KL4O3QOA2N6PL", "updated_at": "2025-05-11T12:52:07.315Z", "created_at": "2025-05-11T12:52:07.356Z", "version": 1746967927315, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "2ZNJ6KPKD2MPXYYHMDKE64AE", "name": "", "ordinal": 0, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1050, "currency": "AUD"}, "inventory_alert_type": "NONE", "sellable": true, "stockable": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_uri": "https://vietstreat.square.site/product/khoai-lang-khen/58", "ecom_available": true, "ecom_visibility": "VISIBLE", "kitchen_name": "", "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "is_alcoholic": false}}, {"type": "ITEM", "id": "CA5IKLU5UJR7I3EPNM2MCZAK", "updated_at": "2025-05-11T12:53:43.561Z", "created_at": "2025-05-11T12:53:20.894Z", "version": 1746968023561, "is_deleted": false, "present_at_all_locations": true, "item_data": {"name": "<PERSON><PERSON><PERSON> lang khen", "is_taxable": true, "tax_ids": ["AUSSALESTAXMLAZ3WM6R0PZ8"], "variations": [{"type": "ITEM_VARIATION", "id": "5OUJ2LFWO6ZSFW3YLV3GI4RJ", "updated_at": "2025-05-11T12:53:25.298Z", "created_at": "2025-05-11T12:53:20.894Z", "version": 1746968005298, "is_deleted": false, "present_at_all_locations": true, "item_variation_data": {"item_id": "CA5IKLU5UJR7I3EPNM2MCZAK", "name": "", "ordinal": 0, "pricing_type": "FIXED_PRICING", "price_money": {"amount": 1050, "currency": "AUD"}, "inventory_alert_type": "NONE", "sellable": true, "stockable": true, "channels": ["CH_FafrMm1H6aDIEI17txQiIswK6arrIUKD2XrfYRlQuYC"]}}], "product_type": "FOOD_AND_BEV", "skip_modifier_screen": false, "ecom_uri": "https://vietstreat.square.site/product/khoai-lang-khen/59", "ecom_available": true, "ecom_visibility": "VISIBLE", "categories": [{"id": "G25FQBHDWHA2BGICH2ULMCON", "ordinal": 68719476749}], "kitchen_name": "", "channels": ["CH_FafrMm1H6aDIEI17txQiIswK6arrIUKD2XrfYRlQuYC"], "is_archived": false, "is_alcoholic": false}}, {"type": "CATEGORY", "id": "5SLA4ZMYJ6UJF772PILIVEQE", "updated_at": "2024-07-19T04:08:29.718Z", "created_at": "2024-07-19T03:49:33.684Z", "version": 1721362109718, "is_deleted": false, "present_at_all_locations": true, "category_data": {"name": "Main", "category_type": "REGULAR_CATEGORY", "parent_category": {"ordinal": -2251731094208512}, "is_top_level": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "online_visibility": true, "ecom_seo_data": {"page_title": "", "page_description": "", "permalink": ""}}}, {"type": "CATEGORY", "id": "QMK6JDT6WGXQFN5SYIGOMOH3", "updated_at": "2024-08-07T03:39:00.864Z", "created_at": "2024-07-19T04:08:14.495Z", "version": 1723001940864, "is_deleted": false, "present_at_all_locations": true, "category_data": {"name": "Entree", "category_type": "REGULAR_CATEGORY", "parent_category": {"ordinal": -2251799813685248}, "is_top_level": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "online_visibility": true}}, {"type": "CATEGORY", "id": "NZJRKTKUXHM4LOURVKJ43WXX", "updated_at": "2024-10-24T06:00:17.19Z", "created_at": "2024-10-19T11:28:57.024Z", "version": 1729749617190, "is_deleted": false, "catalog_v1_ids": [{"catalog_v1_id": "ccbc2a73-4723-4a53-8bb3-66813b28a885", "location_id": "LXRJ28YWPJNBZ"}], "present_at_all_locations": true, "category_data": {"name": "Drinks", "category_type": "REGULAR_CATEGORY", "parent_category": {"ordinal": -2251662374731776}, "is_top_level": true, "channels": ["CH_M2ey7WNq38B9PfQTnxQiIswK6arrIUKD2XrfYRlQuYC"], "online_visibility": true, "ecom_seo_data": {"page_title": "", "page_description": "", "permalink": ""}}}, {"type": "CATEGORY", "id": "5VGARMWVLKMG4NWP7IRJYVQI", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2025-04-02T22:18:30.788Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "category_data": {"name": "Main", "category_type": "MENU_CATEGORY", "parent_category": {"ordinal": -2251593655255040}, "is_top_level": true, "channels": ["CH_FafrMm1H6aDIEI17txQiIswK6arrIUKD2XrfYRlQuYC"], "online_visibility": true}}, {"type": "CATEGORY", "id": "OHREKNA4Z2B7MFQFLD7CX6VT", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2025-04-02T22:18:30.788Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "category_data": {"name": "Main", "category_type": "MENU_CATEGORY", "parent_category": {"ordinal": -2251524935778304}, "is_top_level": true, "channels": ["CH_FafrMm1H6aDIEI17txQiIswK6arrIUKD2XrfYRlQuYC"], "online_visibility": true}}, {"type": "CATEGORY", "id": "WKDJ42JBEBOXMI4GVULK3OUN", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2025-04-02T22:18:30.788Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "category_data": {"name": "Drinks", "category_type": "MENU_CATEGORY", "parent_category": {"id": "OHREKNA4Z2B7MFQFLD7CX6VT", "ordinal": 2}, "is_top_level": false, "channels": ["CH_FafrMm1H6aDIEI17txQiIswK6arrIUKD2XrfYRlQuYC"], "online_visibility": true, "root_category": "OHREKNA4Z2B7MFQFLD7CX6VT"}}, {"type": "CATEGORY", "id": "CXZBESSOVBM6J4PPBGNCNUCJ", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2025-04-02T22:18:30.788Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "category_data": {"name": "Mains", "category_type": "MENU_CATEGORY", "parent_category": {"id": "OHREKNA4Z2B7MFQFLD7CX6VT", "ordinal": 0}, "is_top_level": false, "channels": ["CH_FafrMm1H6aDIEI17txQiIswK6arrIUKD2XrfYRlQuYC"], "online_visibility": true, "root_category": "OHREKNA4Z2B7MFQFLD7CX6VT"}}, {"type": "CATEGORY", "id": "G25FQBHDWHA2BGICH2ULMCON", "updated_at": "2025-05-11T12:53:25.298Z", "created_at": "2025-04-02T22:18:30.788Z", "version": 1746968005298, "is_deleted": false, "present_at_all_locations": true, "category_data": {"name": "Entrée", "category_type": "MENU_CATEGORY", "parent_category": {"id": "OHREKNA4Z2B7MFQFLD7CX6VT", "ordinal": 1}, "is_top_level": false, "channels": ["CH_FafrMm1H6aDIEI17txQiIswK6arrIUKD2XrfYRlQuYC"], "online_visibility": true, "root_category": "OHREKNA4Z2B7MFQFLD7CX6VT", "ecom_seo_data": {"page_title": "", "page_description": "", "permalink": ""}}}, {"type": "CATEGORY", "id": "U7OZVYPXN74GW6DZOOWDK2YV", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2025-04-02T22:18:30.788Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "category_data": {"name": "Drinks", "category_type": "MENU_CATEGORY", "parent_category": {"id": "YS52FU5UHKHU5JNUZ2U6OSCI", "ordinal": 2}, "is_top_level": false, "channels": ["CH_FafrMm1H6aDIEI17txQiIswK6arrIUKD2XrfYRlQuYC"], "online_visibility": true, "root_category": "YS52FU5UHKHU5JNUZ2U6OSCI"}}, {"type": "CATEGORY", "id": "YS52FU5UHKHU5JNUZ2U6OSCI", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2025-04-02T22:18:30.788Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "category_data": {"name": "<PERSON><PERSON><PERSON>", "category_type": "MENU_CATEGORY", "parent_category": {"ordinal": -2251456216301568}, "is_top_level": true, "channels": ["CH_FafrMm1H6aDIEI17txQiIswK6arrIUKD2XrfYRlQuYC"], "online_visibility": true}}, {"type": "CATEGORY", "id": "FVVQMWJET6HWNJN5XV2J6BXD", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2025-04-02T22:18:30.788Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "category_data": {"name": "Entrée", "category_type": "MENU_CATEGORY", "parent_category": {"id": "YS52FU5UHKHU5JNUZ2U6OSCI", "ordinal": 0}, "is_top_level": false, "channels": ["CH_FafrMm1H6aDIEI17txQiIswK6arrIUKD2XrfYRlQuYC"], "online_visibility": true, "root_category": "YS52FU5UHKHU5JNUZ2U6OSCI"}}, {"type": "CATEGORY", "id": "WT6E6LJZ3P5WX6CLXGVX4INA", "updated_at": "2025-04-02T22:18:29.931Z", "created_at": "2025-04-02T22:18:30.788Z", "version": 1743632309931, "is_deleted": false, "present_at_all_locations": true, "category_data": {"name": "Mains", "category_type": "MENU_CATEGORY", "parent_category": {"id": "YS52FU5UHKHU5JNUZ2U6OSCI", "ordinal": 1}, "is_top_level": false, "channels": ["CH_FafrMm1H6aDIEI17txQiIswK6arrIUKD2XrfYRlQuYC"], "online_visibility": true, "root_category": "YS52FU5UHKHU5JNUZ2U6OSCI"}}, {"type": "TAX", "id": "AUSSALESTAXMLAZ3WM6R0PZ8", "updated_at": "2024-07-18T11:04:06.775Z", "created_at": "2024-07-18T11:04:06.828Z", "version": 1721300646775, "is_deleted": false, "present_at_all_locations": true, "tax_data": {"name": "GST", "calculation_phase": "TAX_SUBTOTAL_PHASE", "inclusion_type": "INCLUSIVE", "percentage": "10.0", "applies_to_custom_amounts": true, "enabled": true, "tax_type_id": "au_sales_tax", "tax_type_name": "Sales Tax", "applies_to_product_set_id": "DJU3JA5KHIDTGVKY4D5GZY3U"}}, {"type": "DISCOUNT", "id": "2XIJQQ4HR655Q7GOCQ6RC2F6", "updated_at": "2024-10-30T02:03:51.826Z", "created_at": "2024-10-30T02:03:51.871Z", "version": 1730253831826, "is_deleted": false, "catalog_v1_ids": [{"catalog_v1_id": "91a5e111-2b32-48b1-8f3e-0c33dc5a5442", "location_id": "LXRJ28YWPJNBZ"}], "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "discount_data": {"name": "student ", "discount_type": "FIXED_PERCENTAGE", "percentage": "10.0", "pin_required": true, "modify_tax_basis": "MODIFY_TAX_BASIS"}}, {"type": "DISCOUNT", "id": "BIOUCRFIP7CTY7TSVXHLDOXE", "updated_at": "2024-11-28T13:44:58.085Z", "created_at": "2024-11-28T13:44:58.114Z", "version": 1732801498085, "is_deleted": false, "present_at_all_locations": true, "discount_data": {"name": "Badminton sunshine club", "discount_type": "FIXED_PERCENTAGE", "percentage": "10.0", "application_method": "MANUALLY_APPLIED", "modify_tax_basis": "MODIFY_TAX_BASIS", "maximum_amount_money": {"amount": 5000, "currency": "AUD"}}}, {"type": "DISCOUNT", "id": "MUOWR3W5A4KENY5VPHD6L457", "updated_at": "2024-12-14T05:18:38.129Z", "created_at": "2024-12-14T05:18:38.172Z", "version": 1734153518129, "is_deleted": false, "present_at_all_locations": true, "discount_data": {"name": "Employee Discount", "discount_type": "FIXED_PERCENTAGE", "percentage": "30.0", "pin_required": false, "application_method": "MANUALLY_APPLIED", "modify_tax_basis": "MODIFY_TAX_BASIS"}}, {"type": "DISCOUNT", "id": "GGVX6NTNNPTMRXVK7WYD3UA7", "updated_at": "2024-12-16T23:29:59.071Z", "created_at": "2024-12-16T23:29:59.114Z", "version": 1734391799071, "is_deleted": false, "present_at_all_locations": true, "discount_data": {"name": "Amount Over $40 get %5 Discount", "discount_type": "FIXED_PERCENTAGE", "percentage": "5.0", "application_method": "MANUALLY_APPLIED", "modify_tax_basis": "MODIFY_TAX_BASIS"}}, {"type": "MODIFIER_LIST", "id": "FCLRZEJVQ452DYC5P2OSHSNJ", "updated_at": "2024-10-24T09:08:24.577Z", "created_at": "2024-07-19T03:08:13.018Z", "version": 1729760904577, "is_deleted": false, "catalog_v1_ids": [{"catalog_v1_id": "e1d91834-1b00-4d27-9efe-afb1f6ca57e5", "location_id": "LXRJ28YWPJNBZ"}], "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_list_data": {"name": "Extra", "ordinal": 0, "selection_type": "MULTIPLE", "modifiers": [{"type": "MODIFIER", "id": "GSYHC7666JGRW4AT6YUXHL7G", "updated_at": "2024-10-24T05:31:19.547Z", "created_at": "2024-07-19T03:08:13.018Z", "version": 1729747879547, "is_deleted": false, "catalog_v1_ids": [{"catalog_v1_id": "96da14ac-965d-4318-86a3-6ab94d2dd2bb", "location_id": "LXRJ28YWPJNBZ"}], "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "<PERSON><PERSON><PERSON><PERSON> (Egg)", "price_money": {"amount": 300, "currency": "AUD"}, "on_by_default": false, "ordinal": 1, "modifier_list_id": "FCLRZEJVQ452DYC5P2OSHSNJ"}}, {"type": "MODIFIER", "id": "JLDJPBQNMOJ3PVMX4VAJALCV", "updated_at": "2024-10-24T05:31:19.547Z", "created_at": "2024-10-24T05:31:19.594Z", "version": 1729747879547, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "2 pcs <PERSON><PERSON><PERSON><PERSON> (Egg)", "price_money": {"amount": 600, "currency": "AUD"}, "on_by_default": false, "ordinal": 2, "modifier_list_id": "FCLRZEJVQ452DYC5P2OSHSNJ"}}, {"type": "MODIFIER", "id": "DXTRXQIZOI7VBC3VHMCEMEV3", "updated_at": "2024-10-24T05:31:19.547Z", "created_at": "2024-07-19T03:08:13.018Z", "version": 1729747879547, "is_deleted": false, "catalog_v1_ids": [{"catalog_v1_id": "ccfd3f5e-7074-4ccd-99df-4eae116cfd45", "location_id": "LXRJ28YWPJNBZ"}], "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "<PERSON><PERSON><PERSON><PERSON> (Pork chop)", "price_money": {"amount": 600, "currency": "AUD"}, "on_by_default": false, "ordinal": 3, "modifier_list_id": "FCLRZEJVQ452DYC5P2OSHSNJ"}}, {"type": "MODIFIER", "id": "ZEOKSTSR6NRHRZ62NB2B3U32", "updated_at": "2024-10-24T09:08:24.577Z", "created_at": "2024-10-24T09:08:24.614Z", "version": 1729760904577, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "2 pcs Sườn (Pork chop)", "price_money": {"amount": 1200, "currency": "AUD"}, "on_by_default": false, "ordinal": 4, "modifier_list_id": "FCLRZEJVQ452DYC5P2OSHSNJ"}}], "is_conversational": true, "modifier_type": "LIST", "max_length": 150, "text_required": false, "internal_name": "Porkchop"}}, {"type": "MODIFIER_LIST", "id": "WJOQWJPR7I7AW3WSGDSVYMFL", "updated_at": "2024-07-19T03:48:51.842Z", "created_at": "2024-07-19T03:48:12.812Z", "version": 1721360931842, "is_deleted": false, "present_at_all_locations": true, "modifier_list_data": {"name": "extra", "ordinal": 1, "selection_type": "MULTIPLE", "modifiers": [{"type": "MODIFIER", "id": "4ZYCIFFYCDRCNGK4VUPBZOYA", "updated_at": "2024-07-19T03:48:12.779Z", "created_at": "2024-07-19T03:48:12.812Z", "version": 1721360892779, "is_deleted": false, "present_at_all_locations": true, "modifier_data": {"name": "Thịt nướng (Charcoal meat)", "price_money": {"amount": 500, "currency": "AUD"}, "on_by_default": false, "ordinal": 1, "modifier_list_id": "WJOQWJPR7I7AW3WSGDSVYMFL"}}, {"type": "MODIFIER", "id": "JTXP5TGCLCAOYVPAYZXTGCWN", "updated_at": "2024-07-19T03:48:12.779Z", "created_at": "2024-07-19T03:48:12.812Z", "version": 1721360892779, "is_deleted": false, "present_at_all_locations": true, "modifier_data": {"name": "<PERSON><PERSON> (Meat ball)", "price_money": {"amount": 200, "currency": "AUD"}, "on_by_default": false, "ordinal": 2, "modifier_list_id": "WJOQWJPR7I7AW3WSGDSVYMFL"}}], "modifier_type": "LIST", "max_length": 150, "text_required": false, "internal_name": "<PERSON><PERSON> vermicelli"}}, {"type": "MODIFIER_LIST", "id": "P3SOSO4KCJE3JDXCT6CYDEMU", "updated_at": "2024-08-07T03:37:40.592Z", "created_at": "2024-07-19T03:53:20.053Z", "version": 1723001860592, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_list_data": {"name": "Extra", "ordinal": 2, "selection_type": "MULTIPLE", "modifiers": [{"type": "MODIFIER", "id": "ILLOHIKDWIDOJJI4KXJW52JQ", "updated_at": "2024-07-19T03:53:20.022Z", "created_at": "2024-07-19T03:53:20.053Z", "version": 1721361200022, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "<PERSON><PERSON><PERSON><PERSON> gà", "price_money": {"amount": 300, "currency": "AUD"}, "on_by_default": false, "ordinal": 1, "modifier_list_id": "P3SOSO4KCJE3JDXCT6CYDEMU"}}], "modifier_type": "LIST", "max_length": 150, "text_required": false, "internal_name": "<PERSON><PERSON><PERSON><PERSON> gà"}}, {"type": "MODIFIER_LIST", "id": "6XXSIBYTMOLVKJXOVOMU2ZU5", "updated_at": "2024-10-24T05:51:50.332Z", "created_at": "2024-10-24T05:41:17.206Z", "version": 1729749110332, "is_deleted": false, "present_at_all_locations": true, "modifier_list_data": {"name": "Chicken", "ordinal": 3, "selection_type": "MULTIPLE", "modifiers": [{"type": "MODIFIER", "id": "BFET73BHNRJNIPUOLJ7O3X3U", "updated_at": "2024-10-24T05:41:17.167Z", "created_at": "2024-10-24T05:41:17.206Z", "version": 1729748477167, "is_deleted": false, "present_at_all_locations": true, "modifier_data": {"name": "<PERSON><PERSON><PERSON><PERSON> (Egg)", "price_money": {"amount": 300, "currency": "AUD"}, "on_by_default": false, "ordinal": 1, "modifier_list_id": "6XXSIBYTMOLVKJXOVOMU2ZU5"}}, {"type": "MODIFIER", "id": "IKDGDXE3K6W24UPY5Y4KN7AU", "updated_at": "2024-10-24T05:41:17.167Z", "created_at": "2024-10-24T05:41:17.206Z", "version": 1729748477167, "is_deleted": false, "present_at_all_locations": true, "modifier_data": {"name": "2x <PERSON><PERSON><PERSON><PERSON> (Egg)", "price_money": {"amount": 600, "currency": "AUD"}, "on_by_default": false, "ordinal": 2, "modifier_list_id": "6XXSIBYTMOLVKJXOVOMU2ZU5"}}, {"type": "MODIFIER", "id": "JROHTMLZZGWHGLWUKZ26ACRP", "updated_at": "2024-10-24T05:41:17.167Z", "created_at": "2024-10-24T05:41:17.206Z", "version": 1729748477167, "is_deleted": false, "present_at_all_locations": true, "modifier_data": {"name": "Chicken", "price_money": {"amount": 600, "currency": "AUD"}, "on_by_default": false, "ordinal": 3, "modifier_list_id": "6XXSIBYTMOLVKJXOVOMU2ZU5"}}], "modifier_type": "LIST", "max_length": 150, "text_required": false, "internal_name": "Chicken"}}, {"type": "MODIFIER_LIST", "id": "2PEO526FN2TTSAOVHXL4CWCO", "updated_at": "2024-10-24T05:51:50.332Z", "created_at": "2024-10-24T05:51:30.088Z", "version": 1729749110332, "is_deleted": false, "present_at_all_locations": true, "modifier_list_data": {"name": "Spring Rolls", "ordinal": 4, "selection_type": "MULTIPLE", "modifiers": [{"type": "MODIFIER", "id": "VF7EMSV5VHVAUMMG4X5IK5FU", "updated_at": "2024-10-24T05:51:30.048Z", "created_at": "2024-10-24T05:51:30.088Z", "version": 1729749090048, "is_deleted": false, "present_at_all_locations": true, "modifier_data": {"name": "Spring Rolls", "price_money": {"amount": 300, "currency": "AUD"}, "on_by_default": false, "ordinal": 1, "modifier_list_id": "2PEO526FN2TTSAOVHXL4CWCO"}}], "modifier_type": "LIST", "max_length": 150, "text_required": false, "internal_name": "Spring Rolls"}}, {"type": "MODIFIER_LIST", "id": "JH32VO44Q65MTSI3P7R3ATEO", "updated_at": "2024-10-24T09:02:43.015Z", "created_at": "2024-10-24T05:54:37.466Z", "version": 1729760563015, "is_deleted": false, "present_at_all_locations": true, "modifier_list_data": {"name": "Chicken & Beef", "ordinal": 5, "selection_type": "MULTIPLE", "modifiers": [{"type": "MODIFIER", "id": "GXBYLBHX7IPURAE3OI7SPWXX", "updated_at": "2024-10-24T05:54:37.429Z", "created_at": "2024-10-24T05:54:37.466Z", "version": 1729749277429, "is_deleted": false, "present_at_all_locations": true, "modifier_data": {"name": "Chicken & Beef", "price_money": {"amount": 500, "currency": "AUD"}, "on_by_default": false, "ordinal": 1, "modifier_list_id": "JH32VO44Q65MTSI3P7R3ATEO"}}, {"type": "MODIFIER", "id": "6UJZAHHVNKFIUJXJ4HFX6JTD", "updated_at": "2024-10-24T05:54:37.429Z", "created_at": "2024-10-24T05:54:37.466Z", "version": 1729749277429, "is_deleted": false, "present_at_all_locations": true, "modifier_data": {"name": "Chicken", "price_money": {"amount": 300, "currency": "AUD"}, "on_by_default": false, "ordinal": 2, "modifier_list_id": "JH32VO44Q65MTSI3P7R3ATEO"}}, {"type": "MODIFIER", "id": "ZMQAF2D3HDLLLKIBEOMSON6C", "updated_at": "2024-10-24T05:54:37.429Z", "created_at": "2024-10-24T05:54:37.466Z", "version": 1729749277429, "is_deleted": false, "present_at_all_locations": true, "modifier_data": {"name": "<PERSON><PERSON>", "price_money": {"amount": 300, "currency": "AUD"}, "on_by_default": false, "ordinal": 3, "modifier_list_id": "JH32VO44Q65MTSI3P7R3ATEO"}}], "modifier_type": "LIST", "max_length": 150, "text_required": false, "internal_name": "Chicken & Beef"}}, {"type": "MODIFIER_LIST", "id": "WQPY2U5LPKX5FLF24YJOY5SE", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_list_data": {"name": "Protein Addition", "selection_type": "MULTIPLE", "modifiers": [{"type": "MODIFIER", "id": "764LD7CNJTHWDO3AGA2R7L4L", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "Egg", "price_money": {"amount": 300, "currency": "AUD"}, "on_by_default": false, "ordinal": 0, "modifier_list_id": "WQPY2U5LPKX5FLF24YJOY5SE"}}, {"type": "MODIFIER", "id": "CNCEYJP4TEYVXD6MGF62U7C5", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "Pork Chop", "price_money": {"amount": 800, "currency": "AUD"}, "on_by_default": false, "ordinal": 1, "modifier_list_id": "WQPY2U5LPKX5FLF24YJOY5SE"}}], "is_conversational": false, "modifier_type": "LIST"}}, {"type": "MODIFIER_LIST", "id": "BREJNQ6MAKWFG4IYLWC3QRXB", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_list_data": {"name": "Protein Addition (Charcoal Chicken with Broken Rice)", "selection_type": "MULTIPLE", "modifiers": [{"type": "MODIFIER", "id": "QJFHSA2VM7ICD67FVIQVOKKZ", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "Chicken", "price_money": {"amount": 600, "currency": "AUD"}, "on_by_default": false, "ordinal": 0, "modifier_list_id": "BREJNQ6MAKWFG4IYLWC3QRXB"}}, {"type": "MODIFIER", "id": "Y5TFH6QVFJUDZTW43IJ7CTRF", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "Egg", "price_money": {"amount": 300, "currency": "AUD"}, "on_by_default": false, "ordinal": 1, "modifier_list_id": "BREJNQ6MAKWFG4IYLWC3QRXB"}}], "is_conversational": false, "modifier_type": "LIST"}}, {"type": "MODIFIER_LIST", "id": "PJVS6LUNE7HPWAN7XP3SVVHM", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_list_data": {"name": "Protein Addition (<PERSON>à Nội Ver<PERSON>elli)", "selection_type": "MULTIPLE", "modifiers": [{"type": "MODIFIER", "id": "P2UAXLAVYOGOLZJKPJAUCRJD", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "Charcoal Chicken", "price_money": {"amount": 500, "currency": "AUD"}, "on_by_default": false, "ordinal": 0, "modifier_list_id": "PJVS6LUNE7HPWAN7XP3SVVHM"}}, {"type": "MODIFIER", "id": "KMIC5YJLH7Y76DMHTFX5KFNQ", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "Charcoal Pork Chop", "price_money": {"amount": 500, "currency": "AUD"}, "on_by_default": false, "ordinal": 1, "modifier_list_id": "PJVS6LUNE7HPWAN7XP3SVVHM"}}, {"type": "MODIFIER", "id": "3OITV2SXZ6KIGHCLHMVCKLAL", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "Meatball", "price_money": {"amount": 200, "currency": "AUD"}, "on_by_default": false, "ordinal": 2, "modifier_list_id": "PJVS6LUNE7HPWAN7XP3SVVHM"}}], "is_conversational": false, "modifier_type": "LIST"}}, {"type": "MODIFIER_LIST", "id": "65TK36MGJ3PR563RRVAEQGKD", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_list_data": {"name": "Protein Addition (<PERSON><PERSON><PERSON><PERSON> with <PERSON>)", "selection_type": "MULTIPLE", "modifiers": [{"type": "MODIFIER", "id": "MV2KOZAE77QIBP2NMZW2YMF2", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "Chicken", "price_money": {"amount": 600, "currency": "AUD"}, "on_by_default": false, "ordinal": 0, "modifier_list_id": "65TK36MGJ3PR563RRVAEQGKD"}}], "is_conversational": false, "modifier_type": "LIST"}}, {"type": "MODIFIER_LIST", "id": "YS5VAKMX6MV4UKAHMBTXHWNS", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_list_data": {"name": "Add-On", "selection_type": "MULTIPLE", "modifiers": [{"type": "MODIFIER", "id": "BYIHULXZQH3QN4NZEUL3UYCG", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "Spring Rolls", "price_money": {"amount": 300, "currency": "AUD"}, "on_by_default": false, "ordinal": 0, "modifier_list_id": "YS5VAKMX6MV4UKAHMBTXHWNS"}}], "is_conversational": false, "modifier_type": "LIST"}}, {"type": "MODIFIER_LIST", "id": "ILDHC5LZJGOZTVRUINAOE2LH", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_list_data": {"name": "Protein Addition (Chicken Phở)", "selection_type": "MULTIPLE", "modifiers": [{"type": "MODIFIER", "id": "BYN2WGLL3ZM7SGBC2GWQFZ3O", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "Chicken", "price_money": {"amount": 300, "currency": "AUD"}, "on_by_default": false, "ordinal": 0, "modifier_list_id": "ILDHC5LZJGOZTVRUINAOE2LH"}}], "is_conversational": false, "modifier_type": "LIST"}}, {"type": "MODIFIER_LIST", "id": "UVHU6JCHXHJODBFZRJAJQ4TU", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_list_data": {"name": "Protein Addition (Chicken & Beef Phở)", "selection_type": "MULTIPLE", "modifiers": [{"type": "MODIFIER", "id": "ZE5LMBPZPH5Q5QA374ZRIWAD", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "Chicken & Beef", "price_money": {"amount": 500, "currency": "AUD"}, "on_by_default": false, "ordinal": 0, "modifier_list_id": "UVHU6JCHXHJODBFZRJAJQ4TU"}}], "is_conversational": false, "modifier_type": "LIST"}}, {"type": "MODIFIER_LIST", "id": "EO7Q57EIISPSIYFW726V6AXA", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_list_data": {"name": "Protein Addition (Combination Phở)", "selection_type": "MULTIPLE", "modifiers": [{"type": "MODIFIER", "id": "ZZWDFNSAGIMU56HFTLHQVFSJ", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "Chicken & Beef", "price_money": {"amount": 500, "currency": "AUD"}, "on_by_default": false, "ordinal": 0, "modifier_list_id": "EO7Q57EIISPSIYFW726V6AXA"}}], "is_conversational": false, "modifier_type": "LIST"}}, {"type": "MODIFIER_LIST", "id": "XMQ4FZZKF2HLBXA275VMWEAE", "updated_at": "2025-04-29T16:43:55.655Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1745945035655, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_list_data": {"name": "Sunkist Flavour Choice", "selection_type": "SINGLE", "modifiers": [{"type": "MODIFIER", "id": "6FWCFCWPQB6VEUMKYD3MNCGJ", "updated_at": "2025-04-29T16:43:55.655Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1745945035655, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "Flavour 1", "price_money": {"amount": 0, "currency": "AUD"}, "on_by_default": false, "ordinal": 0, "modifier_list_id": "XMQ4FZZKF2HLBXA275VMWEAE"}}, {"type": "MODIFIER", "id": "OWUKCRHNYIDMFXR6NC3UGBZS", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "Flavour 2", "price_money": {"amount": 0, "currency": "AUD"}, "on_by_default": false, "ordinal": 1, "modifier_list_id": "XMQ4FZZKF2HLBXA275VMWEAE"}}, {"type": "MODIFIER", "id": "UDSOI6IJAC4K6KEKD5YWXUGZ", "updated_at": "2024-10-25T02:10:45.775Z", "created_at": "2024-10-25T02:10:45.98Z", "version": 1729822245775, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "modifier_data": {"name": "Flavour 3", "price_money": {"amount": 0, "currency": "AUD"}, "on_by_default": false, "ordinal": 2, "modifier_list_id": "XMQ4FZZKF2HLBXA275VMWEAE"}}], "is_conversational": false, "modifier_type": "LIST", "min_selected_modifiers": 1, "max_selected_modifiers": 1}}, {"type": "PRICING_RULE", "id": "R376RIPPRU27YQATL76UNLFX", "updated_at": "2025-01-28T09:08:19.709Z", "created_at": "2024-11-17T11:09:31.092Z", "version": 1738055299709, "is_deleted": false, "present_at_all_locations": true, "pricing_rule_data": {"time_period_ids": ["55CO6FHEWYKCKODVIHLGZ2GA", "XQE7HDJFE6C2JHWTWEOJPDLZ", "MNAJ5C6DPYURCWBXKVYBLYAB", "YACGRNBNPFYHKN65CZ2PBBQ5", "E3VYDQ5HOM54PLDW54LVQOHF", "IE7TW32CICQRP3DRMKYDILSA", "2NWW62O7COS2CLTV7O2IYFZZ", "O34CLHJ6TRVEE5AA4JAIEQ5V", "OS2SNMJHDV6GYORYXGKO3G4E", "GPDZN2DCYQK5NY3MSEKOLYAK", "LR3S2RSQSWTXLTKZ32D6FQLU", "5HP2RRXLKH2M47XXEDPDYVEO", "6QXVPS6W6IPOCHAHHG4M5ZXZ"], "match_products_id": "AEQNF6RHHOBWFZXIQ2GMHUOW", "stackable": "EXCLUSIVE", "application_mode": "AUTOMATIC", "service_charge_id": "IHJBTQDSOX3VESFQ443XRPIE", "disabled_pricing_sources": ["SQUARE_ONLINE"]}}, {"type": "PRODUCT_SET", "id": "DJU3JA5KHIDTGVKY4D5GZY3U", "updated_at": "2024-07-18T11:04:06.775Z", "created_at": "2024-07-18T11:04:06.828Z", "version": 1721300646775, "is_deleted": false, "present_at_all_locations": true, "product_set_data": {"all_products": true, "match_custom_amounts": true}}, {"type": "PRODUCT_SET", "id": "K6TDNFOSIKEWXKXW4S3XDDSH", "updated_at": "2024-11-17T11:08:41.975Z", "created_at": "2024-11-17T11:08:42.015Z", "version": 1731841721975, "is_deleted": false, "present_at_all_locations": true, "product_set_data": {"all_products": true}}, {"type": "PRODUCT_SET", "id": "AEQNF6RHHOBWFZXIQ2GMHUOW", "updated_at": "2024-11-17T11:09:31.055Z", "created_at": "2024-11-17T11:09:31.092Z", "version": 1731841771055, "is_deleted": false, "present_at_all_locations": true, "product_set_data": {"all_products": true}}, {"type": "PRODUCT_SET", "id": "AUTBOER57RDCVI3WC4TKJMYE", "updated_at": "2024-12-16T23:29:59.071Z", "created_at": "2024-12-16T23:29:59.114Z", "version": 1734391799071, "is_deleted": false, "present_at_all_locations": true, "product_set_data": {"all_products": true}}, {"type": "TIME_PERIOD", "id": "VM6Q2HSHW2D5CRG2YNB3F6XD", "updated_at": "2024-11-17T11:08:41.975Z", "created_at": "2024-11-17T11:08:42.015Z", "version": 1731841721975, "is_deleted": false, "present_at_all_locations": true, "time_period_data": {"event": "BEGIN:VEVENT\nDURATION:PT23H59M\nRRULE:FREQ=WEEKLY;BYDAY=SU\nDTSTART:20161225T000000\nEND:VEVENT\n"}}, {"type": "TIME_PERIOD", "id": "55CO6FHEWYKCKODVIHLGZ2GA", "updated_at": "2024-12-27T13:55:50.423Z", "created_at": "2024-11-17T11:09:31.092Z", "version": 1735307750423, "is_deleted": false, "present_at_all_locations": true, "time_period_data": {"event": "BEGIN:VEVENT\nDURATION:PT23H59M59S\nDTSTART:20251225T000000\nEND:VEVENT\n"}}, {"type": "TIME_PERIOD", "id": "XQE7HDJFE6C2JHWTWEOJPDLZ", "updated_at": "2024-12-27T23:34:21.305Z", "created_at": "2024-11-17T11:09:31.092Z", "version": 1735342461305, "is_deleted": false, "present_at_all_locations": true, "time_period_data": {"event": "BEGIN:VEVENT\nDURATION:PT23H59M59S\nDTSTART:20251226T000000\nEND:VEVENT\n"}}, {"type": "TIME_PERIOD", "id": "MNAJ5C6DPYURCWBXKVYBLYAB", "updated_at": "2025-01-02T12:23:32.076Z", "created_at": "2024-11-17T11:09:31.092Z", "version": 1735820612076, "is_deleted": false, "present_at_all_locations": true, "time_period_data": {"event": "BEGIN:VEVENT\nDURATION:PT23H59M59S\nDTSTART:20260101T000000\nEND:VEVENT\n"}}, {"type": "TIME_PERIOD", "id": "YACGRNBNPFYHKN65CZ2PBBQ5", "updated_at": "2025-01-29T08:36:16.196Z", "created_at": "2024-11-17T11:09:31.092Z", "version": 1738139776196, "is_deleted": false, "present_at_all_locations": true, "time_period_data": {"event": "BEGIN:VEVENT\nDURATION:PT23H59M59S\nDTSTART:20260126T000000\nEND:VEVENT\n"}}, {"type": "TIME_PERIOD", "id": "E3VYDQ5HOM54PLDW54LVQOHF", "updated_at": "2025-03-12T15:33:21.633Z", "created_at": "2024-11-17T11:09:31.092Z", "version": 1741793601633, "is_deleted": false, "present_at_all_locations": true, "time_period_data": {"event": "BEGIN:VEVENT\nDURATION:PT23H59M59S\nDTSTART:20260309T000000\nEND:VEVENT\n"}}, {"type": "TIME_PERIOD", "id": "IE7TW32CICQRP3DRMKYDILSA", "updated_at": "2025-04-20T00:54:40.349Z", "created_at": "2024-11-17T11:09:31.092Z", "version": 1745110480349, "is_deleted": false, "present_at_all_locations": true, "time_period_data": {"event": "BEGIN:VEVENT\nDURATION:PT23H59M59S\nDTSTART:20260403T000000\nEND:VEVENT\n"}}, {"type": "TIME_PERIOD", "id": "2NWW62O7COS2CLTV7O2IYFZZ", "updated_at": "2025-04-21T21:59:35.751Z", "created_at": "2024-11-17T11:09:31.092Z", "version": 1745272775751, "is_deleted": false, "present_at_all_locations": true, "time_period_data": {"event": "BEGIN:VEVENT\nDURATION:PT23H59M59S\nDTSTART:20260404T000000\nEND:VEVENT\n"}}, {"type": "TIME_PERIOD", "id": "O34CLHJ6TRVEE5AA4JAIEQ5V", "updated_at": "2025-04-21T13:36:52.554Z", "created_at": "2024-11-17T11:09:31.092Z", "version": 1745242612554, "is_deleted": false, "present_at_all_locations": true, "time_period_data": {"event": "BEGIN:VEVENT\nDURATION:PT23H59M59S\nDTSTART:20260405T000000\nEND:VEVENT\n"}}, {"type": "TIME_PERIOD", "id": "OS2SNMJHDV6GYORYXGKO3G4E", "updated_at": "2025-04-23T11:41:12.307Z", "created_at": "2024-11-17T11:09:31.092Z", "version": 1745408472307, "is_deleted": false, "present_at_all_locations": true, "time_period_data": {"event": "BEGIN:VEVENT\nDURATION:PT23H59M59S\nDTSTART:20260406T000000\nEND:VEVENT\n"}}, {"type": "TIME_PERIOD", "id": "GPDZN2DCYQK5NY3MSEKOLYAK", "updated_at": "2025-04-26T13:00:28.348Z", "created_at": "2024-11-17T11:09:31.092Z", "version": 1745672428348, "is_deleted": false, "present_at_all_locations": true, "time_period_data": {"event": "BEGIN:VEVENT\nDURATION:PT23H59M59S\nDTSTART:20260425T000000\nEND:VEVENT\n"}}, {"type": "TIME_PERIOD", "id": "LR3S2RSQSWTXLTKZ32D6FQLU", "updated_at": "2024-11-17T11:09:31.055Z", "created_at": "2024-11-17T11:09:31.092Z", "version": 1731841771055, "is_deleted": false, "present_at_all_locations": true, "time_period_data": {"event": "BEGIN:VEVENT\nDURATION:PT23H59M59S\nDTSTART:20250609T000000\nEND:VEVENT\n"}}, {"type": "TIME_PERIOD", "id": "5HP2RRXLKH2M47XXEDPDYVEO", "updated_at": "2024-11-17T11:09:31.055Z", "created_at": "2024-11-17T11:09:31.092Z", "version": 1731841771055, "is_deleted": false, "present_at_all_locations": true, "time_period_data": {"event": "BEGIN:VEVENT\nDURATION:PT23H59M59S\nDTSTART:20250926T000000\nEND:VEVENT\n"}}, {"type": "TIME_PERIOD", "id": "6QXVPS6W6IPOCHAHHG4M5ZXZ", "updated_at": "2024-11-17T11:09:31.055Z", "created_at": "2024-11-17T11:09:31.092Z", "version": 1731841771055, "is_deleted": false, "present_at_all_locations": true, "time_period_data": {"event": "BEGIN:VEVENT\nDURATION:PT23H59M59S\nDTSTART:20251104T000000\nEND:VEVENT\n"}}, {"type": "CUSTOM_ATTRIBUTE_DEFINITION", "id": "QCB37SZZLVAJFYUBV2YA6WB2", "updated_at": "2024-07-19T02:25:33.766Z", "created_at": "2024-07-19T02:25:33.804Z", "version": 1721355933766, "is_deleted": false, "present_at_all_locations": true, "custom_attribute_definition_data": {"type": "BOOLEAN", "name": "Is Alcoholic", "description": "Enabling this toggle on an item indicates that it contains alcohol.", "source_application": {"application_id": "sq0idp-w46nJ_NCNDMSOywaCY0mwA", "name": "Square Online Store"}, "allowed_object_types": ["ITEM"], "seller_visibility": "SELLER_VISIBILITY_READ_WRITE_VALUES", "app_visibility": "APP_VISIBILITY_HIDDEN", "key": "is_alcoholic"}}, {"type": "CUSTOM_ATTRIBUTE_DEFINITION", "id": "X5QTBATBX7TNB5PZCF4YJLF2", "updated_at": "2024-07-19T02:25:33.953Z", "created_at": "2024-07-19T02:25:33.991Z", "version": 1721355933953, "is_deleted": false, "present_at_all_locations": true, "custom_attribute_definition_data": {"type": "STRING", "name": "Ecom Storefront Classic Site ID", "description": "Ecommerce bridge target storefront classic site ID. Used to create site-item associations after copying items.", "source_application": {"application_id": "sq0idp-w46nJ_NCNDMSOywaCY0mwA", "name": "Square Online Store"}, "allowed_object_types": ["ITEM"], "seller_visibility": "SELLER_VISIBILITY_HIDDEN", "app_visibility": "APP_VISIBILITY_HIDDEN", "string_config": {"enforce_uniqueness": false}, "key": "ecom_target_classic_site_id"}}, {"type": "CUSTOM_ATTRIBUTE_DEFINITION", "id": "LKGJK63FJT3DUX7KFOYT7G4L", "updated_at": "2024-10-19T06:45:56.646Z", "created_at": "2024-10-19T06:45:56.685Z", "version": 1729320356646, "is_deleted": false, "present_at_all_locations": true, "custom_attribute_definition_data": {"type": "BOOLEAN", "name": "Ecom Gifting Enabled", "description": "Enables the item to be sold as a gift on Square Online.", "source_application": {"application_id": "sq0idp-w46nJ_NCNDMSOywaCY0mwA", "name": "Square Online Store"}, "allowed_object_types": ["ITEM"], "seller_visibility": "SELLER_VISIBILITY_READ_WRITE_VALUES", "app_visibility": "APP_VISIBILITY_READ_WRITE_VALUES", "key": "ecom_gifting_enabled"}}, {"type": "QUICK_AMOUNTS_SETTINGS", "id": "UHYYUDUQMO47BPZRGFPBW5CE", "updated_at": "2024-10-20T06:27:02.246Z", "created_at": "2024-10-20T06:27:02.277Z", "version": 1729405622246, "is_deleted": false, "present_at_all_locations": false, "present_at_location_ids": ["LXRJ28YWPJNBZ"], "quick_amounts_settings_data": {"option": "DISABLED", "eligible_for_auto_amounts": false}}], "latest_time": "2025-05-23T09:07:09.264Z"}