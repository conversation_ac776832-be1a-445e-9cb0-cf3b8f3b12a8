-- Square Config Table RLS Policy Setup
-- This script creates proper RLS policies for the square_config table
-- allowing admin users to manage Square API configuration

-- First, ensure the square_config table exists with proper structure
CREATE TABLE IF NOT EXISTS public.square_config (
    id INTEGER PRIMARY KEY DEFAULT 1,
    access_token TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add a constraint to ensure only one config record exists
ALTER TABLE public.square_config 
ADD CONSTRAINT square_config_single_row 
CHECK (id = 1);

-- Enable RLS on the square_config table
ALTER TABLE public.square_config ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (for clean setup)
DROP POLICY IF EXISTS "Admin users can manage square_config" ON public.square_config;
DROP POLICY IF EXISTS "Admin users can read square_config" ON public.square_config;
DROP POLICY IF EXISTS "Admin users can insert square_config" ON public.square_config;
DROP POLICY IF EXISTS "Admin users can update square_config" ON public.square_config;
DROP POLICY IF EXISTS "Admin users can delete square_config" ON public.square_config;

-- Create comprehensive RLS policy for admin users
-- This policy allows admin users to perform all operations (SELECT, INSERT, UPDATE, DELETE)
CREATE POLICY "Admin users can manage square_config" 
ON public.square_config 
FOR ALL 
TO authenticated 
USING (
    -- Check if the current user has admin role
    EXISTS (
        SELECT 1 
        FROM public.users 
        WHERE users.auth_id = auth.uid() 
        AND users.role = 'admin'
    )
) 
WITH CHECK (
    -- Same check for INSERT/UPDATE operations
    EXISTS (
        SELECT 1 
        FROM public.users 
        WHERE users.auth_id = auth.uid() 
        AND users.role = 'admin'
    )
);

-- Alternative: Separate policies for different operations (more granular control)
-- Uncomment these if you prefer separate policies instead of the single policy above

/*
-- Policy for reading square_config (admin only)
CREATE POLICY "Admin users can read square_config" 
ON public.square_config 
FOR SELECT 
TO authenticated 
USING (
    EXISTS (
        SELECT 1 
        FROM public.users 
        WHERE users.auth_id = auth.uid() 
        AND users.role = 'admin'
    )
);

-- Policy for inserting square_config (admin only)
CREATE POLICY "Admin users can insert square_config" 
ON public.square_config 
FOR INSERT 
TO authenticated 
WITH CHECK (
    EXISTS (
        SELECT 1 
        FROM public.users 
        WHERE users.auth_id = auth.uid() 
        AND users.role = 'admin'
    )
);

-- Policy for updating square_config (admin only)
CREATE POLICY "Admin users can update square_config" 
ON public.square_config 
FOR UPDATE 
TO authenticated 
USING (
    EXISTS (
        SELECT 1 
        FROM public.users 
        WHERE users.auth_id = auth.uid() 
        AND users.role = 'admin'
    )
) 
WITH CHECK (
    EXISTS (
        SELECT 1 
        FROM public.users 
        WHERE users.auth_id = auth.uid() 
        AND users.role = 'admin'
    )
);

-- Policy for deleting square_config (admin only)
CREATE POLICY "Admin users can delete square_config" 
ON public.square_config 
FOR DELETE 
TO authenticated 
USING (
    EXISTS (
        SELECT 1 
        FROM public.users 
        WHERE users.auth_id = auth.uid() 
        AND users.role = 'admin'
    )
);
*/

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON public.square_config TO authenticated;

-- Create an index for better performance on the users table lookup
CREATE INDEX IF NOT EXISTS idx_users_auth_id_role ON public.users(auth_id, role);

-- Insert a comment for documentation
COMMENT ON TABLE public.square_config IS 'Stores Square API configuration. Only admin users can manage this table.';
COMMENT ON COLUMN public.square_config.id IS 'Primary key, always 1 (single config record)';
COMMENT ON COLUMN public.square_config.access_token IS 'Square API access token';
COMMENT ON COLUMN public.square_config.updated_at IS 'Last update timestamp';
COMMENT ON COLUMN public.square_config.created_at IS 'Creation timestamp';

-- Verify the setup
SELECT 
    schemaname,
    tablename,
    rowsecurity as "RLS Enabled"
FROM pg_tables 
WHERE tablename = 'square_config';

SELECT 
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'square_config';
