-- Check Square IDs for Items
-- Run this in Supabase SQL Editor to see which items have Square IDs

-- ============================================================================
-- 1. Show all items with their Square IDs
-- ============================================================================
SELECT 
    'ITEMS WITH SQUARE IDS' as section,
    id as local_item_id,
    name,
    vietnamese_name,
    square_item_id,
    category,
    created_at
FROM items 
WHERE square_item_id IS NOT NULL
ORDER BY name;

-- ============================================================================
-- 2. Show items WITHOUT Square IDs (need to be linked)
-- ============================================================================
SELECT 
    'ITEMS WITHOUT SQUARE IDS' as section,
    id as local_item_id,
    name,
    vietnamese_name,
    category,
    'Needs Square ID' as status
FROM items 
WHERE square_item_id IS NULL
ORDER BY name;

-- ============================================================================
-- 3. Count summary
-- ============================================================================
SELECT 
    'SUMMARY' as section,
    COUNT(*) as total_items,
    COUNT(square_item_id) as items_with_square_id,
    COUNT(*) - COUNT(square_item_id) as items_without_square_id,
    ROUND(
        (COUNT(square_item_id)::DECIMAL / COUNT(*)) * 100, 
        2
    ) as percentage_linked
FROM items;

-- ============================================================================
-- 4. Show specific item for Charcoal Pork Chop (Cơm sườn heo)
-- ============================================================================
SELECT 
    'CHARCOAL PORK CHOP' as section,
    id as local_item_id,
    name,
    vietnamese_name,
    square_item_id,
    category,
    CASE 
        WHEN square_item_id IS NOT NULL THEN 'Has Square ID'
        ELSE 'Missing Square ID'
    END as status
FROM items 
WHERE 
    name ILIKE '%pork%' 
    OR name ILIKE '%sườn%'
    OR vietnamese_name ILIKE '%sườn%'
    OR name ILIKE '%charcoal%'
ORDER BY name;
