-- Quick RLS Fix for Restaurant Inventory System
-- Run this in your Supabase SQL Editor to fix immediate RLS issues

-- ============================================================================
-- STEP 1: Enable RLS on all tables (safe to run multiple times)
-- ============================================================================

ALTER TABLE IF EXISTS users ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS storage ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS inventory_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS items ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS item_variants ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS item_ingredients ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS non_inventory_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS productions ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS config ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 2: Handle square_config table
-- ============================================================================

-- Create square_config table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.square_config (
    id INTEGER PRIMARY KEY DEFAULT 1,
    access_token TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on square_config
ALTER TABLE IF EXISTS square_config ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 3: Recreate helper functions (in case they're missing)
-- ============================================================================

-- Helper function to get current user's role
CREATE OR REPLACE FUNCTION get_user_role(user_auth_id UUID)
RETURNS user_role AS $$
BEGIN
  RETURN (
    SELECT role 
    FROM users 
    WHERE auth_id = user_auth_id AND is_active = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is admin or manager
CREATE OR REPLACE FUNCTION is_admin_or_manager(user_auth_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN get_user_role(user_auth_id) IN ('admin', 'manager');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- STEP 4: Fix square_config policies
-- ============================================================================

-- Drop existing square_config policies (if any)
DROP POLICY IF EXISTS "Admin users can manage square_config" ON square_config;

-- Create new policy for square_config
CREATE POLICY "Admin users can manage square_config" 
ON square_config 
FOR ALL 
TO authenticated 
USING (
    EXISTS (
        SELECT 1 
        FROM users 
        WHERE users.auth_id = auth.uid() 
        AND users.role = 'admin'
    )
) 
WITH CHECK (
    EXISTS (
        SELECT 1 
        FROM users 
        WHERE users.auth_id = auth.uid() 
        AND users.role = 'admin'
    )
);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON square_config TO authenticated;

-- ============================================================================
-- STEP 5: Verify the fix
-- ============================================================================

-- Check RLS status
SELECT 
    tablename,
    rowsecurity as "RLS Enabled"
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('users', 'inventory_items', 'items', 'square_config')
ORDER BY tablename;

-- Check if policies exist
SELECT 
    tablename,
    COUNT(*) as policy_count
FROM pg_policies 
WHERE schemaname = 'public'
AND tablename IN ('users', 'inventory_items', 'items', 'square_config')
GROUP BY tablename
ORDER BY tablename;

SELECT 'Quick RLS fix completed!' as status;
