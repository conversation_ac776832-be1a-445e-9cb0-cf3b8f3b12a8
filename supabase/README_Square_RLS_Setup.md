# Square Config RLS Policy Setup

This directory contains SQL scripts to set up Row Level Security (RLS) policies for the `square_config` table, allowing only admin users to manage Square API configuration.

## Quick Setup (Recommended)

### Step 1: Run the Quick Setup Script

1. **Go to your Supabase Dashboard**
   - Navigate to: https://supabase.com/dashboard/project/ayzppqpygicvbptdqrnt
   - Click on **SQL Editor** in the left sidebar

2. **Execute the Quick Setup**
   - Copy the contents of `quick_square_rls_setup.sql`
   - Paste it into the SQL Editor
   - Click **Run** to execute

### Step 2: Verify Your User is Admin

Make sure your current user has admin role:

```sql
-- Check your current user's role
SELECT 
    id,
    email,
    role,
    auth_id
FROM public.users 
WHERE auth_id = auth.uid();
```

If your user doesn't exist or doesn't have admin role, update it:

```sql
-- Update your user to admin role (replace with your actual auth_id)
UPDATE public.users 
SET role = 'admin' 
WHERE auth_id = auth.uid();

-- Or insert if user doesn't exist (replace with your email)
INSERT INTO public.users (email, role, auth_id)
VALUES ('<EMAIL>', 'admin', auth.uid())
ON CONFLICT (auth_id) DO UPDATE SET role = 'admin';
```

## What This Does

### 1. Enables RLS
- Turns on Row Level Security for the `square_config` table
- Prevents unauthorized access to Square API configuration

### 2. Creates Admin-Only Policy
- **Policy Name**: "Admin users can manage square_config"
- **Permissions**: SELECT, INSERT, UPDATE, DELETE
- **Restriction**: Only users with `role = 'admin'` in the `users` table

### 3. Security Model
```
User Authentication (Supabase Auth)
         ↓
User Record (public.users table)
         ↓
Role Check (role = 'admin')
         ↓
Square Config Access (if admin)
```

## Testing the Setup

### Test 1: Admin User Access
```sql
-- This should work if you're an admin
SELECT * FROM public.square_config;
```

### Test 2: Insert/Update Access
```sql
-- This should work if you're an admin
INSERT INTO public.square_config (id, access_token) 
VALUES (1, 'test_token_123')
ON CONFLICT (id) DO UPDATE SET 
    access_token = EXCLUDED.access_token,
    updated_at = NOW();
```

### Test 3: Verify Policy
```sql
-- Check that the policy exists
SELECT 
    policyname,
    permissive,
    roles,
    cmd
FROM pg_policies 
WHERE tablename = 'square_config';
```

## Troubleshooting

### Issue: "new row violates row-level security policy"
**Solution**: Make sure your user has admin role:
```sql
UPDATE public.users SET role = 'admin' WHERE auth_id = auth.uid();
```

### Issue: "relation does not exist"
**Solution**: The `square_config` table might not exist. Create it:
```sql
CREATE TABLE IF NOT EXISTS public.square_config (
    id INTEGER PRIMARY KEY DEFAULT 1,
    access_token TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Issue: Policy not working
**Solution**: Check if RLS is enabled:
```sql
SELECT tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'square_config';
```

## Alternative: Disable RLS (Less Secure)

If you want to temporarily disable RLS for testing:

```sql
-- CAUTION: This removes all security restrictions
ALTER TABLE public.square_config DISABLE ROW LEVEL SECURITY;
```

## Files in This Directory

- **`quick_square_rls_setup.sql`** - Minimal setup script (recommended)
- **`square_config_rls_policy.sql`** - Complete setup with table creation and documentation
- **`README_Square_RLS_Setup.md`** - This instruction file

## Next Steps

After running the setup:

1. **Test Square Config Save** - Try saving a Square access token in the app
2. **Verify Console Logs** - Should see success messages instead of RLS errors
3. **Check Functionality** - Square integration should work for admin users

The application will now properly handle Square API configuration for admin users!
