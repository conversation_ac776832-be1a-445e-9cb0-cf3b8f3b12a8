-- Comprehensive RLS Fix Script for Restaurant Inventory System
-- This script diagnoses and fixes all RLS issues across all tables
-- Run this in your Supabase SQL Editor

-- ============================================================================
-- PART 1: DIAGNOSTIC - Check current RLS status
-- ============================================================================

-- Check which tables have RLS enabled
SELECT 
    schemaname,
    tablename,
    rowsecurity as "RLS Enabled"
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY tablename;

-- Check existing policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- ============================================================================
-- PART 2: ENABLE RLS ON ALL TABLES
-- ============================================================================

-- Enable RLS on core tables (safe to run multiple times)
ALTER TABLE IF EXISTS users ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS storage ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS inventory_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS items ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS item_variants ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS item_ingredients ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS non_inventory_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS productions ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS config ENABLE ROW LEVEL SECURITY;

-- Enable RLS on square_config table (if it exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'square_config' AND table_schema = 'public') THEN
        ALTER TABLE square_config ENABLE ROW LEVEL SECURITY;
        RAISE NOTICE 'RLS enabled on square_config table';
    ELSE
        RAISE NOTICE 'square_config table does not exist';
    END IF;
END $$;

-- ============================================================================
-- PART 3: CREATE/RECREATE HELPER FUNCTIONS
-- ============================================================================

-- Helper function to get current user's role
CREATE OR REPLACE FUNCTION get_user_role(user_auth_id UUID)
RETURNS user_role AS $$
BEGIN
  RETURN (
    SELECT role 
    FROM users 
    WHERE auth_id = user_auth_id AND is_active = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is admin or manager
CREATE OR REPLACE FUNCTION is_admin_or_manager(user_auth_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN get_user_role(user_auth_id) IN ('admin', 'manager');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- PART 4: SQUARE CONFIG TABLE SETUP AND POLICIES
-- ============================================================================

-- Create square_config table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.square_config (
    id INTEGER PRIMARY KEY DEFAULT 1,
    access_token TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add constraint to ensure only one config record exists
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'square_config_single_row' 
        AND table_name = 'square_config'
    ) THEN
        ALTER TABLE public.square_config 
        ADD CONSTRAINT square_config_single_row 
        CHECK (id = 1);
    END IF;
END $$;

-- Enable RLS on square_config
ALTER TABLE public.square_config ENABLE ROW LEVEL SECURITY;

-- Drop existing square_config policies
DROP POLICY IF EXISTS "Admin users can manage square_config" ON public.square_config;
DROP POLICY IF EXISTS "Admin users can read square_config" ON public.square_config;
DROP POLICY IF EXISTS "Admin users can insert square_config" ON public.square_config;
DROP POLICY IF EXISTS "Admin users can update square_config" ON public.square_config;
DROP POLICY IF EXISTS "Admin users can delete square_config" ON public.square_config;

-- Create comprehensive RLS policy for square_config
CREATE POLICY "Admin users can manage square_config" 
ON public.square_config 
FOR ALL 
TO authenticated 
USING (
    EXISTS (
        SELECT 1 
        FROM public.users 
        WHERE users.auth_id = auth.uid() 
        AND users.role = 'admin'
    )
) 
WITH CHECK (
    EXISTS (
        SELECT 1 
        FROM public.users 
        WHERE users.auth_id = auth.uid() 
        AND users.role = 'admin'
    )
);

-- Grant permissions on square_config
GRANT SELECT, INSERT, UPDATE, DELETE ON public.square_config TO authenticated;

-- ============================================================================
-- PART 5: VERIFY SETUP
-- ============================================================================

-- Create index for better performance on users table
CREATE INDEX IF NOT EXISTS idx_users_auth_id_role ON public.users(auth_id, role);

-- Final verification
SELECT 
    'RLS Status Check' as check_type,
    tablename,
    rowsecurity as "RLS Enabled"
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('users', 'inventory_items', 'items', 'transactions', 'square_config')
ORDER BY tablename;

-- Check policies count
SELECT 
    'Policy Count Check' as check_type,
    tablename,
    COUNT(*) as policy_count
FROM pg_policies 
WHERE schemaname = 'public'
GROUP BY tablename
ORDER BY tablename;

-- Success message
SELECT 'RLS setup completed successfully!' as status;
