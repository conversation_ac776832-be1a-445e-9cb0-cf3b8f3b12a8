-- Enable Row Level Security on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE storage ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE items ENABLE ROW LEVEL SECURITY;
ALTER TABLE item_variants ENABLE ROW LEVEL SECURITY;
ALTER TABLE item_ingredients ENABLE ROW LEVEL SECURITY;
ALTER TABLE non_inventory_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE productions ENABLE ROW LEVEL SECURITY;
ALTER TABLE alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE config ENABLE ROW LEVEL SECURITY;

-- Enable RLS on square_config table (if it exists)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'square_config') THEN
        ALTER TABLE square_config ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- Helper function to get current user's role
CREATE OR REPLACE FUNCTION get_user_role(user_auth_id UUID)
RETURNS user_role AS $$
BEGIN
  RETURN (
    SELECT role
    FROM users
    WHERE auth_id = user_auth_id AND is_active = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is admin or manager
CREATE OR REPLACE FUNCTION is_admin_or_manager(user_auth_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN get_user_role(user_auth_id) IN ('admin', 'manager');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Users table policies
CREATE POLICY "Users can view their own profile" ON users
  FOR SELECT USING (auth_id = auth.uid());

CREATE POLICY "Admins can view all users" ON users
  FOR SELECT USING (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Admins can insert users" ON users
  FOR INSERT WITH CHECK (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Admins can update users" ON users
  FOR UPDATE USING (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Users can update their own profile" ON users
  FOR UPDATE USING (auth_id = auth.uid());

-- Suppliers table policies
CREATE POLICY "All authenticated users can view suppliers" ON suppliers
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Admin and managers can manage suppliers" ON suppliers
  FOR ALL USING (is_admin_or_manager(auth.uid()));

-- Storage table policies
CREATE POLICY "All authenticated users can view storage" ON storage
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Admin and managers can manage storage" ON storage
  FOR ALL USING (is_admin_or_manager(auth.uid()));

-- Inventory items table policies
CREATE POLICY "All authenticated users can view inventory items" ON inventory_items
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Admin, managers, and kitchen can manage inventory items" ON inventory_items
  FOR ALL USING (get_user_role(auth.uid()) IN ('admin', 'manager', 'kitchen'));

CREATE POLICY "Staff can update inventory quantities" ON inventory_items
  FOR UPDATE USING (
    get_user_role(auth.uid()) = 'staff' AND
    auth.uid() IS NOT NULL
  );

-- Items table policies
CREATE POLICY "All authenticated users can view items" ON items
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Admin, managers, and kitchen can manage items" ON items
  FOR ALL USING (get_user_role(auth.uid()) IN ('admin', 'manager', 'kitchen'));

-- Item variants table policies
CREATE POLICY "All authenticated users can view item variants" ON item_variants
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Admin, managers, and kitchen can manage item variants" ON item_variants
  FOR ALL USING (get_user_role(auth.uid()) IN ('admin', 'manager', 'kitchen'));

-- Item ingredients table policies
CREATE POLICY "All authenticated users can view item ingredients" ON item_ingredients
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Admin, managers, and kitchen can manage item ingredients" ON item_ingredients
  FOR ALL USING (get_user_role(auth.uid()) IN ('admin', 'manager', 'kitchen'));

-- Non-inventory items table policies
CREATE POLICY "All authenticated users can view non-inventory items" ON non_inventory_items
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Admin, managers, and kitchen can manage non-inventory items" ON non_inventory_items
  FOR ALL USING (get_user_role(auth.uid()) IN ('admin', 'manager', 'kitchen'));

-- Transactions table policies
CREATE POLICY "All authenticated users can view transactions" ON transactions
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "All authenticated users can create transactions" ON transactions
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Admin and managers can update/delete transactions" ON transactions
  FOR ALL USING (is_admin_or_manager(auth.uid()));

-- Productions table policies
CREATE POLICY "All authenticated users can view productions" ON productions
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "All authenticated users can create productions" ON productions
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Admin and managers can update/delete productions" ON productions
  FOR ALL USING (is_admin_or_manager(auth.uid()));

-- Alerts table policies
CREATE POLICY "All authenticated users can view alerts" ON alerts
  FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "System can create alerts" ON alerts
  FOR INSERT WITH CHECK (true); -- System-generated alerts

CREATE POLICY "All authenticated users can mark alerts as read" ON alerts
  FOR UPDATE USING (auth.uid() IS NOT NULL);

CREATE POLICY "Admin and managers can delete alerts" ON alerts
  FOR DELETE USING (is_admin_or_manager(auth.uid()));

-- Config table policies
CREATE POLICY "Admin can manage config" ON config
  FOR ALL USING (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "All authenticated users can view config" ON config
  FOR SELECT USING (auth.uid() IS NOT NULL);

-- Square Config table policies (if table exists)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'square_config') THEN
        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "Admin users can manage square_config" ON square_config;
        DROP POLICY IF EXISTS "Admin users can read square_config" ON square_config;
        DROP POLICY IF EXISTS "Admin users can insert square_config" ON square_config;
        DROP POLICY IF EXISTS "Admin users can update square_config" ON square_config;
        DROP POLICY IF EXISTS "Admin users can delete square_config" ON square_config;

        -- Create comprehensive RLS policy for admin users
        EXECUTE 'CREATE POLICY "Admin users can manage square_config"
        ON square_config
        FOR ALL
        TO authenticated
        USING (
            EXISTS (
                SELECT 1
                FROM users
                WHERE users.auth_id = auth.uid()
                AND users.role = ''admin''
            )
        )
        WITH CHECK (
            EXISTS (
                SELECT 1
                FROM users
                WHERE users.auth_id = auth.uid()
                AND users.role = ''admin''
            )
        )';

        -- Grant necessary permissions
        GRANT SELECT, INSERT, UPDATE, DELETE ON square_config TO authenticated;

        -- Create index for better performance
        CREATE INDEX IF NOT EXISTS idx_users_auth_id_role ON users(auth_id, role);
    END IF;
END $$;
