-- Quick Square Config RLS Setup
-- Run this in your Supabase SQL Editor

-- Enable RLS and create admin-only policy for square_config table
ALTER TABLE public.square_config ENABLE ROW LEVEL SECURITY;

-- Drop existing policy if it exists
DROP POLICY IF EXISTS "Admin users can manage square_config" ON public.square_config;

-- Create policy allowing admin users to manage square_config
CREATE POLICY "Admin users can manage square_config" 
ON public.square_config 
FOR ALL 
TO authenticated 
USING (
    EXISTS (
        SELECT 1 
        FROM public.users 
        WHERE users.auth_id = auth.uid() 
        AND users.role = 'admin'
    )
) 
WITH CHECK (
    EXISTS (
        SELECT 1 
        FROM public.users 
        WHERE users.auth_id = auth.uid() 
        AND users.role = 'admin'
    )
);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.square_config TO authenticated;
