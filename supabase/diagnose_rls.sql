-- R<PERSON> Diagnostic Script
-- Run this first to see what's wrong with RLS setup

-- ============================================================================
-- Check which tables exist
-- ============================================================================
SELECT 'EXISTING TABLES' as section, table_name as tablename, table_schema as schemaname
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_type = 'BASE TABLE'
ORDER BY table_name;

-- ============================================================================
-- Check RLS status for all tables
-- ============================================================================
SELECT 'RLS STATUS' as section, tablename, rowsecurity as "RLS_Enabled"
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY tablename;

-- ============================================================================
-- Check existing policies
-- ============================================================================
SELECT 'EXISTING POLICIES' as section, tablename, policyname, cmd
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- ============================================================================
-- Check if helper functions exist
-- ============================================================================
SELECT 'HELPER FUNCTIONS' as section, routine_name, routine_type
FROM information_schema.routines
WHERE routine_schema = 'public'
AND routine_name IN ('get_user_role', 'is_admin_or_manager');

-- ============================================================================
-- Check user roles (sample)
-- ============================================================================
SELECT 'USER ROLES SAMPLE' as section, auth_id, role, is_active
FROM users
LIMIT 5;

-- ============================================================================
-- Test current user access
-- ============================================================================
SELECT 'CURRENT USER' as section, auth.uid() as current_auth_id;

-- Check if current user has a role
SELECT 'CURRENT USER ROLE' as section, role, is_active
FROM users
WHERE auth_id = auth.uid();
