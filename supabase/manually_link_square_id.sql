-- Manually Link Square IDs to Items
-- Replace the values below with actual IDs

-- ============================================================================
-- Template for manually linking Square IDs
-- ============================================================================

-- Example: Link Charcoal Pork Chop to its Square catalog ID
-- UPDATE items 
-- SET 
--     square_item_id = 'YOUR_SQUARE_CATALOG_ITEM_ID_HERE',
--     updated_at = NOW()
-- WHERE 
--     name ILIKE '%charcoal pork%' 
--     OR vietnamese_name ILIKE '%sườn heo%';

-- ============================================================================
-- Step-by-step process:
-- ============================================================================

-- 1. First, find your local item ID
SELECT 
    'FIND LOCAL ITEM' as step,
    id,
    name,
    vietnamese_name,
    square_item_id
FROM items 
WHERE 
    name ILIKE '%pork%' 
    OR vietnamese_name ILIKE '%sườn%'
ORDER BY name;

-- 2. Then update with the Square catalog ID
-- (Replace 'YOUR_LOCAL_ITEM_ID' and 'YOUR_SQUARE_CATALOG_ID' with actual values)

/*
UPDATE items 
SET 
    square_item_id = 'YOUR_SQUARE_CATALOG_ID',
    updated_at = NOW()
WHERE id = 'YOUR_LOCAL_ITEM_ID';
*/

-- 3. Verify the update
/*
SELECT 
    'VERIFY UPDATE' as step,
    id,
    name,
    square_item_id,
    updated_at
FROM items 
WHERE id = 'YOUR_LOCAL_ITEM_ID';
*/
