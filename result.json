{"orders": [{"id": "OGwPcuZotoJzNgPUXajOZceFUQbZY", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "476cb0ab-b674-4f19-b247-7edc7dbc7ace", "catalog_object_id": "ARS3TZD7Z3EG2LHDRK5BJ6GG", "catalog_version": 1743632309931, "quantity": "1", "name": "Charcoal chicken with broken rice (Cơm gà nướng)", "variation_name": "Regular", "base_price_money": {"amount": 1750, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 1750, "currency": "AUD"}, "total_tax_money": {"amount": 159, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1750, "currency": "AUD"}, "variation_total_price_money": {"amount": 1750, "currency": "AUD"}, "applied_taxes": [{"uid": "4402a11b-77c6-46db-a4b5-04e54e226337", "tax_uid": "4402a11b-77c6-46db-a4b5-04e54e226337", "applied_money": {"amount": 159, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "4402a11b-77c6-46db-a4b5-04e54e226337", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10.0", "type": "INCLUSIVE", "applied_money": {"amount": 159, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T13:42:42.053Z", "updated_at": "2025-05-25T13:42:44.000Z", "state": "COMPLETED", "version": 4, "total_tax_money": {"amount": 159, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1750, "currency": "AUD"}, "closed_at": "2025-05-25T13:42:42.527Z", "tenders": [{"id": "5ejBgGLEoFGEIPAvWYPh6mPfiZQZY", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "OGwPcuZotoJzNgPUXajOZceFUQbZY", "created_at": "2025-05-25T13:42:42Z", "amount_money": {"amount": 1750, "currency": "AUD"}, "type": "CASH", "cash_details": {"buyer_tendered_money": {"amount": 2000, "currency": "AUD"}, "change_back_money": {"amount": 250, "currency": "AUD"}}, "payment_id": "5ejBgGLEoFGEIPAvWYPh6mPfiZQZY"}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "net_amounts": {"total_money": {"amount": 1750, "currency": "AUD"}, "tax_money": {"amount": 159, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "12 Ta", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "kZ45afsm8aB9o97etVEbBJkfFJZZY", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "730ea6ee-41dd-42d5-bba6-8c0778c06aae", "catalog_object_id": "G36Z2AAEAGQFM3NPMBQGCBQN", "catalog_version": 1743632309931, "quantity": "1", "name": "Phở bò (<PERSON><PERSON>)", "variation_name": "Regular", "base_price_money": {"amount": 1850, "currency": "AUD"}, "gross_sales_money": {"amount": 1850, "currency": "AUD"}, "total_tax_money": {"amount": 168, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1850, "currency": "AUD"}, "variation_total_price_money": {"amount": 1850, "currency": "AUD"}, "applied_taxes": [{"uid": "764358d9-8494-476b-a6e4-cbcd3b50fb5a", "tax_uid": "764358d9-8494-476b-a6e4-cbcd3b50fb5a", "applied_money": {"amount": 168, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "764358d9-8494-476b-a6e4-cbcd3b50fb5a", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10.0", "type": "INCLUSIVE", "applied_money": {"amount": 168, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T13:02:08.198Z", "updated_at": "2025-05-25T13:02:09.000Z", "state": "COMPLETED", "version": 4, "total_tax_money": {"amount": 168, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1850, "currency": "AUD"}, "closed_at": "2025-05-25T13:02:08.678Z", "tenders": [{"id": "RKh4WRSseyhQNwBGfpUBfRHLXuXZY", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "kZ45afsm8aB9o97etVEbBJkfFJZZY", "created_at": "2025-05-25T13:02:08Z", "amount_money": {"amount": 1850, "currency": "AUD"}, "type": "CASH", "cash_details": {"buyer_tendered_money": {"amount": 2000, "currency": "AUD"}, "change_back_money": {"amount": 150, "currency": "AUD"}}, "payment_id": "RKh4WRSseyhQNwBGfpUBfRHLXuXZY"}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "net_amounts": {"total_money": {"amount": 1850, "currency": "AUD"}, "tax_money": {"amount": 168, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "4", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "GWi5zWaabQBXusLIIIdYfuDOOgaZY", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "104fe262-90ee-430a-a123-0b3f69086b72", "catalog_object_id": "XN26JPNMEXTCJJH6O7P4D6F5", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON> (Bún chả Hà Nội)", "variation_name": "Regular", "base_price_money": {"amount": 1850, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 1850, "currency": "AUD"}, "total_tax_money": {"amount": 168, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1850, "currency": "AUD"}, "variation_total_price_money": {"amount": 1850, "currency": "AUD"}, "applied_taxes": [{"uid": "63dd8021-84ef-40a9-bf1c-8c6537fc9362", "tax_uid": "63dd8021-84ef-40a9-bf1c-8c6537fc9362", "applied_money": {"amount": 168, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "7f1b48bf-f3d5-4781-a05a-ca471283232a", "catalog_object_id": "CPD7YUKPSBQCTRDYBLAV7EV4", "catalog_version": 1743632309931, "quantity": "1", "name": "Charcoal Pork Chop Combo", "variation_name": "Regular", "base_price_money": {"amount": 2250, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 2250, "currency": "AUD"}, "total_tax_money": {"amount": 205, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2250, "currency": "AUD"}, "variation_total_price_money": {"amount": 2250, "currency": "AUD"}, "applied_taxes": [{"uid": "63dd8021-84ef-40a9-bf1c-8c6537fc9362", "tax_uid": "63dd8021-84ef-40a9-bf1c-8c6537fc9362", "applied_money": {"amount": 205, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "00fb083d-0995-436d-ab21-05dbe41be187", "catalog_object_id": "7HDRRPGYGFTZ763Z3U2GBRZK", "catalog_version": 1743632309931, "quantity": "1", "name": "bò lá lốt", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "63dd8021-84ef-40a9-bf1c-8c6537fc9362", "tax_uid": "63dd8021-84ef-40a9-bf1c-8c6537fc9362", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "42e10411-bb13-4f5c-954c-e19bb964c091", "catalog_object_id": "C4XTLDOHSNQHGFDYW66NFDTI", "catalog_version": 1743632309931, "quantity": "2", "name": "Peach Iced Tea Orange & Lemongrass (Trà đào cam sả)", "variation_name": "Large", "base_price_money": {"amount": 850, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 1700, "currency": "AUD"}, "total_tax_money": {"amount": 154, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1700, "currency": "AUD"}, "variation_total_price_money": {"amount": 1700, "currency": "AUD"}, "applied_taxes": [{"uid": "63dd8021-84ef-40a9-bf1c-8c6537fc9362", "tax_uid": "63dd8021-84ef-40a9-bf1c-8c6537fc9362", "applied_money": {"amount": 154, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "63dd8021-84ef-40a9-bf1c-8c6537fc9362", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10.0", "type": "INCLUSIVE", "applied_money": {"amount": 659, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T13:01:04.173Z", "updated_at": "2025-05-25T13:01:05.000Z", "state": "COMPLETED", "version": 4, "total_tax_money": {"amount": 659, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 7250, "currency": "AUD"}, "closed_at": "2025-05-25T13:01:04.644Z", "tenders": [{"id": "Hh5DJBhJdxNoR4CB2pFzWUmry2HZY", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "GWi5zWaabQBXusLIIIdYfuDOOgaZY", "created_at": "2025-05-25T13:01:04Z", "amount_money": {"amount": 7250, "currency": "AUD"}, "type": "CASH", "cash_details": {"buyer_tendered_money": {"amount": 10000, "currency": "AUD"}, "change_back_money": {"amount": 2750, "currency": "AUD"}}, "payment_id": "Hh5DJBhJdxNoR4CB2pFzWUmry2HZY"}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "net_amounts": {"total_money": {"amount": 7250, "currency": "AUD"}, "tax_money": {"amount": 659, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "12", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "Kq7NXpkirj0v7ovdY7hkZhxeV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "e4de4acc-0e67-4c56-b2c2-8771ddb51485", "catalog_object_id": "ARS3TZD7Z3EG2LHDRK5BJ6GG", "catalog_version": 1743632309931, "quantity": "1", "name": "Charcoal chicken with broken rice (Cơm gà nướng)", "variation_name": "Regular", "base_price_money": {"amount": 1750, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 1750, "currency": "AUD"}, "total_tax_money": {"amount": 159, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1750, "currency": "AUD"}, "variation_total_price_money": {"amount": 1750, "currency": "AUD"}, "applied_taxes": [{"uid": "2ee1e435-416f-4ceb-b182-d538d5c4ed71", "tax_uid": "2ee1e435-416f-4ceb-b182-d538d5c4ed71", "applied_money": {"amount": 159, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "6250f43f-d207-4230-baa8-6e95f0f9d9c3", "catalog_object_id": "XN26JPNMEXTCJJH6O7P4D6F5", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON> (Bún chả Hà Nội)", "variation_name": "Regular", "base_price_money": {"amount": 1850, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 1850, "currency": "AUD"}, "total_tax_money": {"amount": 168, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1850, "currency": "AUD"}, "variation_total_price_money": {"amount": 1850, "currency": "AUD"}, "applied_taxes": [{"uid": "2ee1e435-416f-4ceb-b182-d538d5c4ed71", "tax_uid": "2ee1e435-416f-4ceb-b182-d538d5c4ed71", "applied_money": {"amount": 168, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "76a6af80-09b5-4804-8358-41f41266c2e5", "catalog_object_id": "C4XTLDOHSNQHGFDYW66NFDTI", "catalog_version": 1743632309931, "quantity": "2", "name": "Peach Iced Tea Orange & Lemongrass (Trà đào cam sả)", "variation_name": "Large", "base_price_money": {"amount": 850, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 1700, "currency": "AUD"}, "total_tax_money": {"amount": 155, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1700, "currency": "AUD"}, "variation_total_price_money": {"amount": 1700, "currency": "AUD"}, "applied_taxes": [{"uid": "2ee1e435-416f-4ceb-b182-d538d5c4ed71", "tax_uid": "2ee1e435-416f-4ceb-b182-d538d5c4ed71", "applied_money": {"amount": 155, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "2ee1e435-416f-4ceb-b182-d538d5c4ed71", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 482, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T12:55:24Z", "updated_at": "2025-05-25T12:55:28.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 482, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 5300, "currency": "AUD"}, "closed_at": "2025-05-25T12:55:28.000Z", "tenders": [{"id": "lUH9dYC9yJh97crRMhfkTPHyvaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "Kq7NXpkirj0v7ovdY7hkZhxeV", "created_at": "2025-05-25T12:56:38Z", "amount_money": {"amount": 5300, "currency": "AUD"}, "processing_fee_money": {"amount": 85, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "MASTERCARD", "last_4": "1150", "fingerprint": "sq-1-SGRE8OaVG3Mo_Yu3m4WpbYUq-w5Jh23np5WAGC-2f-Tc694Y26_Wubc5npQAHVmD9Q"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 5300, "currency": "AUD"}, "tax_money": {"amount": 482, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "6 Ta", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "WY1U5p45dhCB2tF5uwp2fk1eV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "73d44b69-afa0-4822-bd7c-5ac37b3fe0c8", "catalog_object_id": "U7TVTKLJ4Z5FWXIHD7MLX2LL", "catalog_version": 1747462207006, "quantity": "1", "name": "<PERSON><PERSON> chua r<PERSON>", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "585883c3-c7aa-46d3-bc96-95f802e1f729", "tax_uid": "585883c3-c7aa-46d3-bc96-95f802e1f729", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "585883c3-c7aa-46d3-bc96-95f802e1f729", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 132, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T12:51:46Z", "updated_at": "2025-05-25T12:51:49.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "closed_at": "2025-05-25T12:51:49.000Z", "tenders": [{"id": "HprsUwsNgFsWKEdNgrSijTzGvaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "WY1U5p45dhCB2tF5uwp2fk1eV", "created_at": "2025-05-25T12:53:00Z", "amount_money": {"amount": 1450, "currency": "AUD"}, "processing_fee_money": {"amount": 23, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "EFTPOS", "last_4": "0737", "fingerprint": "sq-1-9JTdv2qkygDacyqVi-S1i8yGiM2jmg_ythKXI8jub5M90oL3XszIUQmefLh1WP1ZeQ"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 1450, "currency": "AUD"}, "tax_money": {"amount": 132, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "4", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "0J49HwNy9rEMNxl4E60Q4cweV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "6e516177-453c-4c10-9d9b-4ca02b0c59fd", "catalog_object_id": "CPD7YUKPSBQCTRDYBLAV7EV4", "catalog_version": 1743632309931, "quantity": "1", "name": "Charcoal Pork Chop Combo", "variation_name": "Regular", "base_price_money": {"amount": 2250, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 2250, "currency": "AUD"}, "total_tax_money": {"amount": 205, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2250, "currency": "AUD"}, "variation_total_price_money": {"amount": 2250, "currency": "AUD"}, "applied_taxes": [{"uid": "c73397cc-4aea-4287-a6f9-72deef146959", "tax_uid": "c73397cc-4aea-4287-a6f9-72deef146959", "applied_money": {"amount": 205, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "c73397cc-4aea-4287-a6f9-72deef146959", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 205, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T12:35:02Z", "updated_at": "2025-05-25T12:35:06.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 205, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2250, "currency": "AUD"}, "closed_at": "2025-05-25T12:35:06.000Z", "tenders": [{"id": "D1oXkYOHpV4IgGaRFqVAHbVauaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "0J49HwNy9rEMNxl4E60Q4cweV", "created_at": "2025-05-25T12:36:16Z", "amount_money": {"amount": 2250, "currency": "AUD"}, "processing_fee_money": {"amount": 36, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "MASTERCARD", "last_4": "8850", "fingerprint": "sq-1-m9SVAWOMDwmHXCqLDNMJgTKFFQ6RHyyIdM4tilHM3FHD3j07Ef23DMoMlMK_TUNipA"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 2250, "currency": "AUD"}, "tax_money": {"amount": 205, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "12", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "GCkRyFGnKRMn6x4hJatu1lseV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "5b0bea1d-a171-4d81-b1ea-ed33f9ade47f", "catalog_object_id": "CPD7YUKPSBQCTRDYBLAV7EV4", "catalog_version": 1743632309931, "quantity": "1", "name": "Charcoal Pork Chop Combo", "variation_name": "Regular", "base_price_money": {"amount": 2250, "currency": "AUD"}, "note": "it com", "gross_sales_money": {"amount": 2250, "currency": "AUD"}, "total_tax_money": {"amount": 205, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2250, "currency": "AUD"}, "variation_total_price_money": {"amount": 2250, "currency": "AUD"}, "applied_taxes": [{"uid": "f4b284eb-1863-4e68-a7e2-0001ba718a3f", "tax_uid": "f4b284eb-1863-4e68-a7e2-0001ba718a3f", "applied_money": {"amount": 205, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "f4b284eb-1863-4e68-a7e2-0001ba718a3f", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 205, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T12:34:30Z", "updated_at": "2025-05-25T12:34:34.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 205, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2250, "currency": "AUD"}, "closed_at": "2025-05-25T12:34:34.000Z", "tenders": [{"id": "j1yUGe1OEJHvlnciu58tgzDCuaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "GCkRyFGnKRMn6x4hJatu1lseV", "created_at": "2025-05-25T12:35:44Z", "amount_money": {"amount": 2250, "currency": "AUD"}, "processing_fee_money": {"amount": 36, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "MASTERCARD", "last_4": "1573", "fingerprint": "sq-1-7BCKk7GhdZ5Ebbg-aEiR3aN1NncfnsWn1RXPbeP0_D0puRVwx7fUUdvl8XrXsMKu2w"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 2250, "currency": "AUD"}, "tax_money": {"amount": 205, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "8", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "MvHgQm9qGLdnzwSaZjBnJCoTl8YZY", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "07c3bc93-fead-4dd8-a2e5-4a8589d7f7df", "catalog_object_id": "CPD7YUKPSBQCTRDYBLAV7EV4", "catalog_version": 1743632309931, "quantity": "3", "name": "Charcoal Pork Chop Combo", "variation_name": "Regular", "base_price_money": {"amount": 2250, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 6750, "currency": "AUD"}, "total_tax_money": {"amount": 614, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 6750, "currency": "AUD"}, "variation_total_price_money": {"amount": 6750, "currency": "AUD"}, "applied_taxes": [{"uid": "6dde77b9-f6e6-4918-a9fd-48c71ca2860b", "tax_uid": "6dde77b9-f6e6-4918-a9fd-48c71ca2860b", "applied_money": {"amount": 614, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "c3856eb8-db5a-4381-9adb-d3191a1478f4", "catalog_object_id": "XN26JPNMEXTCJJH6O7P4D6F5", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON> (Bún chả Hà Nội)", "variation_name": "Regular", "base_price_money": {"amount": 1850, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 1850, "currency": "AUD"}, "total_tax_money": {"amount": 168, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1850, "currency": "AUD"}, "variation_total_price_money": {"amount": 1850, "currency": "AUD"}, "applied_taxes": [{"uid": "6dde77b9-f6e6-4918-a9fd-48c71ca2860b", "tax_uid": "6dde77b9-f6e6-4918-a9fd-48c71ca2860b", "applied_money": {"amount": 168, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "e7bda8b0-7820-40fc-8646-fbdadc74372c", "catalog_object_id": "XN26JPNMEXTCJJH6O7P4D6F5", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON> (Bún chả Hà Nội)", "variation_name": "Regular", "base_price_money": {"amount": 1850, "currency": "AUD"}, "note": "takew", "gross_sales_money": {"amount": 1850, "currency": "AUD"}, "total_tax_money": {"amount": 168, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1850, "currency": "AUD"}, "variation_total_price_money": {"amount": 1850, "currency": "AUD"}, "applied_taxes": [{"uid": "6dde77b9-f6e6-4918-a9fd-48c71ca2860b", "tax_uid": "6dde77b9-f6e6-4918-a9fd-48c71ca2860b", "applied_money": {"amount": 168, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "eee66ed2-09d4-498c-b8d3-c1f833f1a67d", "catalog_object_id": "UXFBOISXB5RDGIXJAHVD3WIW", "catalog_version": 1743632309931, "quantity": "1", "name": "Coke", "variation_name": "Regular", "base_price_money": {"amount": 350, "currency": "AUD"}, "gross_sales_money": {"amount": 350, "currency": "AUD"}, "total_tax_money": {"amount": 32, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 350, "currency": "AUD"}, "variation_total_price_money": {"amount": 350, "currency": "AUD"}, "applied_taxes": [{"uid": "6dde77b9-f6e6-4918-a9fd-48c71ca2860b", "tax_uid": "6dde77b9-f6e6-4918-a9fd-48c71ca2860b", "applied_money": {"amount": 32, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "6dde77b9-f6e6-4918-a9fd-48c71ca2860b", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10.0", "type": "INCLUSIVE", "applied_money": {"amount": 982, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T12:25:21.984Z", "updated_at": "2025-05-25T12:25:23.000Z", "state": "COMPLETED", "version": 4, "total_tax_money": {"amount": 982, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 10800, "currency": "AUD"}, "closed_at": "2025-05-25T12:25:22.431Z", "tenders": [{"id": "10BQj6HyNmopdK0jYF7qZ3LXLvRZY", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "MvHgQm9qGLdnzwSaZjBnJCoTl8YZY", "created_at": "2025-05-25T12:25:22Z", "amount_money": {"amount": 10800, "currency": "AUD"}, "type": "CASH", "cash_details": {"buyer_tendered_money": {"amount": 12000, "currency": "AUD"}, "change_back_money": {"amount": 1200, "currency": "AUD"}}, "payment_id": "10BQj6HyNmopdK0jYF7qZ3LXLvRZY"}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "net_amounts": {"total_money": {"amount": 10800, "currency": "AUD"}, "tax_money": {"amount": 982, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "4", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "81CqqLUyd9YYOffAMzljh4eeV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "67e0aa11-a0d7-4465-ba22-e1f8ad1adfa5", "catalog_object_id": "WLOA62RODZIMIB6RZPJBUNEY", "catalog_version": 1743632309931, "quantity": "1", "name": "b<PERSON>h tr<PERSON>g n<PERSON> ( 2pcs Grilled Rice Paper Rolls)", "variation_name": "Regular", "base_price_money": {"amount": 1790, "currency": "AUD"}, "gross_sales_money": {"amount": 1790, "currency": "AUD"}, "total_tax_money": {"amount": 163, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1790, "currency": "AUD"}, "variation_total_price_money": {"amount": 1790, "currency": "AUD"}, "applied_taxes": [{"uid": "378cb018-9a22-4c02-a9a2-5203a778ea5b", "tax_uid": "378cb018-9a22-4c02-a9a2-5203a778ea5b", "applied_money": {"amount": 163, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "92a60ad3-b153-4f77-a43a-a67c53ad890f", "catalog_object_id": "N7NLNPFHF5ZLSD3KQVWFKIVY", "catalog_version": 1743632309931, "quantity": "1", "name": "4 pcs Prawn Spring Rolls (Chả giò tôm)", "variation_name": "Regular", "base_price_money": {"amount": 1050, "currency": "AUD"}, "gross_sales_money": {"amount": 1050, "currency": "AUD"}, "total_tax_money": {"amount": 95, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1050, "currency": "AUD"}, "variation_total_price_money": {"amount": 1050, "currency": "AUD"}, "applied_taxes": [{"uid": "378cb018-9a22-4c02-a9a2-5203a778ea5b", "tax_uid": "378cb018-9a22-4c02-a9a2-5203a778ea5b", "applied_money": {"amount": 95, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "378cb018-9a22-4c02-a9a2-5203a778ea5b", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 258, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T12:10:40Z", "updated_at": "2025-05-25T12:10:43.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 258, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2840, "currency": "AUD"}, "closed_at": "2025-05-25T12:10:43.000Z", "tenders": [{"id": "jlmI9oxt6fnxAQIgGCRhL977taB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "81CqqLUyd9YYOffAMzljh4eeV", "created_at": "2025-05-25T12:11:54Z", "amount_money": {"amount": 2840, "currency": "AUD"}, "processing_fee_money": {"amount": 45, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "EFTPOS", "last_4": "5676", "fingerprint": "sq-1-8oQA1H3moGWCSUN0zE3fueDFd6sFjdQcbPznhzW8QN6FolNnatpYqMwUI2Sp1XiKtQ"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 2840, "currency": "AUD"}, "tax_money": {"amount": 258, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "8", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "uOGSh1aYvazJu39x3J7YOh2eV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "10d897ec-04e5-4fd5-8afd-77b190031373", "catalog_object_id": "CPD7YUKPSBQCTRDYBLAV7EV4", "catalog_version": 1743632309931, "quantity": "1", "name": "Charcoal Pork Chop Combo", "variation_name": "Regular", "base_price_money": {"amount": 2250, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 2250, "currency": "AUD"}, "total_tax_money": {"amount": 205, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2250, "currency": "AUD"}, "variation_total_price_money": {"amount": 2250, "currency": "AUD"}, "applied_taxes": [{"uid": "b79956bb-a381-4e47-9c9c-400ba99a4f46", "tax_uid": "b79956bb-a381-4e47-9c9c-400ba99a4f46", "applied_money": {"amount": 205, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "588c175c-643a-40e3-8479-b8d2291151e0", "catalog_object_id": "ARS3TZD7Z3EG2LHDRK5BJ6GG", "catalog_version": 1743632309931, "quantity": "1", "name": "Charcoal chicken with broken rice (Cơm gà nướng)", "variation_name": "Regular", "base_price_money": {"amount": 1750, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 1750, "currency": "AUD"}, "total_tax_money": {"amount": 159, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1750, "currency": "AUD"}, "variation_total_price_money": {"amount": 1750, "currency": "AUD"}, "applied_taxes": [{"uid": "b79956bb-a381-4e47-9c9c-400ba99a4f46", "tax_uid": "b79956bb-a381-4e47-9c9c-400ba99a4f46", "applied_money": {"amount": 159, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "b79956bb-a381-4e47-9c9c-400ba99a4f46", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 364, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T11:49:51Z", "updated_at": "2025-05-25T11:49:55.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 364, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 4000, "currency": "AUD"}, "closed_at": "2025-05-25T11:49:55.000Z", "tenders": [{"id": "jXfWUIvtAnGWPPpps0jzY9SsvaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "uOGSh1aYvazJu39x3J7YOh2eV", "created_at": "2025-05-25T11:51:05Z", "amount_money": {"amount": 4000, "currency": "AUD"}, "processing_fee_money": {"amount": 64, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "MASTERCARD", "last_4": "3921", "fingerprint": "sq-1-duXk9aTZsnk57Shua-uJh4q3BebsrW4Mlb14u_BfWKHTs-TJvho3MjO5i1G3ZurO4g"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 4000, "currency": "AUD"}, "tax_money": {"amount": 364, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "9", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "0HNotvZ6QjhIcpZt6OPPWmteV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "5e6cd605-6697-43b4-9e64-1a2619bf143d", "catalog_object_id": "CPD7YUKPSBQCTRDYBLAV7EV4", "catalog_version": 1743632309931, "quantity": "1", "name": "Charcoal Pork Chop Combo", "variation_name": "Regular", "base_price_money": {"amount": 2250, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 2250, "currency": "AUD"}, "total_tax_money": {"amount": 205, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2250, "currency": "AUD"}, "variation_total_price_money": {"amount": 2250, "currency": "AUD"}, "applied_taxes": [{"uid": "53c039f9-5ef9-4ce3-a57c-966893ea3e97", "tax_uid": "53c039f9-5ef9-4ce3-a57c-966893ea3e97", "applied_money": {"amount": 205, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "f82484ac-aaa7-4b83-a815-4562b4a2a885", "catalog_object_id": "DC4BFSABXOBNVJVWI2ETCSLI", "catalog_version": 1743632309931, "quantity": "1", "name": "5 pcs Pork Spring Rolls (<PERSON><PERSON> giò heo)", "variation_name": "Regular", "base_price_money": {"amount": 1050, "currency": "AUD"}, "gross_sales_money": {"amount": 1050, "currency": "AUD"}, "total_tax_money": {"amount": 95, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1050, "currency": "AUD"}, "variation_total_price_money": {"amount": 1050, "currency": "AUD"}, "applied_taxes": [{"uid": "53c039f9-5ef9-4ce3-a57c-966893ea3e97", "tax_uid": "53c039f9-5ef9-4ce3-a57c-966893ea3e97", "applied_money": {"amount": 95, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "5a8065a5-68e9-4cac-8241-efb54625722b", "catalog_object_id": "UXFBOISXB5RDGIXJAHVD3WIW", "catalog_version": 1743632309931, "quantity": "1", "name": "Coke", "variation_name": "Regular", "base_price_money": {"amount": 350, "currency": "AUD"}, "gross_sales_money": {"amount": 350, "currency": "AUD"}, "total_tax_money": {"amount": 32, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 350, "currency": "AUD"}, "variation_total_price_money": {"amount": 350, "currency": "AUD"}, "applied_taxes": [{"uid": "53c039f9-5ef9-4ce3-a57c-966893ea3e97", "tax_uid": "53c039f9-5ef9-4ce3-a57c-966893ea3e97", "applied_money": {"amount": 32, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "72d7ffca-f2f4-451a-91b7-3daf2c4ab386", "catalog_object_id": "NFZTLUIUVJGPJC2KQVY37D7A", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON><PERSON><PERSON> with chicken (Bún gà)", "variation_name": "Regular", "base_price_money": {"amount": 1750, "currency": "AUD"}, "gross_sales_money": {"amount": 1750, "currency": "AUD"}, "total_tax_money": {"amount": 159, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1750, "currency": "AUD"}, "variation_total_price_money": {"amount": 1750, "currency": "AUD"}, "applied_taxes": [{"uid": "53c039f9-5ef9-4ce3-a57c-966893ea3e97", "tax_uid": "53c039f9-5ef9-4ce3-a57c-966893ea3e97", "applied_money": {"amount": 159, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "53c039f9-5ef9-4ce3-a57c-966893ea3e97", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 491, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T11:48:25Z", "updated_at": "2025-05-25T11:48:30.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 491, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 5400, "currency": "AUD"}, "closed_at": "2025-05-25T11:48:30.000Z", "tenders": [{"id": "hgqNSSel7YbSLRhnx9LakoAkvaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "0HNotvZ6QjhIcpZt6OPPWmteV", "created_at": "2025-05-25T11:49:39Z", "amount_money": {"amount": 5400, "currency": "AUD"}, "processing_fee_money": {"amount": 86, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "MASTERCARD", "last_4": "1491", "fingerprint": "sq-1-FoxZe0wm5BTiv5p0lvnIy-hATq4iRwqlfKP72_pY1R5mvJCtl2-cMXN0YAgde_ga5g"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 5400, "currency": "AUD"}, "tax_money": {"amount": 491, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "6", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "YrQzWowX1yNADrCKJNxedJ0eV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "b8bd56f3-8400-4ced-9ef0-c9578c2cceee", "catalog_object_id": "ARS3TZD7Z3EG2LHDRK5BJ6GG", "catalog_version": 1743632309931, "quantity": "1", "name": "Charcoal chicken with broken rice (Cơm gà nướng)", "variation_name": "Regular", "base_price_money": {"amount": 1750, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 1750, "currency": "AUD"}, "total_tax_money": {"amount": 159, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1750, "currency": "AUD"}, "variation_total_price_money": {"amount": 1750, "currency": "AUD"}, "applied_taxes": [{"uid": "a88213aa-98ad-4946-a992-1da436fb4673", "tax_uid": "a88213aa-98ad-4946-a992-1da436fb4673", "applied_money": {"amount": 159, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "a88213aa-98ad-4946-a992-1da436fb4673", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 159, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T11:47:19Z", "updated_at": "2025-05-25T11:47:21.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 159, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1750, "currency": "AUD"}, "closed_at": "2025-05-25T11:47:21.000Z", "tenders": [{"id": "blQG2ki5YdRoBc7TtWBQJ31wuaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "YrQzWowX1yNADrCKJNxedJ0eV", "created_at": "2025-05-25T11:48:32Z", "amount_money": {"amount": 1750, "currency": "AUD"}, "processing_fee_money": {"amount": 28, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "VISA", "last_4": "0914", "fingerprint": "sq-1-he23UaVCZl67i4Io-SbB1mKw8wU99c8fDcS5K7gRIGrdbJhV6ECPz7WyIHA_A7djJw"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 1750, "currency": "AUD"}, "tax_money": {"amount": 159, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "5", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "qeVt7ybvzkLLKipvElj5iO0eV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "2174254f-61c4-4e41-a431-95468e0ffe9d", "catalog_object_id": "CPD7YUKPSBQCTRDYBLAV7EV4", "catalog_version": 1743632309931, "quantity": "1", "name": "Charcoal Pork Chop Combo", "variation_name": "Regular", "base_price_money": {"amount": 2250, "currency": "AUD"}, "modifiers": [{"uid": "c84d4ac4-4462-4ecb-a98b-e001420506cc", "base_price_money": {"amount": 300, "currency": "AUD"}, "total_price_money": {"amount": 300, "currency": "AUD"}, "name": "Egg", "catalog_object_id": "764LD7CNJTHWDO3AGA2R7L4L", "catalog_version": 1729822245775, "quantity": "1"}], "note": "", "gross_sales_money": {"amount": 2550, "currency": "AUD"}, "total_tax_money": {"amount": 232, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2550, "currency": "AUD"}, "variation_total_price_money": {"amount": 2250, "currency": "AUD"}, "applied_taxes": [{"uid": "894d2aa1-89ed-4904-a38a-1f0941227410", "tax_uid": "894d2aa1-89ed-4904-a38a-1f0941227410", "applied_money": {"amount": 232, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "93c9bf41-48ca-45aa-b7ae-ceac1aa5d2cd", "catalog_object_id": "ARS3TZD7Z3EG2LHDRK5BJ6GG", "catalog_version": 1743632309931, "quantity": "1", "name": "Charcoal chicken with broken rice (Cơm gà nướng)", "variation_name": "Regular", "base_price_money": {"amount": 1750, "currency": "AUD"}, "modifiers": [{"uid": "68f05fb1-88cb-4f7f-91eb-ed7489525941", "base_price_money": {"amount": 300, "currency": "AUD"}, "total_price_money": {"amount": 300, "currency": "AUD"}, "name": "<PERSON><PERSON><PERSON><PERSON> (Egg)", "catalog_object_id": "BFET73BHNRJNIPUOLJ7O3X3U", "catalog_version": 1729748477167, "quantity": "1"}], "note": "", "gross_sales_money": {"amount": 2050, "currency": "AUD"}, "total_tax_money": {"amount": 186, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2050, "currency": "AUD"}, "variation_total_price_money": {"amount": 1750, "currency": "AUD"}, "applied_taxes": [{"uid": "894d2aa1-89ed-4904-a38a-1f0941227410", "tax_uid": "894d2aa1-89ed-4904-a38a-1f0941227410", "applied_money": {"amount": 186, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "25304192-30cc-4f91-9206-3f1ebe9f42a3", "catalog_object_id": "DC4BFSABXOBNVJVWI2ETCSLI", "catalog_version": 1743632309931, "quantity": "1", "name": "5 pcs Pork Spring Rolls (<PERSON><PERSON> giò heo)", "variation_name": "Regular", "base_price_money": {"amount": 1050, "currency": "AUD"}, "gross_sales_money": {"amount": 1050, "currency": "AUD"}, "total_tax_money": {"amount": 95, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1050, "currency": "AUD"}, "variation_total_price_money": {"amount": 1050, "currency": "AUD"}, "applied_taxes": [{"uid": "894d2aa1-89ed-4904-a38a-1f0941227410", "tax_uid": "894d2aa1-89ed-4904-a38a-1f0941227410", "applied_money": {"amount": 95, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "f1676d4c-b837-4256-b458-86e9a98eedc6", "catalog_object_id": "UXFBOISXB5RDGIXJAHVD3WIW", "catalog_version": 1743632309931, "quantity": "1", "name": "Coke", "variation_name": "Regular", "base_price_money": {"amount": 350, "currency": "AUD"}, "gross_sales_money": {"amount": 350, "currency": "AUD"}, "total_tax_money": {"amount": 32, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 350, "currency": "AUD"}, "variation_total_price_money": {"amount": 350, "currency": "AUD"}, "applied_taxes": [{"uid": "894d2aa1-89ed-4904-a38a-1f0941227410", "tax_uid": "894d2aa1-89ed-4904-a38a-1f0941227410", "applied_money": {"amount": 32, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "894d2aa1-89ed-4904-a38a-1f0941227410", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 545, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T11:41:29Z", "updated_at": "2025-05-25T11:41:32.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 545, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 6000, "currency": "AUD"}, "closed_at": "2025-05-25T11:41:32.000Z", "tenders": [{"id": "fNDyiH2V5p91hAr5boeQWUxmvaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "qeVt7ybvzkLLKipvElj5iO0eV", "created_at": "2025-05-25T11:42:43Z", "amount_money": {"amount": 6000, "currency": "AUD"}, "processing_fee_money": {"amount": 96, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "EFTPOS", "last_4": "3091", "fingerprint": "sq-1-CRxhoXRQ_cw2R5mHub1BzFV4VyOd8ayd93V9lV1MZuyan1pQybIpUoLaMGu84jJr3w"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 6000, "currency": "AUD"}, "tax_money": {"amount": 545, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "4", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "S67sa09ekvvKc59NITlgSboeV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "53bf1076-8dfd-4e9c-9f48-5042ba58d358", "catalog_object_id": "CPD7YUKPSBQCTRDYBLAV7EV4", "catalog_version": 1743632309931, "quantity": "1", "name": "Charcoal Pork Chop Combo", "variation_name": "Regular", "base_price_money": {"amount": 2250, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 2250, "currency": "AUD"}, "total_tax_money": {"amount": 205, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2250, "currency": "AUD"}, "variation_total_price_money": {"amount": 2250, "currency": "AUD"}, "applied_taxes": [{"uid": "ff3aa203-f5bf-4e43-be9a-a4f7fb784b5c", "tax_uid": "ff3aa203-f5bf-4e43-be9a-a4f7fb784b5c", "applied_money": {"amount": 205, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "3425d7f4-a17e-4d7c-8c0d-4a59bf6f4610", "catalog_object_id": "C4XTLDOHSNQHGFDYW66NFDTI", "catalog_version": 1743632309931, "quantity": "1", "name": "Peach Iced Tea Orange & Lemongrass (Trà đào cam sả)", "variation_name": "Large", "base_price_money": {"amount": 850, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 850, "currency": "AUD"}, "total_tax_money": {"amount": 77, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 850, "currency": "AUD"}, "variation_total_price_money": {"amount": 850, "currency": "AUD"}, "applied_taxes": [{"uid": "ff3aa203-f5bf-4e43-be9a-a4f7fb784b5c", "tax_uid": "ff3aa203-f5bf-4e43-be9a-a4f7fb784b5c", "applied_money": {"amount": 77, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "ff3aa203-f5bf-4e43-be9a-a4f7fb784b5c", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 282, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T11:37:00Z", "updated_at": "2025-05-25T11:37:04.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 282, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 3100, "currency": "AUD"}, "closed_at": "2025-05-25T11:37:04.000Z", "tenders": [{"id": "f1pPWqnEjCLOnQyaiMzXhzB7taB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "S67sa09ekvvKc59NITlgSboeV", "created_at": "2025-05-25T11:38:14Z", "amount_money": {"amount": 3100, "currency": "AUD"}, "processing_fee_money": {"amount": 50, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "MASTERCARD", "last_4": "3486", "fingerprint": "sq-1-lC2PL-ErRI5_EuZ4yP7HPBaLvxIPiJZfzlKko6UWRxeO_aCKL3YCM2EBHd5_u5lxSw"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 3100, "currency": "AUD"}, "tax_money": {"amount": 282, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "12", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "oFv4fgHAVhI2sn1C3MEg93eeV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "9a0a8405-8c43-4fc0-bfe5-48fb71f93ffb", "catalog_object_id": "XN26JPNMEXTCJJH6O7P4D6F5", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON> (Bún chả Hà Nội)", "variation_name": "Regular", "base_price_money": {"amount": 1850, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 1850, "currency": "AUD"}, "total_tax_money": {"amount": 168, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1850, "currency": "AUD"}, "variation_total_price_money": {"amount": 1850, "currency": "AUD"}, "applied_taxes": [{"uid": "734582c9-08e6-4d52-900c-e6293a222318", "tax_uid": "734582c9-08e6-4d52-900c-e6293a222318", "applied_money": {"amount": 168, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "b862fefa-44ea-43ee-82a1-81badeee9664", "catalog_object_id": "N7NLNPFHF5ZLSD3KQVWFKIVY", "catalog_version": 1743632309931, "quantity": "1", "name": "4 pcs Prawn Spring Rolls (Chả giò tôm)", "variation_name": "Regular", "base_price_money": {"amount": 1050, "currency": "AUD"}, "gross_sales_money": {"amount": 1050, "currency": "AUD"}, "total_tax_money": {"amount": 95, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1050, "currency": "AUD"}, "variation_total_price_money": {"amount": 1050, "currency": "AUD"}, "applied_taxes": [{"uid": "734582c9-08e6-4d52-900c-e6293a222318", "tax_uid": "734582c9-08e6-4d52-900c-e6293a222318", "applied_money": {"amount": 95, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "25ec3603-7636-462f-a1e4-014c1a7f4a2a", "catalog_object_id": "WLOA62RODZIMIB6RZPJBUNEY", "catalog_version": 1743632309931, "quantity": "1", "name": "b<PERSON>h tr<PERSON>g n<PERSON> ( 2pcs Grilled Rice Paper Rolls)", "variation_name": "Regular", "base_price_money": {"amount": 1790, "currency": "AUD"}, "gross_sales_money": {"amount": 1790, "currency": "AUD"}, "total_tax_money": {"amount": 163, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1790, "currency": "AUD"}, "variation_total_price_money": {"amount": 1790, "currency": "AUD"}, "applied_taxes": [{"uid": "734582c9-08e6-4d52-900c-e6293a222318", "tax_uid": "734582c9-08e6-4d52-900c-e6293a222318", "applied_money": {"amount": 163, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "734582c9-08e6-4d52-900c-e6293a222318", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 426, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T11:36:11Z", "updated_at": "2025-05-25T11:36:14.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 426, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 4690, "currency": "AUD"}, "closed_at": "2025-05-25T11:36:14.000Z", "tenders": [{"id": "7LvgCq8RV71taFy710Sdhkg9taB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "oFv4fgHAVhI2sn1C3MEg93eeV", "created_at": "2025-05-25T11:37:25Z", "amount_money": {"amount": 4690, "currency": "AUD"}, "processing_fee_money": {"amount": 75, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "VISA", "last_4": "0134", "fingerprint": "sq-1-7kuMc3PSB2lLg2uUkubwwlFueJXBJ1xhLxjpQv9mPpwMr9JSIEztSG8xz2gqYwtvHQ"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 4690, "currency": "AUD"}, "tax_money": {"amount": 426, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "8", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "mCgLmD7PFlTQfdBWt8C3bdrOT96YY", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "a8ac32d5-62e0-48de-8a88-5a1fd4249f3b", "catalog_object_id": "4P5YFVOOXILO62JOBQR67NVC", "catalog_version": 1746967894746, "quantity": "2", "name": "<PERSON>rc<PERSON><PERSON> (Cơm sư<PERSON> heo)", "variation_name": "Regular", "base_price_money": {"amount": 1750, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 3500, "currency": "AUD"}, "total_tax_money": {"amount": 318, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 3500, "currency": "AUD"}, "variation_total_price_money": {"amount": 3500, "currency": "AUD"}, "applied_taxes": [{"uid": "91064b71-22bb-475f-847e-626a0932c095", "tax_uid": "91064b71-22bb-475f-847e-626a0932c095", "applied_money": {"amount": 318, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "c58fb6e0-d3d0-4215-903d-00cda3aa0cf4", "catalog_object_id": "I2U35K7L5XNVKDR2NPGGQCFJ", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON>", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "91064b71-22bb-475f-847e-626a0932c095", "tax_uid": "91064b71-22bb-475f-847e-626a0932c095", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "91064b71-22bb-475f-847e-626a0932c095", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10.0", "type": "INCLUSIVE", "applied_money": {"amount": 450, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T11:27:24.012Z", "updated_at": "2025-05-25T11:27:25.000Z", "state": "COMPLETED", "version": 4, "total_tax_money": {"amount": 450, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 4950, "currency": "AUD"}, "closed_at": "2025-05-25T11:27:24.468Z", "tenders": [{"id": "Byzxqu1LAyvBs5iEJ3I9GEsyNMHZY", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "mCgLmD7PFlTQfdBWt8C3bdrOT96YY", "created_at": "2025-05-25T11:27:24Z", "amount_money": {"amount": 4950, "currency": "AUD"}, "type": "CASH", "cash_details": {"buyer_tendered_money": {"amount": 10000, "currency": "AUD"}, "change_back_money": {"amount": 5050, "currency": "AUD"}}, "payment_id": "Byzxqu1LAyvBs5iEJ3I9GEsyNMHZY"}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "net_amounts": {"total_money": {"amount": 4950, "currency": "AUD"}, "tax_money": {"amount": 450, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "5", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "QnQR7DEbvPnMaVMis8JVBz5eV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "4d2ed3ab-38d6-48d3-a39d-716974fbf30d", "catalog_object_id": "DC4BFSABXOBNVJVWI2ETCSLI", "catalog_version": 1743632309931, "quantity": "1", "name": "5 pcs Pork Spring Rolls (<PERSON><PERSON> giò heo)", "variation_name": "Regular", "base_price_money": {"amount": 1050, "currency": "AUD"}, "gross_sales_money": {"amount": 1050, "currency": "AUD"}, "total_tax_money": {"amount": 95, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1050, "currency": "AUD"}, "variation_total_price_money": {"amount": 1050, "currency": "AUD"}, "applied_taxes": [{"uid": "62c96085-9ec9-412d-bf77-87ac98a74668", "tax_uid": "62c96085-9ec9-412d-bf77-87ac98a74668", "applied_money": {"amount": 95, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "62c96085-9ec9-412d-bf77-87ac98a74668", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 95, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T11:19:15Z", "updated_at": "2025-05-25T11:19:18.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 95, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1050, "currency": "AUD"}, "closed_at": "2025-05-25T11:19:18.000Z", "tenders": [{"id": "Zi3SMt1syy9L1CtwIVxYD1f6uaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "QnQR7DEbvPnMaVMis8JVBz5eV", "created_at": "2025-05-25T11:20:29Z", "amount_money": {"amount": 1050, "currency": "AUD"}, "processing_fee_money": {"amount": 17, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "VISA", "last_4": "6831", "fingerprint": "sq-1-pjEPEvlqc3pL97hJs9BDmqyuh0bDh4QA02SIoWA46h4ZsHahKbjxIbW8SchI-MKxlw"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 1050, "currency": "AUD"}, "tax_money": {"amount": 95, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "8 Ta", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "4Z9GYGpyYHtSj1ifnwdYp5veV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "8f9b1ec3-d2ac-46b8-9179-1e2acf2952e8", "catalog_object_id": "CPD7YUKPSBQCTRDYBLAV7EV4", "catalog_version": 1743632309931, "quantity": "1", "name": "Charcoal Pork Chop Combo", "variation_name": "Regular", "base_price_money": {"amount": 2250, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 2250, "currency": "AUD"}, "total_tax_money": {"amount": 204, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2250, "currency": "AUD"}, "variation_total_price_money": {"amount": 2250, "currency": "AUD"}, "applied_taxes": [{"uid": "a0d6a3c4-1004-45b0-8419-f6cef46f807b", "tax_uid": "a0d6a3c4-1004-45b0-8419-f6cef46f807b", "applied_money": {"amount": 204, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "ae8734d9-420f-4339-8d33-bc42ba0c5487", "catalog_object_id": "NFZTLUIUVJGPJC2KQVY37D7A", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON><PERSON><PERSON> with chicken (Bún gà)", "variation_name": "Regular", "base_price_money": {"amount": 1750, "currency": "AUD"}, "modifiers": [{"uid": "0daf9e58-a067-40a7-be2e-2c9196f1412b", "base_price_money": {"amount": 300, "currency": "AUD"}, "total_price_money": {"amount": 300, "currency": "AUD"}, "name": "<PERSON><PERSON><PERSON><PERSON> gà", "catalog_object_id": "ILLOHIKDWIDOJJI4KXJW52JQ", "catalog_version": 1721361200022, "quantity": "1"}], "note": "", "gross_sales_money": {"amount": 2050, "currency": "AUD"}, "total_tax_money": {"amount": 187, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2050, "currency": "AUD"}, "variation_total_price_money": {"amount": 1750, "currency": "AUD"}, "applied_taxes": [{"uid": "a0d6a3c4-1004-45b0-8419-f6cef46f807b", "tax_uid": "a0d6a3c4-1004-45b0-8419-f6cef46f807b", "applied_money": {"amount": 187, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "c88df532-0d7f-4dff-8687-250f476e17c9", "catalog_object_id": "C4XTLDOHSNQHGFDYW66NFDTI", "catalog_version": 1743632309931, "quantity": "1", "name": "Peach Iced Tea Orange & Lemongrass (Trà đào cam sả)", "variation_name": "Large", "base_price_money": {"amount": 850, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 850, "currency": "AUD"}, "total_tax_money": {"amount": 77, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 850, "currency": "AUD"}, "variation_total_price_money": {"amount": 850, "currency": "AUD"}, "applied_taxes": [{"uid": "a0d6a3c4-1004-45b0-8419-f6cef46f807b", "tax_uid": "a0d6a3c4-1004-45b0-8419-f6cef46f807b", "applied_money": {"amount": 77, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "a0d6a3c4-1004-45b0-8419-f6cef46f807b", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 468, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T11:18:39Z", "updated_at": "2025-05-25T11:18:42.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 468, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 5150, "currency": "AUD"}, "closed_at": "2025-05-25T11:18:42.000Z", "tenders": [{"id": "LX1YLvCS7lsNdzJR2oW5QojBwaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "4Z9GYGpyYHtSj1ifnwdYp5veV", "created_at": "2025-05-25T11:19:53Z", "amount_money": {"amount": 5150, "currency": "AUD"}, "processing_fee_money": {"amount": 82, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "VISA", "last_4": "2609", "fingerprint": "sq-1-4aJPK2sW5zdZAxRqyd0LJiPLYzONMyZJF-dOp04ExOvXM_juJg6263ERdos_D--Pow"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 5150, "currency": "AUD"}, "tax_money": {"amount": 468, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "12 Ta", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "28jE0rK1omM1Vy73jl802pd2bdAZY", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "0b5c2e73-a45d-41b5-ba2c-4ee17a361377", "catalog_object_id": "WLOA62RODZIMIB6RZPJBUNEY", "catalog_version": 1743632309931, "quantity": "1", "name": "b<PERSON>h tr<PERSON>g n<PERSON> ( 2pcs Grilled Rice Paper Rolls)", "variation_name": "Regular", "base_price_money": {"amount": 1790, "currency": "AUD"}, "gross_sales_money": {"amount": 1790, "currency": "AUD"}, "total_tax_money": {"amount": 163, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1790, "currency": "AUD"}, "variation_total_price_money": {"amount": 1790, "currency": "AUD"}, "applied_taxes": [{"uid": "20adcedd-ebb1-4bed-890f-27ceac21318c", "tax_uid": "20adcedd-ebb1-4bed-890f-27ceac21318c", "applied_money": {"amount": 163, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "8d6d5c8e-bf48-4427-a0cc-aa705b8f9d3d", "catalog_object_id": "U7TVTKLJ4Z5FWXIHD7MLX2LL", "catalog_version": 1747462207006, "quantity": "1", "name": "<PERSON><PERSON> chua r<PERSON>", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "20adcedd-ebb1-4bed-890f-27ceac21318c", "tax_uid": "20adcedd-ebb1-4bed-890f-27ceac21318c", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "1edbd529-56a6-46ea-b1ac-f3cfa54a561f", "catalog_object_id": "XN26JPNMEXTCJJH6O7P4D6F5", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON> (Bún chả Hà Nội)", "variation_name": "Regular", "base_price_money": {"amount": 1850, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 1850, "currency": "AUD"}, "total_tax_money": {"amount": 168, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1850, "currency": "AUD"}, "variation_total_price_money": {"amount": 1850, "currency": "AUD"}, "applied_taxes": [{"uid": "20adcedd-ebb1-4bed-890f-27ceac21318c", "tax_uid": "20adcedd-ebb1-4bed-890f-27ceac21318c", "applied_money": {"amount": 168, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "20adcedd-ebb1-4bed-890f-27ceac21318c", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10.0", "type": "INCLUSIVE", "applied_money": {"amount": 463, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T11:10:39.400Z", "updated_at": "2025-05-25T11:10:41.000Z", "state": "COMPLETED", "version": 4, "total_tax_money": {"amount": 463, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 5090, "currency": "AUD"}, "closed_at": "2025-05-25T11:10:39.864Z", "tenders": [{"id": "52rl9VA4u6xDzkIyQLLx07w21MeZY", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "28jE0rK1omM1Vy73jl802pd2bdAZY", "created_at": "2025-05-25T11:10:39Z", "amount_money": {"amount": 5090, "currency": "AUD"}, "type": "CASH", "cash_details": {"buyer_tendered_money": {"amount": 5090, "currency": "AUD"}, "change_back_money": {"amount": 0, "currency": "AUD"}}, "payment_id": "52rl9VA4u6xDzkIyQLLx07w21MeZY"}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "net_amounts": {"total_money": {"amount": 5090, "currency": "AUD"}, "tax_money": {"amount": 463, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "5", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "6OPiMoicE5n4WjW5CCN9L3hNID8YY", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "cafe40dd-ab62-4c87-9e0e-512946121b36", "catalog_object_id": "ZRWI7HUU5SX3AUGYLFBBN4NY", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON><PERSON> vị<PERSON> lộn ( 2 pcs Balut)", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "82287409-007c-4d99-9eed-e122cec21c29", "tax_uid": "82287409-007c-4d99-9eed-e122cec21c29", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "3cb72dc6-cfa2-446e-82aa-9978266effc8", "catalog_object_id": "7HDRRPGYGFTZ763Z3U2GBRZK", "catalog_version": 1743632309931, "quantity": "1", "name": "bò lá lốt", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "82287409-007c-4d99-9eed-e122cec21c29", "tax_uid": "82287409-007c-4d99-9eed-e122cec21c29", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "1ea7b9be-0c15-4c55-b0b6-7d4c1d4d7ffa", "catalog_object_id": "WLOA62RODZIMIB6RZPJBUNEY", "catalog_version": 1743632309931, "quantity": "1", "name": "b<PERSON>h tr<PERSON>g n<PERSON> ( 2pcs Grilled Rice Paper Rolls)", "variation_name": "Regular", "base_price_money": {"amount": 1790, "currency": "AUD"}, "gross_sales_money": {"amount": 1790, "currency": "AUD"}, "total_tax_money": {"amount": 162, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1790, "currency": "AUD"}, "variation_total_price_money": {"amount": 1790, "currency": "AUD"}, "applied_taxes": [{"uid": "82287409-007c-4d99-9eed-e122cec21c29", "tax_uid": "82287409-007c-4d99-9eed-e122cec21c29", "applied_money": {"amount": 162, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "82287409-007c-4d99-9eed-e122cec21c29", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10.0", "type": "INCLUSIVE", "applied_money": {"amount": 426, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T11:07:40.089Z", "updated_at": "2025-05-25T11:07:41.000Z", "state": "COMPLETED", "version": 4, "total_tax_money": {"amount": 426, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 4690, "currency": "AUD"}, "closed_at": "2025-05-25T11:07:40.530Z", "tenders": [{"id": "l2CC37vkrroHghnQOGV7kcGCQfVZY", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "6OPiMoicE5n4WjW5CCN9L3hNID8YY", "created_at": "2025-05-25T11:07:40Z", "amount_money": {"amount": 4690, "currency": "AUD"}, "type": "CASH", "cash_details": {"buyer_tendered_money": {"amount": 5000, "currency": "AUD"}, "change_back_money": {"amount": 310, "currency": "AUD"}}, "payment_id": "l2CC37vkrroHghnQOGV7kcGCQfVZY"}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "net_amounts": {"total_money": {"amount": 4690, "currency": "AUD"}, "tax_money": {"amount": 426, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "4", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "akek8JjFJipT2vRlrmcUhD7eV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "0cdf221f-7e7e-42f3-bb9f-5620d7f5d45c", "catalog_object_id": "4GTTSUDUKSDV5PGT2MXKKKPJ", "catalog_version": 1743632309931, "quantity": "1", "name": "Water", "variation_name": "Regular", "base_price_money": {"amount": 350, "currency": "AUD"}, "gross_sales_money": {"amount": 350, "currency": "AUD"}, "total_tax_money": {"amount": 32, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 350, "currency": "AUD"}, "variation_total_price_money": {"amount": 350, "currency": "AUD"}, "applied_taxes": [{"uid": "bb50c85f-de7e-46a5-a738-4304062f869d", "tax_uid": "bb50c85f-de7e-46a5-a738-4304062f869d", "applied_money": {"amount": 32, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "bb50c85f-de7e-46a5-a738-4304062f869d", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 32, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T10:59:41Z", "updated_at": "2025-05-25T10:59:44.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 32, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 350, "currency": "AUD"}, "closed_at": "2025-05-25T10:59:44.000Z", "tenders": [{"id": "z3dvAUW5FYKJVLvwedTyVVssuaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "akek8JjFJipT2vRlrmcUhD7eV", "created_at": "2025-05-25T11:00:55Z", "amount_money": {"amount": 350, "currency": "AUD"}, "processing_fee_money": {"amount": 6, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "VISA", "last_4": "6831", "fingerprint": "sq-1-pjEPEvlqc3pL97hJs9BDmqyuh0bDh4QA02SIoWA46h4ZsHahKbjxIbW8SchI-MKxlw"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 350, "currency": "AUD"}, "tax_money": {"amount": 32, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "0zPirrm72XbG1OuYWJlCOLzeV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "26ea3af9-3c7b-4ba9-bf5a-322304bed7b0", "quantity": "1", "base_price_money": {"amount": 200, "currency": "AUD"}, "gross_sales_money": {"amount": 200, "currency": "AUD"}, "total_tax_money": {"amount": 18, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 200, "currency": "AUD"}, "variation_total_price_money": {"amount": 200, "currency": "AUD"}, "applied_taxes": [{"uid": "a310951b-7d89-4269-acc1-b7ba7f426281", "tax_uid": "a310951b-7d89-4269-acc1-b7ba7f426281", "applied_money": {"amount": 18, "currency": "AUD"}, "auto_applied": true}], "item_type": "CUSTOM_AMOUNT", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "a310951b-7d89-4269-acc1-b7ba7f426281", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 18, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T10:48:44Z", "updated_at": "2025-05-25T10:48:48.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 18, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 200, "currency": "AUD"}, "closed_at": "2025-05-25T10:48:48.000Z", "tenders": [{"id": "Hz6BMOfWCCw21TpstAnEuVmMuaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "0zPirrm72XbG1OuYWJlCOLzeV", "created_at": "2025-05-25T10:49:59Z", "amount_money": {"amount": 200, "currency": "AUD"}, "processing_fee_money": {"amount": 3, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "MASTERCARD", "last_4": "8876", "fingerprint": "sq-1-eQDhJbI5BIrY49cdnpCLXzdFi51UlYFzVWeXFuJpkpWwYNAzftPtwj3lyxtrJY73Sg"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 200, "currency": "AUD"}, "tax_money": {"amount": 18, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "ElAiq2FK4ZUYRnfoBNihC5neV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "c2092496-9644-4952-8e18-aaa76ee08875", "catalog_object_id": "DC4BFSABXOBNVJVWI2ETCSLI", "catalog_version": 1743632309931, "quantity": "1", "name": "5 pcs Pork Spring Rolls (<PERSON><PERSON> giò heo)", "variation_name": "Regular", "base_price_money": {"amount": 1050, "currency": "AUD"}, "gross_sales_money": {"amount": 1050, "currency": "AUD"}, "total_tax_money": {"amount": 95, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1050, "currency": "AUD"}, "variation_total_price_money": {"amount": 1050, "currency": "AUD"}, "applied_taxes": [{"uid": "b898f30c-86e6-4115-abf9-d9ff203a3991", "tax_uid": "b898f30c-86e6-4115-abf9-d9ff203a3991", "applied_money": {"amount": 95, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "b898f30c-86e6-4115-abf9-d9ff203a3991", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 95, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T10:48:05Z", "updated_at": "2025-05-25T10:48:08.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 95, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1050, "currency": "AUD"}, "closed_at": "2025-05-25T10:48:08.000Z", "tenders": [{"id": "VstQ3DanI0sOtpLgrTGKCYd4vaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "ElAiq2FK4ZUYRnfoBNihC5neV", "created_at": "2025-05-25T10:49:19Z", "amount_money": {"amount": 1050, "currency": "AUD"}, "processing_fee_money": {"amount": 17, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "VISA", "last_4": "6831", "fingerprint": "sq-1-pjEPEvlqc3pL97hJs9BDmqyuh0bDh4QA02SIoWA46h4ZsHahKbjxIbW8SchI-MKxlw"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 1050, "currency": "AUD"}, "tax_money": {"amount": 95, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "12", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "mO0MLLZnc54slAOR6aSgMVfeV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "604f2c95-f1a0-48a7-b7fd-ec7eade1d2e4", "catalog_object_id": "ZRWI7HUU5SX3AUGYLFBBN4NY", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON><PERSON> vị<PERSON> lộn ( 2 pcs Balut)", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "7022a01d-f759-4a59-bd3b-eef12e30a878", "tax_uid": "7022a01d-f759-4a59-bd3b-eef12e30a878", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "778f1909-afb2-426d-9a21-9c9e28d76c24", "catalog_object_id": "U7TVTKLJ4Z5FWXIHD7MLX2LL", "catalog_version": 1747462207006, "quantity": "1", "name": "<PERSON><PERSON> chua r<PERSON>", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "7022a01d-f759-4a59-bd3b-eef12e30a878", "tax_uid": "7022a01d-f759-4a59-bd3b-eef12e30a878", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "8e5095b4-f743-4e91-9aa0-368775c68c83", "catalog_object_id": "C4XTLDOHSNQHGFDYW66NFDTI", "catalog_version": 1743632309931, "quantity": "1", "name": "Peach Iced Tea Orange & Lemongrass (Trà đào cam sả)", "variation_name": "Large", "base_price_money": {"amount": 850, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 850, "currency": "AUD"}, "total_tax_money": {"amount": 77, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 850, "currency": "AUD"}, "variation_total_price_money": {"amount": 850, "currency": "AUD"}, "applied_taxes": [{"uid": "7022a01d-f759-4a59-bd3b-eef12e30a878", "tax_uid": "7022a01d-f759-4a59-bd3b-eef12e30a878", "applied_money": {"amount": 77, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "7022a01d-f759-4a59-bd3b-eef12e30a878", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 341, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T10:41:16Z", "updated_at": "2025-05-25T10:41:19.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 341, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 3750, "currency": "AUD"}, "closed_at": "2025-05-25T10:41:19.000Z", "tenders": [{"id": "7xyib0c0KTVeNgTS6ATMyxRqvaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "mO0MLLZnc54slAOR6aSgMVfeV", "created_at": "2025-05-25T10:42:30Z", "amount_money": {"amount": 3750, "currency": "AUD"}, "processing_fee_money": {"amount": 60, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "EFTPOS", "last_4": "3853", "fingerprint": "sq-1-yN4NcQ6RwFyL6dNcZyl0kDSMjgrU8AFXtQohQ1ZEkddsCxHu6zMFwuTeYBWwfF8O5Q"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 3750, "currency": "AUD"}, "tax_money": {"amount": 341, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "4", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "6eBT0lGMn2TYVlDiGrbLdO2eV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "92d7a023-076c-4cfd-b7c8-091ca0c5607c", "catalog_object_id": "CPD7YUKPSBQCTRDYBLAV7EV4", "catalog_version": 1743632309931, "quantity": "3", "name": "Charcoal Pork Chop Combo", "variation_name": "Regular", "base_price_money": {"amount": 2250, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 6750, "currency": "AUD"}, "total_tax_money": {"amount": 614, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 6750, "currency": "AUD"}, "variation_total_price_money": {"amount": 6750, "currency": "AUD"}, "applied_taxes": [{"uid": "460ce455-934c-463d-9ca9-c85471aa2758", "tax_uid": "460ce455-934c-463d-9ca9-c85471aa2758", "applied_money": {"amount": 614, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "748836ca-3a4c-4d6d-a9c2-86da782aecc1", "catalog_object_id": "WLOA62RODZIMIB6RZPJBUNEY", "catalog_version": 1743632309931, "quantity": "1", "name": "b<PERSON>h tr<PERSON>g n<PERSON> ( 2pcs Grilled Rice Paper Rolls)", "variation_name": "Regular", "base_price_money": {"amount": 1790, "currency": "AUD"}, "gross_sales_money": {"amount": 1790, "currency": "AUD"}, "total_tax_money": {"amount": 163, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1790, "currency": "AUD"}, "variation_total_price_money": {"amount": 1790, "currency": "AUD"}, "applied_taxes": [{"uid": "460ce455-934c-463d-9ca9-c85471aa2758", "tax_uid": "460ce455-934c-463d-9ca9-c85471aa2758", "applied_money": {"amount": 163, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "40436c55-a2a4-4d64-b783-ebe5f348a641", "catalog_object_id": "4GTTSUDUKSDV5PGT2MXKKKPJ", "catalog_version": 1743632309931, "quantity": "3", "name": "Water", "variation_name": "Regular", "base_price_money": {"amount": 350, "currency": "AUD"}, "gross_sales_money": {"amount": 1050, "currency": "AUD"}, "total_tax_money": {"amount": 95, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1050, "currency": "AUD"}, "variation_total_price_money": {"amount": 1050, "currency": "AUD"}, "applied_taxes": [{"uid": "460ce455-934c-463d-9ca9-c85471aa2758", "tax_uid": "460ce455-934c-463d-9ca9-c85471aa2758", "applied_money": {"amount": 95, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "460ce455-934c-463d-9ca9-c85471aa2758", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 872, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T10:36:40Z", "updated_at": "2025-05-25T10:36:44.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 872, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 9590, "currency": "AUD"}, "closed_at": "2025-05-25T10:36:44.000Z", "tenders": [{"id": "tyYRMRHnAGRp6X3h046ky2WOvaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "6eBT0lGMn2TYVlDiGrbLdO2eV", "created_at": "2025-05-25T10:37:54Z", "amount_money": {"amount": 9590, "currency": "AUD"}, "processing_fee_money": {"amount": 153, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "MASTERCARD", "last_4": "8876", "fingerprint": "sq-1-eQDhJbI5BIrY49cdnpCLXzdFi51UlYFzVWeXFuJpkpWwYNAzftPtwj3lyxtrJY73Sg"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 9590, "currency": "AUD"}, "tax_money": {"amount": 872, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "8", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "cVmG9dZoMfMMqzqEiXnhTsYV6keZY", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "353e06c2-0f99-4834-a4e6-23a54d2a827e", "catalog_object_id": "C4XTLDOHSNQHGFDYW66NFDTI", "catalog_version": 1743632309931, "quantity": "2", "name": "Peach Iced Tea Orange & Lemongrass (Trà đào cam sả)", "variation_name": "Large", "base_price_money": {"amount": 850, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 1700, "currency": "AUD"}, "total_tax_money": {"amount": 155, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1700, "currency": "AUD"}, "variation_total_price_money": {"amount": 1700, "currency": "AUD"}, "applied_taxes": [{"uid": "bd3683b1-1eef-47a6-b8d9-7974d7e821db", "tax_uid": "bd3683b1-1eef-47a6-b8d9-7974d7e821db", "applied_money": {"amount": 155, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "bd3683b1-1eef-47a6-b8d9-7974d7e821db", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10.0", "type": "INCLUSIVE", "applied_money": {"amount": 155, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T10:16:55.269Z", "updated_at": "2025-05-25T10:16:56.000Z", "state": "COMPLETED", "version": 4, "total_tax_money": {"amount": 155, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1700, "currency": "AUD"}, "closed_at": "2025-05-25T10:16:55.719Z", "tenders": [{"id": "VqM4Xvt3sOVsPoYzv3EuatwAB7eZY", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "cVmG9dZoMfMMqzqEiXnhTsYV6keZY", "created_at": "2025-05-25T10:16:55Z", "amount_money": {"amount": 1700, "currency": "AUD"}, "type": "CASH", "cash_details": {"buyer_tendered_money": {"amount": 5000, "currency": "AUD"}, "change_back_money": {"amount": 3300, "currency": "AUD"}}, "payment_id": "VqM4Xvt3sOVsPoYzv3EuatwAB7eZY"}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "net_amounts": {"total_money": {"amount": 1700, "currency": "AUD"}, "tax_money": {"amount": 155, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "m6MVblPH4wVPfOocDybyCng29KfZY", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "8d0c5d87-45d0-4c5a-9f10-a8022487dce0", "catalog_object_id": "ZRWI7HUU5SX3AUGYLFBBN4NY", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON><PERSON> vị<PERSON> lộn ( 2 pcs Balut)", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "16fae09e-46a0-4ac3-8ee2-d6aebcee3475", "tax_uid": "16fae09e-46a0-4ac3-8ee2-d6aebcee3475", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "8b40d306-33ba-4a77-a69e-90878e84a745", "catalog_object_id": "I2U35K7L5XNVKDR2NPGGQCFJ", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON>", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "16fae09e-46a0-4ac3-8ee2-d6aebcee3475", "tax_uid": "16fae09e-46a0-4ac3-8ee2-d6aebcee3475", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "16fae09e-46a0-4ac3-8ee2-d6aebcee3475", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10.0", "type": "INCLUSIVE", "applied_money": {"amount": 264, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T10:02:16.941Z", "updated_at": "2025-05-25T10:02:18.000Z", "state": "COMPLETED", "version": 4, "total_tax_money": {"amount": 264, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2900, "currency": "AUD"}, "closed_at": "2025-05-25T10:02:17.433Z", "tenders": [{"id": "T3pP1bCuTPxGXSZmu9KGZ762obOZY", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "m6MVblPH4wVPfOocDybyCng29KfZY", "created_at": "2025-05-25T10:02:17Z", "amount_money": {"amount": 2900, "currency": "AUD"}, "type": "CASH", "cash_details": {"buyer_tendered_money": {"amount": 3000, "currency": "AUD"}, "change_back_money": {"amount": 100, "currency": "AUD"}}, "payment_id": "T3pP1bCuTPxGXSZmu9KGZ762obOZY"}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "net_amounts": {"total_money": {"amount": 2900, "currency": "AUD"}, "tax_money": {"amount": 264, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "4", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "ojoxNGBs3HwhzlU9ACVzcR0AnCKZY", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "7bb6d548-deeb-4f1f-8234-0cc9d82d92c6", "catalog_object_id": "7HDRRPGYGFTZ763Z3U2GBRZK", "catalog_version": 1743632309931, "quantity": "1", "name": "bò lá lốt", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "c8d40d93-a986-41d6-85c3-78b901d5bfa2", "tax_uid": "c8d40d93-a986-41d6-85c3-78b901d5bfa2", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "89444aca-c201-4337-9397-f11a6776b1a6", "catalog_object_id": "WLOA62RODZIMIB6RZPJBUNEY", "catalog_version": 1743632309931, "quantity": "1", "name": "b<PERSON>h tr<PERSON>g n<PERSON> ( 2pcs Grilled Rice Paper Rolls)", "variation_name": "Regular", "base_price_money": {"amount": 1790, "currency": "AUD"}, "gross_sales_money": {"amount": 1790, "currency": "AUD"}, "total_tax_money": {"amount": 163, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1790, "currency": "AUD"}, "variation_total_price_money": {"amount": 1790, "currency": "AUD"}, "applied_taxes": [{"uid": "c8d40d93-a986-41d6-85c3-78b901d5bfa2", "tax_uid": "c8d40d93-a986-41d6-85c3-78b901d5bfa2", "applied_money": {"amount": 163, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "c8d40d93-a986-41d6-85c3-78b901d5bfa2", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10.0", "type": "INCLUSIVE", "applied_money": {"amount": 295, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T09:59:44.962Z", "updated_at": "2025-05-25T09:59:45.292Z", "state": "COMPLETED", "version": 4, "total_tax_money": {"amount": 295, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 3240, "currency": "AUD"}, "closed_at": "2025-05-25T09:59:45.448Z", "tenders": [{"id": "nDki4xvRDlmGQiDinFMnvu7E0NQZY", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "ojoxNGBs3HwhzlU9ACVzcR0AnCKZY", "created_at": "2025-05-25T09:59:45Z", "amount_money": {"amount": 3240, "currency": "AUD"}, "type": "CASH", "cash_details": {"buyer_tendered_money": {"amount": 3240, "currency": "AUD"}, "change_back_money": {"amount": 0, "currency": "AUD"}}, "payment_id": "nDki4xvRDlmGQiDinFMnvu7E0NQZY"}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "net_amounts": {"total_money": {"amount": 3240, "currency": "AUD"}, "tax_money": {"amount": 295, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "4", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "IBlcDXTUPFPLm56zXPZfbWzeV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "9b305482-46de-4e13-8229-81d9d53292a4", "catalog_object_id": "G36Z2AAEAGQFM3NPMBQGCBQN", "catalog_version": 1743632309931, "quantity": "1", "name": "Phở bò (<PERSON><PERSON>)", "variation_name": "Regular", "base_price_money": {"amount": 1850, "currency": "AUD"}, "gross_sales_money": {"amount": 1850, "currency": "AUD"}, "total_tax_money": {"amount": 168, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1850, "currency": "AUD"}, "variation_total_price_money": {"amount": 1850, "currency": "AUD"}, "applied_taxes": [{"uid": "5f651be8-348e-4cd8-96d5-04fb4821ae09", "tax_uid": "5f651be8-348e-4cd8-96d5-04fb4821ae09", "applied_money": {"amount": 168, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "be480510-d814-4bfe-a273-a891d2ac84d3", "catalog_object_id": "WLOA62RODZIMIB6RZPJBUNEY", "catalog_version": 1743632309931, "quantity": "1", "name": "b<PERSON>h tr<PERSON>g n<PERSON> ( 2pcs Grilled Rice Paper Rolls)", "variation_name": "Regular", "base_price_money": {"amount": 1790, "currency": "AUD"}, "gross_sales_money": {"amount": 1790, "currency": "AUD"}, "total_tax_money": {"amount": 163, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1790, "currency": "AUD"}, "variation_total_price_money": {"amount": 1790, "currency": "AUD"}, "applied_taxes": [{"uid": "5f651be8-348e-4cd8-96d5-04fb4821ae09", "tax_uid": "5f651be8-348e-4cd8-96d5-04fb4821ae09", "applied_money": {"amount": 163, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "5f651be8-348e-4cd8-96d5-04fb4821ae09", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 331, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T09:20:57Z", "updated_at": "2025-05-25T09:21:00.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 331, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 3640, "currency": "AUD"}, "closed_at": "2025-05-25T09:21:00.000Z", "tenders": [{"id": "f1PyoZGXexES04f7PMdFZ7ivuaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "IBlcDXTUPFPLm56zXPZfbWzeV", "created_at": "2025-05-25T09:22:11Z", "amount_money": {"amount": 3640, "currency": "AUD"}, "processing_fee_money": {"amount": 58, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "EFTPOS", "last_4": "4553", "fingerprint": "sq-1-4OdQmRSilUqMv1QC14Pd3zs02y6NYIUVmWpKMRlMKYZvK7ovE0CldQ4jhvTo3Vn0UA"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 3640, "currency": "AUD"}, "tax_money": {"amount": 331, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "12", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "4dhfCrGsFQHJ3Fn3rw9uPrR5f2SZY", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "97c4d9b0-16c2-435b-bb0a-13705f14c395", "catalog_object_id": "TBRDSRUFSXJ6I7Y634ZYB4DP", "catalog_version": 1743632309931, "quantity": "1", "name": "Combination Pho (Phờ đặc biệt)", "variation_name": "Regular", "base_price_money": {"amount": 1950, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 1950, "currency": "AUD"}, "total_tax_money": {"amount": 177, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1950, "currency": "AUD"}, "variation_total_price_money": {"amount": 1950, "currency": "AUD"}, "applied_taxes": [{"uid": "c9f6446d-f51c-4d11-9a9a-2713250e429c", "tax_uid": "c9f6446d-f51c-4d11-9a9a-2713250e429c", "applied_money": {"amount": 177, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "c9f6446d-f51c-4d11-9a9a-2713250e429c", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10.0", "type": "INCLUSIVE", "applied_money": {"amount": 177, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T09:06:05.498Z", "updated_at": "2025-05-25T09:06:07.000Z", "state": "COMPLETED", "version": 4, "total_tax_money": {"amount": 177, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1950, "currency": "AUD"}, "closed_at": "2025-05-25T09:06:05.972Z", "tenders": [{"id": "9QRYA3PfEk4tuok2q1r7jn8B5mcZY", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "4dhfCrGsFQHJ3Fn3rw9uPrR5f2SZY", "created_at": "2025-05-25T09:06:05Z", "amount_money": {"amount": 1950, "currency": "AUD"}, "type": "CASH", "cash_details": {"buyer_tendered_money": {"amount": 5000, "currency": "AUD"}, "change_back_money": {"amount": 3050, "currency": "AUD"}}, "payment_id": "9QRYA3PfEk4tuok2q1r7jn8B5mcZY"}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "net_amounts": {"total_money": {"amount": 1950, "currency": "AUD"}, "tax_money": {"amount": 177, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "12", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "u6owB5x7hnsRY1vWWLczltreV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "48950573-d8cd-476d-9c22-aa432950ac8a", "catalog_object_id": "DC4BFSABXOBNVJVWI2ETCSLI", "catalog_version": 1743632309931, "quantity": "1", "name": "5 pcs Pork Spring Rolls (<PERSON><PERSON> giò heo)", "variation_name": "Regular", "base_price_money": {"amount": 1050, "currency": "AUD"}, "gross_sales_money": {"amount": 1050, "currency": "AUD"}, "total_tax_money": {"amount": 95, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1050, "currency": "AUD"}, "variation_total_price_money": {"amount": 1050, "currency": "AUD"}, "applied_taxes": [{"uid": "7b860394-05d3-4873-b1f8-e66c92c8bbef", "tax_uid": "7b860394-05d3-4873-b1f8-e66c92c8bbef", "applied_money": {"amount": 95, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "a37224e8-21ce-4e06-bb41-b2e76adb6820", "catalog_object_id": "N7NLNPFHF5ZLSD3KQVWFKIVY", "catalog_version": 1743632309931, "quantity": "1", "name": "4 pcs Prawn Spring Rolls (Chả giò tôm)", "variation_name": "Regular", "base_price_money": {"amount": 1050, "currency": "AUD"}, "gross_sales_money": {"amount": 1050, "currency": "AUD"}, "total_tax_money": {"amount": 96, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1050, "currency": "AUD"}, "variation_total_price_money": {"amount": 1050, "currency": "AUD"}, "applied_taxes": [{"uid": "7b860394-05d3-4873-b1f8-e66c92c8bbef", "tax_uid": "7b860394-05d3-4873-b1f8-e66c92c8bbef", "applied_money": {"amount": 96, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "bac34f94-3338-4ddf-9fbf-737b5b2ff787", "catalog_object_id": "ARS3TZD7Z3EG2LHDRK5BJ6GG", "catalog_version": 1743632309931, "quantity": "2", "name": "Charcoal chicken with broken rice (Cơm gà nướng)", "variation_name": "Regular", "base_price_money": {"amount": 1750, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 3500, "currency": "AUD"}, "total_tax_money": {"amount": 318, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 3500, "currency": "AUD"}, "variation_total_price_money": {"amount": 3500, "currency": "AUD"}, "applied_taxes": [{"uid": "7b860394-05d3-4873-b1f8-e66c92c8bbef", "tax_uid": "7b860394-05d3-4873-b1f8-e66c92c8bbef", "applied_money": {"amount": 318, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "4990eba2-15fb-44b4-b0f1-5008915a4e82", "catalog_object_id": "NFZTLUIUVJGPJC2KQVY37D7A", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON><PERSON><PERSON> with chicken (Bún gà)", "variation_name": "Regular", "base_price_money": {"amount": 1750, "currency": "AUD"}, "gross_sales_money": {"amount": 1750, "currency": "AUD"}, "total_tax_money": {"amount": 159, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1750, "currency": "AUD"}, "variation_total_price_money": {"amount": 1750, "currency": "AUD"}, "applied_taxes": [{"uid": "7b860394-05d3-4873-b1f8-e66c92c8bbef", "tax_uid": "7b860394-05d3-4873-b1f8-e66c92c8bbef", "applied_money": {"amount": 159, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "7b860394-05d3-4873-b1f8-e66c92c8bbef", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 668, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T08:31:16Z", "updated_at": "2025-05-25T08:31:19.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 668, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 7350, "currency": "AUD"}, "closed_at": "2025-05-25T08:31:19.000Z", "tenders": [{"id": "nxLSUdnyzgb0d0E22e0mXM0kvaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "u6owB5x7hnsRY1vWWLczltreV", "created_at": "2025-05-25T08:32:30Z", "amount_money": {"amount": 7350, "currency": "AUD"}, "processing_fee_money": {"amount": 118, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "VISA", "last_4": "7861", "fingerprint": "sq-1-jk5mCvn0jyfjz1GIBBhAapZTstaRqLuqDGJNJtl_FI3IbUPe437cK6ba_icRVAxJxg"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 7350, "currency": "AUD"}, "tax_money": {"amount": 668, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "5", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "cHlLg2bg8Xc5demVZiPftVYfisGZY", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "4129bc10-6e81-4137-9421-88c26bfb22b4", "catalog_object_id": "XN26JPNMEXTCJJH6O7P4D6F5", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON> (Bún chả Hà Nội)", "variation_name": "Regular", "base_price_money": {"amount": 1850, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 1850, "currency": "AUD"}, "total_tax_money": {"amount": 168, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1850, "currency": "AUD"}, "variation_total_price_money": {"amount": 1850, "currency": "AUD"}, "applied_taxes": [{"uid": "b4f15a9e-e8e0-4339-a4bf-9b1578ad9efa", "tax_uid": "b4f15a9e-e8e0-4339-a4bf-9b1578ad9efa", "applied_money": {"amount": 168, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "*************-49cc-81b4-40ff075ce20c", "catalog_object_id": "U7TVTKLJ4Z5FWXIHD7MLX2LL", "catalog_version": 1747462207006, "quantity": "1", "name": "<PERSON><PERSON> chua r<PERSON>", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "b4f15a9e-e8e0-4339-a4bf-9b1578ad9efa", "tax_uid": "b4f15a9e-e8e0-4339-a4bf-9b1578ad9efa", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "b4f15a9e-e8e0-4339-a4bf-9b1578ad9efa", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10.0", "type": "INCLUSIVE", "applied_money": {"amount": 300, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T08:14:32.539Z", "updated_at": "2025-05-25T08:14:34.000Z", "state": "COMPLETED", "version": 4, "total_tax_money": {"amount": 300, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 3300, "currency": "AUD"}, "closed_at": "2025-05-25T08:14:33.027Z", "tenders": [{"id": "zhYqqzmgHTQBjji1q2WUnxQX7jYZY", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "cHlLg2bg8Xc5demVZiPftVYfisGZY", "created_at": "2025-05-25T08:14:32Z", "amount_money": {"amount": 3300, "currency": "AUD"}, "type": "CASH", "cash_details": {"buyer_tendered_money": {"amount": 5000, "currency": "AUD"}, "change_back_money": {"amount": 1700, "currency": "AUD"}}, "payment_id": "zhYqqzmgHTQBjji1q2WUnxQX7jYZY"}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "net_amounts": {"total_money": {"amount": 3300, "currency": "AUD"}, "tax_money": {"amount": 300, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "12", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "O8HlEYlYuZkl71rYVBfwshfeV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "97954f88-35b9-441d-9616-cc1b368a0f8c", "catalog_object_id": "WLOA62RODZIMIB6RZPJBUNEY", "catalog_version": 1743632309931, "quantity": "1", "name": "b<PERSON>h tr<PERSON>g n<PERSON> ( 2pcs Grilled Rice Paper Rolls)", "variation_name": "Regular", "base_price_money": {"amount": 1790, "currency": "AUD"}, "gross_sales_money": {"amount": 1790, "currency": "AUD"}, "total_tax_money": {"amount": 163, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1790, "currency": "AUD"}, "variation_total_price_money": {"amount": 1790, "currency": "AUD"}, "applied_taxes": [{"uid": "41cff645-f911-4c85-97d5-e98030832e98", "tax_uid": "41cff645-f911-4c85-97d5-e98030832e98", "applied_money": {"amount": 163, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "23f06514-2434-45b1-8874-09c166b3928a", "catalog_object_id": "7HDRRPGYGFTZ763Z3U2GBRZK", "catalog_version": 1743632309931, "quantity": "1", "name": "bò lá lốt", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "41cff645-f911-4c85-97d5-e98030832e98", "tax_uid": "41cff645-f911-4c85-97d5-e98030832e98", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "41cff645-f911-4c85-97d5-e98030832e98", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 295, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-25T08:08:37Z", "updated_at": "2025-05-25T08:08:40.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 295, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 3240, "currency": "AUD"}, "closed_at": "2025-05-25T08:08:40.000Z", "tenders": [{"id": "V6QCxf0JAkoEwYJ6rKLFTBx6vaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "O8HlEYlYuZkl71rYVBfwshfeV", "created_at": "2025-05-25T08:09:51Z", "amount_money": {"amount": 3240, "currency": "AUD"}, "processing_fee_money": {"amount": 52, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "AMERICAN_EXPRESS", "last_4": "1006", "fingerprint": "sq-1--QnRFpCed0sNiOoFkVWkmlELsi552b9oBOHTwlX3WluT01VVWKD2ModuvDBfIKGvBg"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 3240, "currency": "AUD"}, "tax_money": {"amount": 295, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "5", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "8jHnnSDNG3v0m1EvNfdnP0leV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "ce9c4539-bd31-4472-98ee-7b1aa7a56c06", "catalog_object_id": "WLOA62RODZIMIB6RZPJBUNEY", "catalog_version": 1743632309931, "quantity": "1", "name": "b<PERSON>h tr<PERSON>g n<PERSON> ( 2pcs Grilled Rice Paper Rolls)", "variation_name": "Regular", "base_price_money": {"amount": 1790, "currency": "AUD"}, "gross_sales_money": {"amount": 1790, "currency": "AUD"}, "total_tax_money": {"amount": 163, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1790, "currency": "AUD"}, "variation_total_price_money": {"amount": 1790, "currency": "AUD"}, "applied_taxes": [{"uid": "9ee5d524-9bed-476b-8e59-fd20dac51724", "tax_uid": "9ee5d524-9bed-476b-8e59-fd20dac51724", "applied_money": {"amount": 163, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "9ee5d524-9bed-476b-8e59-fd20dac51724", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 163, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-24T15:32:43Z", "updated_at": "2025-05-24T15:32:47.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 163, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1790, "currency": "AUD"}, "closed_at": "2025-05-24T15:32:47.000Z", "tenders": [{"id": "PVdFzxyqI6VT3yTMH6u2yJVFvaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "8jHnnSDNG3v0m1EvNfdnP0leV", "created_at": "2025-05-24T15:33:57Z", "amount_money": {"amount": 1790, "currency": "AUD"}, "processing_fee_money": {"amount": 29, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "MASTERCARD", "last_4": "3675", "fingerprint": "sq-1-7kQunAPE1OIwgiWiN_lZ5w421nxa7uv-MLIfh_3nrB0C8xGn8JvGH_I-Wox6M204RQ"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 1790, "currency": "AUD"}, "tax_money": {"amount": 163, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "12ta", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "sbDrgrUU7X0CzBrLW1LAPSreV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "02e56435-91c5-4480-83ec-13f1c1a82e40", "catalog_object_id": "XN26JPNMEXTCJJH6O7P4D6F5", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON> (Bún chả Hà Nội)", "variation_name": "Regular", "base_price_money": {"amount": 1850, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 1850, "currency": "AUD"}, "total_tax_money": {"amount": 168, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1850, "currency": "AUD"}, "variation_total_price_money": {"amount": 1850, "currency": "AUD"}, "applied_taxes": [{"uid": "e6c492fe-14a0-4b85-8127-be596e4a870f", "tax_uid": "e6c492fe-14a0-4b85-8127-be596e4a870f", "applied_money": {"amount": 168, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "24672d8b-f985-410c-906c-e9f8d6384c5f", "catalog_object_id": "CPD7YUKPSBQCTRDYBLAV7EV4", "catalog_version": 1743632309931, "quantity": "1", "name": "Charcoal Pork Chop Combo", "variation_name": "Regular", "base_price_money": {"amount": 2250, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 2250, "currency": "AUD"}, "total_tax_money": {"amount": 205, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2250, "currency": "AUD"}, "variation_total_price_money": {"amount": 2250, "currency": "AUD"}, "applied_taxes": [{"uid": "e6c492fe-14a0-4b85-8127-be596e4a870f", "tax_uid": "e6c492fe-14a0-4b85-8127-be596e4a870f", "applied_money": {"amount": 205, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "8639f7ae-1893-4089-af22-7e22193a41ad", "catalog_object_id": "U7TVTKLJ4Z5FWXIHD7MLX2LL", "catalog_version": 1747462207006, "quantity": "1", "name": "<PERSON><PERSON> chua r<PERSON>", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "e6c492fe-14a0-4b85-8127-be596e4a870f", "tax_uid": "e6c492fe-14a0-4b85-8127-be596e4a870f", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "e6c492fe-14a0-4b85-8127-be596e4a870f", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 505, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-24T15:08:04Z", "updated_at": "2025-05-24T15:08:08.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 505, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 5550, "currency": "AUD"}, "closed_at": "2025-05-24T15:08:08.000Z", "tenders": [{"id": "n3cBIFPhIIZ0D9wu5ayU8PLFvaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "sbDrgrUU7X0CzBrLW1LAPSreV", "created_at": "2025-05-24T15:09:18Z", "amount_money": {"amount": 5550, "currency": "AUD"}, "processing_fee_money": {"amount": 89, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "EFTPOS", "last_4": "3791", "fingerprint": "sq-1-NRMLl2AxdktYR8ldkLcXGh003hAe9qBZDj0Z8D6awA9T2-U5GA0o6AqAAPjmw69BYA"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 5550, "currency": "AUD"}, "tax_money": {"amount": 505, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "5", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "kBO2YnGqitBY1OxAyQjrhWneV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "c52d9987-c7b8-42db-9fec-cf5abc2b435d", "catalog_object_id": "WLOA62RODZIMIB6RZPJBUNEY", "catalog_version": 1743632309931, "quantity": "1", "name": "b<PERSON>h tr<PERSON>g n<PERSON> ( 2pcs Grilled Rice Paper Rolls)", "variation_name": "Regular", "base_price_money": {"amount": 1790, "currency": "AUD"}, "gross_sales_money": {"amount": 1790, "currency": "AUD"}, "total_tax_money": {"amount": 163, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1790, "currency": "AUD"}, "variation_total_price_money": {"amount": 1790, "currency": "AUD"}, "applied_taxes": [{"uid": "311d38fc-a9f4-489e-aaaa-6c5ae045b244", "tax_uid": "311d38fc-a9f4-489e-aaaa-6c5ae045b244", "applied_money": {"amount": 163, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "311d38fc-a9f4-489e-aaaa-6c5ae045b244", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 163, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-24T14:48:55Z", "updated_at": "2025-05-24T14:48:58.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 163, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1790, "currency": "AUD"}, "closed_at": "2025-05-24T14:48:58.000Z", "tenders": [{"id": "5k886FsY8dmpBZZ859WfikQxuaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "kBO2YnGqitBY1OxAyQjrhWneV", "created_at": "2025-05-24T14:50:09Z", "amount_money": {"amount": 1790, "currency": "AUD"}, "processing_fee_money": {"amount": 29, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "MASTERCARD", "last_4": "7393", "fingerprint": "sq-1-jbbovPUxVQT_cMpVJkOFeghKhiOmjJ4DOVVWEBK6oZS_EViJY48JXiOGDi6pfJlbwQ"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 1790, "currency": "AUD"}, "tax_money": {"amount": 163, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "5ta", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "0rxnQrZMgt4fZphafahUrDheV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "31f352fc-2819-4a36-9eae-74f09bd554db", "catalog_object_id": "7HDRRPGYGFTZ763Z3U2GBRZK", "catalog_version": 1743632309931, "quantity": "2", "name": "bò lá lốt", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 2900, "currency": "AUD"}, "total_tax_money": {"amount": 264, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2900, "currency": "AUD"}, "variation_total_price_money": {"amount": 2900, "currency": "AUD"}, "applied_taxes": [{"uid": "d8ee5b80-8be9-4d9e-9907-2d78d4b5199e", "tax_uid": "d8ee5b80-8be9-4d9e-9907-2d78d4b5199e", "applied_money": {"amount": 264, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "d8ee5b80-8be9-4d9e-9907-2d78d4b5199e", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 264, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-24T14:25:08Z", "updated_at": "2025-05-24T14:25:11.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 264, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2900, "currency": "AUD"}, "closed_at": "2025-05-24T14:25:11.000Z", "tenders": [{"id": "9OiDtDqGpteBnJ8z1QPEcrIBwaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "0rxnQrZMgt4fZphafahUrDheV", "created_at": "2025-05-24T14:26:22Z", "amount_money": {"amount": 2900, "currency": "AUD"}, "processing_fee_money": {"amount": 46, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "EFTPOS", "last_4": "2319", "fingerprint": "sq-1-XOq-N6p-kesSdTdwcss_pDDB8uWoyHY9C_gSo8OWofLR1ipPzB7fTunFE-pWp0qAhQ"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 2900, "currency": "AUD"}, "tax_money": {"amount": 264, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "12ta", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "cDN69UqK1DqVmzQ3O5lOTVOXOaEZY", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "d8bca974-a32c-4c36-8d5b-cfe6c14b5da9", "catalog_object_id": "ZRWI7HUU5SX3AUGYLFBBN4NY", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON><PERSON> vị<PERSON> lộn ( 2 pcs Balut)", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "95e9e421-8950-44ad-a235-3e468404394c", "tax_uid": "95e9e421-8950-44ad-a235-3e468404394c", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "95e9e421-8950-44ad-a235-3e468404394c", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10.0", "type": "INCLUSIVE", "applied_money": {"amount": 132, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-24T14:11:31.070Z", "updated_at": "2025-05-24T14:11:32.000Z", "state": "COMPLETED", "version": 4, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "closed_at": "2025-05-24T14:11:31.542Z", "tenders": [{"id": "NSYdQt76yDopL4Zgnq9CQWk2WJVZY", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "cDN69UqK1DqVmzQ3O5lOTVOXOaEZY", "created_at": "2025-05-24T14:11:31Z", "amount_money": {"amount": 1450, "currency": "AUD"}, "type": "CASH", "cash_details": {"buyer_tendered_money": {"amount": 2000, "currency": "AUD"}, "change_back_money": {"amount": 550, "currency": "AUD"}}, "payment_id": "NSYdQt76yDopL4Zgnq9CQWk2WJVZY"}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "net_amounts": {"total_money": {"amount": 1450, "currency": "AUD"}, "tax_money": {"amount": 132, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "12ta", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "UrFBYef63qOK3QQyTpMek9yeV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "c31d7971-3486-4b07-a341-fe3a15647540", "catalog_object_id": "CPD7YUKPSBQCTRDYBLAV7EV4", "catalog_version": 1743632309931, "quantity": "1", "name": "Charcoal Pork Chop Combo", "variation_name": "Regular", "base_price_money": {"amount": 2250, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 2250, "currency": "AUD"}, "total_tax_money": {"amount": 205, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2250, "currency": "AUD"}, "variation_total_price_money": {"amount": 2250, "currency": "AUD"}, "applied_taxes": [{"uid": "9340eb85-0bec-460a-b8eb-5db40643c68c", "tax_uid": "9340eb85-0bec-460a-b8eb-5db40643c68c", "applied_money": {"amount": 205, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "9340eb85-0bec-460a-b8eb-5db40643c68c", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 205, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-24T13:43:47Z", "updated_at": "2025-05-24T13:43:50.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 205, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2250, "currency": "AUD"}, "closed_at": "2025-05-24T13:43:50.000Z", "tenders": [{"id": "TTJwX0rwOFPFfMdXiuU61J9ZvaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "UrFBYef63qOK3QQyTpMek9yeV", "created_at": "2025-05-24T13:45:01Z", "amount_money": {"amount": 2250, "currency": "AUD"}, "processing_fee_money": {"amount": 36, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "MASTERCARD", "last_4": "3905", "fingerprint": "sq-1-srZGzMnXpNGMlfDMOAajHLHJyHsH5vagDh1Zbda_TyF_a_2La7CLtwhvJIV0OJ437Q"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 2250, "currency": "AUD"}, "tax_money": {"amount": 205, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "12", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "UhwPRDI86r1vp6PHfuThgibQfh8YY", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "8be388f8-d2d6-4c12-8d3a-acf0a5af2795", "catalog_object_id": "DC4BFSABXOBNVJVWI2ETCSLI", "catalog_version": 1743632309931, "quantity": "1", "name": "5 pcs Pork Spring Rolls (<PERSON><PERSON> giò heo)", "variation_name": "Regular", "base_price_money": {"amount": 1050, "currency": "AUD"}, "gross_sales_money": {"amount": 1050, "currency": "AUD"}, "total_tax_money": {"amount": 95, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1050, "currency": "AUD"}, "variation_total_price_money": {"amount": 1050, "currency": "AUD"}, "applied_taxes": [{"uid": "5ae0946b-2e57-4359-8161-27a0caaaf29b", "tax_uid": "5ae0946b-2e57-4359-8161-27a0caaaf29b", "applied_money": {"amount": 95, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "e46c1ba9-f7db-49a5-b4d8-a849e1aaf495", "catalog_object_id": "I2U35K7L5XNVKDR2NPGGQCFJ", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON>", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "5ae0946b-2e57-4359-8161-27a0caaaf29b", "tax_uid": "5ae0946b-2e57-4359-8161-27a0caaaf29b", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "010bbcc6-90e3-4a66-a080-a164d9be1e48", "catalog_object_id": "U7TVTKLJ4Z5FWXIHD7MLX2LL", "catalog_version": 1747462207006, "quantity": "1", "name": "<PERSON><PERSON> chua r<PERSON>", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "5ae0946b-2e57-4359-8161-27a0caaaf29b", "tax_uid": "5ae0946b-2e57-4359-8161-27a0caaaf29b", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "fc096e4e-816e-47c0-8a24-1eb18ebd2f4f", "catalog_object_id": "DC4BFSABXOBNVJVWI2ETCSLI", "catalog_version": 1743632309931, "quantity": "1", "name": "5 pcs Pork Spring Rolls (<PERSON><PERSON> giò heo)", "variation_name": "Regular", "base_price_money": {"amount": 1050, "currency": "AUD"}, "gross_sales_money": {"amount": 1050, "currency": "AUD"}, "total_tax_money": {"amount": 95, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1050, "currency": "AUD"}, "variation_total_price_money": {"amount": 1050, "currency": "AUD"}, "applied_taxes": [{"uid": "5ae0946b-2e57-4359-8161-27a0caaaf29b", "tax_uid": "5ae0946b-2e57-4359-8161-27a0caaaf29b", "applied_money": {"amount": 95, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "781e7442-db10-4503-ba95-72af8071c0db", "catalog_object_id": "WLOA62RODZIMIB6RZPJBUNEY", "catalog_version": 1743632309931, "quantity": "1", "name": "b<PERSON>h tr<PERSON>g n<PERSON> ( 2pcs Grilled Rice Paper Rolls)", "variation_name": "Regular", "base_price_money": {"amount": 1790, "currency": "AUD"}, "gross_sales_money": {"amount": 1790, "currency": "AUD"}, "total_tax_money": {"amount": 163, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1790, "currency": "AUD"}, "variation_total_price_money": {"amount": 1790, "currency": "AUD"}, "applied_taxes": [{"uid": "5ae0946b-2e57-4359-8161-27a0caaaf29b", "tax_uid": "5ae0946b-2e57-4359-8161-27a0caaaf29b", "applied_money": {"amount": 163, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "5ae0946b-2e57-4359-8161-27a0caaaf29b", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10.0", "type": "INCLUSIVE", "applied_money": {"amount": 617, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-24T13:39:17.341Z", "updated_at": "2025-05-24T13:39:19.000Z", "state": "COMPLETED", "version": 4, "total_tax_money": {"amount": 617, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 6790, "currency": "AUD"}, "closed_at": "2025-05-24T13:39:17.827Z", "tenders": [{"id": "NwT5dMcTKrTuqz7m5ErrU4es9F9YY", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "UhwPRDI86r1vp6PHfuThgibQfh8YY", "created_at": "2025-05-24T13:39:17Z", "amount_money": {"amount": 6790, "currency": "AUD"}, "type": "CASH", "cash_details": {"buyer_tendered_money": {"amount": 10000, "currency": "AUD"}, "change_back_money": {"amount": 3210, "currency": "AUD"}}, "payment_id": "NwT5dMcTKrTuqz7m5ErrU4es9F9YY"}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "net_amounts": {"total_money": {"amount": 6790, "currency": "AUD"}, "tax_money": {"amount": 617, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "5", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "QdnFORVtJUXqFQYjMzhmoMVE5EDZY", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "3852709e-a20e-47c5-a1fb-9fd4de1fd012", "catalog_object_id": "ZRWI7HUU5SX3AUGYLFBBN4NY", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON><PERSON> vị<PERSON> lộn ( 2 pcs Balut)", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "6c85f9e5-e128-48c2-a501-86280d6cb97e", "tax_uid": "6c85f9e5-e128-48c2-a501-86280d6cb97e", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "8643d159-c3b5-48ad-a282-84afabb6a44e", "catalog_object_id": "UXFBOISXB5RDGIXJAHVD3WIW", "catalog_version": 1743632309931, "quantity": "2", "name": "Coke", "variation_name": "Regular", "base_price_money": {"amount": 350, "currency": "AUD"}, "gross_sales_money": {"amount": 700, "currency": "AUD"}, "total_tax_money": {"amount": 63, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 700, "currency": "AUD"}, "variation_total_price_money": {"amount": 700, "currency": "AUD"}, "applied_taxes": [{"uid": "6c85f9e5-e128-48c2-a501-86280d6cb97e", "tax_uid": "6c85f9e5-e128-48c2-a501-86280d6cb97e", "applied_money": {"amount": 63, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "6c85f9e5-e128-48c2-a501-86280d6cb97e", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10.0", "type": "INCLUSIVE", "applied_money": {"amount": 195, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-24T13:30:01.754Z", "updated_at": "2025-05-24T13:30:03.000Z", "state": "COMPLETED", "version": 4, "total_tax_money": {"amount": 195, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2150, "currency": "AUD"}, "closed_at": "2025-05-24T13:30:02.227Z", "tenders": [{"id": "deAKZlOn6mlbXPJKpMO48sovzBXZY", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "QdnFORVtJUXqFQYjMzhmoMVE5EDZY", "created_at": "2025-05-24T13:30:02Z", "amount_money": {"amount": 2150, "currency": "AUD"}, "type": "CASH", "cash_details": {"buyer_tendered_money": {"amount": 2150, "currency": "AUD"}, "change_back_money": {"amount": 0, "currency": "AUD"}}, "payment_id": "deAKZlOn6mlbXPJKpMO48sovzBXZY"}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "net_amounts": {"total_money": {"amount": 2150, "currency": "AUD"}, "tax_money": {"amount": 195, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "12", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "uorcyUnOaGIe1oqA0glAMTieV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "f2341a2b-cb07-49e0-b9bc-743d4cf081db", "catalog_object_id": "WLOA62RODZIMIB6RZPJBUNEY", "catalog_version": 1743632309931, "quantity": "1", "name": "b<PERSON>h tr<PERSON>g n<PERSON> ( 2pcs Grilled Rice Paper Rolls)", "variation_name": "Regular", "base_price_money": {"amount": 1790, "currency": "AUD"}, "gross_sales_money": {"amount": 1790, "currency": "AUD"}, "total_tax_money": {"amount": 163, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1790, "currency": "AUD"}, "variation_total_price_money": {"amount": 1790, "currency": "AUD"}, "applied_taxes": [{"uid": "909fefff-4045-4454-b0d6-7ccb21093920", "tax_uid": "909fefff-4045-4454-b0d6-7ccb21093920", "applied_money": {"amount": 163, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "4e4ee51e-ddcf-45d6-8156-b5d595a1df9c", "catalog_object_id": "U7TVTKLJ4Z5FWXIHD7MLX2LL", "catalog_version": 1747462207006, "quantity": "1", "name": "<PERSON><PERSON> chua r<PERSON>", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "909fefff-4045-4454-b0d6-7ccb21093920", "tax_uid": "909fefff-4045-4454-b0d6-7ccb21093920", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "909fefff-4045-4454-b0d6-7ccb21093920", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 295, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-24T13:28:46Z", "updated_at": "2025-05-24T13:28:50.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 295, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 3240, "currency": "AUD"}, "closed_at": "2025-05-24T13:28:50.000Z", "tenders": [{"id": "RiJYlKHDMEt9xqJDgDIzWQ8YuaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "uorcyUnOaGIe1oqA0glAMTieV", "created_at": "2025-05-24T13:30:00Z", "amount_money": {"amount": 3240, "currency": "AUD"}, "processing_fee_money": {"amount": 52, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "MASTERCARD", "last_4": "8389", "fingerprint": "sq-1-ZDvaUA3DmqThODBo6O4Nq1NSm86unw5vVANLoVn-xeg6xKQcU_sIPpuzDrottTsZFw"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 3240, "currency": "AUD"}, "tax_money": {"amount": 295, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "6ta", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "miQbKd1g9UqUmpXNCBPSIa5eV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "19e64871-190a-4466-89f1-946721dac63e", "catalog_object_id": "DC4BFSABXOBNVJVWI2ETCSLI", "catalog_version": 1743632309931, "quantity": "1", "name": "5 pcs Pork Spring Rolls (<PERSON><PERSON> giò heo)", "variation_name": "Regular", "base_price_money": {"amount": 1050, "currency": "AUD"}, "gross_sales_money": {"amount": 1050, "currency": "AUD"}, "total_tax_money": {"amount": 95, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1050, "currency": "AUD"}, "variation_total_price_money": {"amount": 1050, "currency": "AUD"}, "applied_taxes": [{"uid": "e04337e5-17bd-4f86-bd29-b3d875135462", "tax_uid": "e04337e5-17bd-4f86-bd29-b3d875135462", "applied_money": {"amount": 95, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "e04337e5-17bd-4f86-bd29-b3d875135462", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 95, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-24T13:27:49Z", "updated_at": "2025-05-24T13:27:53.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 95, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1050, "currency": "AUD"}, "closed_at": "2025-05-24T13:27:53.000Z", "tenders": [{"id": "dO0ETJT6mrYLzve8UnSmyxPLuaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "miQbKd1g9UqUmpXNCBPSIa5eV", "created_at": "2025-05-24T13:29:02Z", "amount_money": {"amount": 1050, "currency": "AUD"}, "processing_fee_money": {"amount": 17, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "MASTERCARD", "last_4": "3288", "fingerprint": "sq-1-nmsm6TkITQ_2O-3O47AfxZ3rXyYR66y5lE3ajB7kXmh9y-DkQg1uV4FL56A05otDOw"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 1050, "currency": "AUD"}, "tax_money": {"amount": 95, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "4ta", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "c5UuGNsJWh9wHisAybZVX69eV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "fde37df9-e83e-42f9-a6cb-5c07ea237b97", "catalog_object_id": "WLOA62RODZIMIB6RZPJBUNEY", "catalog_version": 1743632309931, "quantity": "1", "name": "b<PERSON>h tr<PERSON>g n<PERSON> ( 2pcs Grilled Rice Paper Rolls)", "variation_name": "Regular", "base_price_money": {"amount": 1790, "currency": "AUD"}, "gross_sales_money": {"amount": 1790, "currency": "AUD"}, "total_tax_money": {"amount": 163, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1790, "currency": "AUD"}, "variation_total_price_money": {"amount": 1790, "currency": "AUD"}, "applied_taxes": [{"uid": "ee44e074-cfe3-4d3d-badb-6b96414ec9fa", "tax_uid": "ee44e074-cfe3-4d3d-badb-6b96414ec9fa", "applied_money": {"amount": 163, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "ee44e074-cfe3-4d3d-badb-6b96414ec9fa", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 163, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-24T13:26:35Z", "updated_at": "2025-05-24T13:26:39.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 163, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1790, "currency": "AUD"}, "closed_at": "2025-05-24T13:26:39.000Z", "tenders": [{"id": "bp8NNhVFnyPjPEXEi5CK5es0vaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "c5UuGNsJWh9wHisAybZVX69eV", "created_at": "2025-05-24T13:27:49Z", "amount_money": {"amount": 1790, "currency": "AUD"}, "processing_fee_money": {"amount": 29, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "MASTERCARD", "last_4": "0692", "fingerprint": "sq-1-qywQqhCD2L2H9Pm2BBLZ9_k-Cs-oXFnxy-VTWOMXD9pYs1pjjjBhTr8sZDDib3Ee6w"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 1790, "currency": "AUD"}, "tax_money": {"amount": 163, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "9ta", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "K2zwQFpEc8V2PsamWG5KKJQZagSZY", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "82ffae31-4c8b-47a6-af6f-74f2b8d3ed6f", "catalog_object_id": "CPD7YUKPSBQCTRDYBLAV7EV4", "catalog_version": 1743632309931, "quantity": "3", "name": "Charcoal Pork Chop Combo", "variation_name": "Regular", "base_price_money": {"amount": 2250, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 6750, "currency": "AUD"}, "total_tax_money": {"amount": 614, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 6750, "currency": "AUD"}, "variation_total_price_money": {"amount": 6750, "currency": "AUD"}, "applied_taxes": [{"uid": "4ba2924e-024b-418b-bd8b-b5b985ae6760", "tax_uid": "4ba2924e-024b-418b-bd8b-b5b985ae6760", "applied_money": {"amount": 614, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "6003e23c-953b-45f5-851c-7ccc85826944", "catalog_object_id": "4GTTSUDUKSDV5PGT2MXKKKPJ", "catalog_version": 1743632309931, "quantity": "1", "name": "Water", "variation_name": "Regular", "base_price_money": {"amount": 350, "currency": "AUD"}, "gross_sales_money": {"amount": 350, "currency": "AUD"}, "total_tax_money": {"amount": 32, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 350, "currency": "AUD"}, "variation_total_price_money": {"amount": 350, "currency": "AUD"}, "applied_taxes": [{"uid": "4ba2924e-024b-418b-bd8b-b5b985ae6760", "tax_uid": "4ba2924e-024b-418b-bd8b-b5b985ae6760", "applied_money": {"amount": 32, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "0033e3ce-3565-4dfa-bbd8-a5c5ce5b9fb1", "catalog_object_id": "UXFBOISXB5RDGIXJAHVD3WIW", "catalog_version": 1743632309931, "quantity": "2", "name": "Coke", "variation_name": "Regular", "base_price_money": {"amount": 350, "currency": "AUD"}, "gross_sales_money": {"amount": 700, "currency": "AUD"}, "total_tax_money": {"amount": 63, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 700, "currency": "AUD"}, "variation_total_price_money": {"amount": 700, "currency": "AUD"}, "applied_taxes": [{"uid": "4ba2924e-024b-418b-bd8b-b5b985ae6760", "tax_uid": "4ba2924e-024b-418b-bd8b-b5b985ae6760", "applied_money": {"amount": 63, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "4ba2924e-024b-418b-bd8b-b5b985ae6760", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10.0", "type": "INCLUSIVE", "applied_money": {"amount": 709, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-24T13:25:19.834Z", "updated_at": "2025-05-24T13:25:21.000Z", "state": "COMPLETED", "version": 4, "total_tax_money": {"amount": 709, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 7800, "currency": "AUD"}, "closed_at": "2025-05-24T13:25:20.315Z", "tenders": [{"id": "B2vQdBMAbvzYsTIGzSBNtDBBcdaZY", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "K2zwQFpEc8V2PsamWG5KKJQZagSZY", "created_at": "2025-05-24T13:25:20Z", "amount_money": {"amount": 7800, "currency": "AUD"}, "type": "CASH", "cash_details": {"buyer_tendered_money": {"amount": 9000, "currency": "AUD"}, "change_back_money": {"amount": 1200, "currency": "AUD"}}, "payment_id": "B2vQdBMAbvzYsTIGzSBNtDBBcdaZY"}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "net_amounts": {"total_money": {"amount": 7800, "currency": "AUD"}, "tax_money": {"amount": 709, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "5", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "0zJn50GEizHsA6i3czGZmY4eV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "8f8de831-a322-4d4c-ac88-9b4cd9f4d32a", "catalog_object_id": "N7NLNPFHF5ZLSD3KQVWFKIVY", "catalog_version": 1743632309931, "quantity": "1", "name": "4 pcs Prawn Spring Rolls (Chả giò tôm)", "variation_name": "Regular", "base_price_money": {"amount": 1050, "currency": "AUD"}, "gross_sales_money": {"amount": 1050, "currency": "AUD"}, "total_tax_money": {"amount": 95, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1050, "currency": "AUD"}, "variation_total_price_money": {"amount": 1050, "currency": "AUD"}, "applied_taxes": [{"uid": "4571eb6c-01ef-4ad8-b70d-a760de0fe02f", "tax_uid": "4571eb6c-01ef-4ad8-b70d-a760de0fe02f", "applied_money": {"amount": 95, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "e2b3cad6-6c7b-4323-9a70-d646e36ae21a", "catalog_object_id": "4GTTSUDUKSDV5PGT2MXKKKPJ", "catalog_version": 1743632309931, "quantity": "1", "name": "Water", "variation_name": "Regular", "base_price_money": {"amount": 350, "currency": "AUD"}, "gross_sales_money": {"amount": 350, "currency": "AUD"}, "total_tax_money": {"amount": 32, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 350, "currency": "AUD"}, "variation_total_price_money": {"amount": 350, "currency": "AUD"}, "applied_taxes": [{"uid": "4571eb6c-01ef-4ad8-b70d-a760de0fe02f", "tax_uid": "4571eb6c-01ef-4ad8-b70d-a760de0fe02f", "applied_money": {"amount": 32, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "4571eb6c-01ef-4ad8-b70d-a760de0fe02f", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 127, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-24T13:21:40Z", "updated_at": "2025-05-24T13:21:44.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 127, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1400, "currency": "AUD"}, "closed_at": "2025-05-24T13:21:44.000Z", "tenders": [{"id": "pIi8giOmPI6BjejvBHmlx7ZpuaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "0zJn50GEizHsA6i3czGZmY4eV", "created_at": "2025-05-24T13:22:54Z", "amount_money": {"amount": 1400, "currency": "AUD"}, "processing_fee_money": {"amount": 22, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "MASTERCARD", "last_4": "8324", "fingerprint": "sq-1-KDmC3HmtoP_30Zq11wSw1JDVFFkHqa4Cx6Jc5lxybJM5iKu5HFiRHTIc7XnEndCOgQ"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 1400, "currency": "AUD"}, "tax_money": {"amount": 127, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "11", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "2qO2KYUDKQTGyyYD1yGK0W5eV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "cb5481b4-1e36-47b4-a755-8e77a548d8a8", "catalog_object_id": "OQQ6OJVHUOIKHMNLDUGX22AI", "catalog_version": 1743632309931, "quantity": "1", "name": "Chicken & Beef Phở", "variation_name": "Regular", "base_price_money": {"amount": 1750, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 1750, "currency": "AUD"}, "total_tax_money": {"amount": 159, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1750, "currency": "AUD"}, "variation_total_price_money": {"amount": 1750, "currency": "AUD"}, "applied_taxes": [{"uid": "6235c26c-1fc2-48a1-ad82-b2d1dbf178f8", "tax_uid": "6235c26c-1fc2-48a1-ad82-b2d1dbf178f8", "applied_money": {"amount": 159, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "6235c26c-1fc2-48a1-ad82-b2d1dbf178f8", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 159, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-24T13:16:11Z", "updated_at": "2025-05-24T13:16:14.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 159, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1750, "currency": "AUD"}, "closed_at": "2025-05-24T13:16:14.000Z", "tenders": [{"id": "FSYmBzZxTSYwjjEVs5mJlydcuaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "2qO2KYUDKQTGyyYD1yGK0W5eV", "created_at": "2025-05-24T13:17:25Z", "amount_money": {"amount": 1750, "currency": "AUD"}, "processing_fee_money": {"amount": 28, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "MASTERCARD", "last_4": "4018", "fingerprint": "sq-1--x29ucOGIQWcWpJuPZDpPqSR9O6oDA9V3pYbQE_MRsMbLio1aJw6H3ZJlts7-ZEhvw"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 1750, "currency": "AUD"}, "tax_money": {"amount": 159, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "11", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "EBCW5hSHsMdcKRgFI3VV1DgeV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "4bce83bc-bfd2-4415-997e-9bf4d6a54726", "catalog_object_id": "CPD7YUKPSBQCTRDYBLAV7EV4", "catalog_version": 1743632309931, "quantity": "1", "name": "Charcoal Pork Chop Combo", "variation_name": "Regular", "base_price_money": {"amount": 2250, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 2250, "currency": "AUD"}, "total_tax_money": {"amount": 205, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2250, "currency": "AUD"}, "variation_total_price_money": {"amount": 2250, "currency": "AUD"}, "applied_taxes": [{"uid": "083c4ef7-2d9a-4bce-9ac2-d2a664c9ce0b", "tax_uid": "083c4ef7-2d9a-4bce-9ac2-d2a664c9ce0b", "applied_money": {"amount": 205, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "083c4ef7-2d9a-4bce-9ac2-d2a664c9ce0b", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 205, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-24T13:14:20Z", "updated_at": "2025-05-24T13:14:22.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 205, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2250, "currency": "AUD"}, "closed_at": "2025-05-24T13:14:22.000Z", "tenders": [{"id": "ZaTy43bXgOQH3F2KFBE9H1XcvaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "EBCW5hSHsMdcKRgFI3VV1DgeV", "created_at": "2025-05-24T13:15:33Z", "amount_money": {"amount": 2250, "currency": "AUD"}, "processing_fee_money": {"amount": 36, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "EFTPOS", "last_4": "0543", "fingerprint": "sq-1-s5k6L69gFZJL9fhGaqs7zD3AdRDUhLi07lCniDiPL0m6F_qC5xLqrFA5s5NPz9lsKg"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 2250, "currency": "AUD"}, "tax_money": {"amount": 205, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "YDmmRw5Hs33gsnsSmYKaJI3eV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "6aac7dab-9562-465c-be12-68bbcca6aec8", "catalog_object_id": "ARS3TZD7Z3EG2LHDRK5BJ6GG", "catalog_version": 1743632309931, "quantity": "1", "name": "Charcoal chicken with broken rice (Cơm gà nướng)", "variation_name": "Regular", "base_price_money": {"amount": 1750, "currency": "AUD"}, "note": "", "gross_sales_money": {"amount": 1750, "currency": "AUD"}, "total_tax_money": {"amount": 159, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1750, "currency": "AUD"}, "variation_total_price_money": {"amount": 1750, "currency": "AUD"}, "applied_taxes": [{"uid": "1d648737-eda1-4850-8c6f-106a7884922a", "tax_uid": "1d648737-eda1-4850-8c6f-106a7884922a", "applied_money": {"amount": 159, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "1d648737-eda1-4850-8c6f-106a7884922a", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 159, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-24T12:54:13Z", "updated_at": "2025-05-24T12:54:16.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 159, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1750, "currency": "AUD"}, "closed_at": "2025-05-24T12:54:16.000Z", "tenders": [{"id": "bjzAcPDML0E55BkqAZ2bAoT3vaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "YDmmRw5Hs33gsnsSmYKaJI3eV", "created_at": "2025-05-24T12:55:27Z", "amount_money": {"amount": 1750, "currency": "AUD"}, "processing_fee_money": {"amount": 28, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "EFTPOS", "last_4": "7486", "fingerprint": "sq-1-iywiWep0NnGRIxy5o7qn5PHvNAtOwMvgMhj-F8HZKYe86udeEBK83VOjILB2R8uMaA"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 1750, "currency": "AUD"}, "tax_money": {"amount": 159, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "5ta", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}, {"id": "Ss2FuuYXAYClAoSH3fhaEt4eV", "location_id": "LXRJ28YWPJNBZ", "line_items": [{"uid": "3d3974ee-c3fe-4dac-a621-63e54f84d812", "catalog_object_id": "ZRWI7HUU5SX3AUGYLFBBN4NY", "catalog_version": 1743632309931, "quantity": "2", "name": "<PERSON><PERSON><PERSON> vị<PERSON> lộn ( 2 pcs Balut)", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 2900, "currency": "AUD"}, "total_tax_money": {"amount": 264, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 2900, "currency": "AUD"}, "variation_total_price_money": {"amount": 2900, "currency": "AUD"}, "applied_taxes": [{"uid": "03086022-a688-429d-9792-f49ac1c262f4", "tax_uid": "03086022-a688-429d-9792-f49ac1c262f4", "applied_money": {"amount": 264, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "3defb74b-0a51-4774-b3eb-b78fa50a2733", "catalog_object_id": "U7TVTKLJ4Z5FWXIHD7MLX2LL", "catalog_version": 1747462207006, "quantity": "1", "name": "<PERSON><PERSON> chua r<PERSON>", "variation_name": "Regular", "base_price_money": {"amount": 1450, "currency": "AUD"}, "gross_sales_money": {"amount": 1450, "currency": "AUD"}, "total_tax_money": {"amount": 132, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1450, "currency": "AUD"}, "variation_total_price_money": {"amount": 1450, "currency": "AUD"}, "applied_taxes": [{"uid": "03086022-a688-429d-9792-f49ac1c262f4", "tax_uid": "03086022-a688-429d-9792-f49ac1c262f4", "applied_money": {"amount": 132, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}, {"uid": "dabeffdd-bf85-4dc8-8c3d-a270761511b6", "catalog_object_id": "7FUO4AKVNNI6QYY5TPQ56DGO", "catalog_version": 1743632309931, "quantity": "1", "name": "<PERSON><PERSON><PERSON><PERSON> with Spring rolls (<PERSON><PERSON> chả chả giò)", "variation_name": "Regular", "base_price_money": {"amount": 1850, "currency": "AUD"}, "gross_sales_money": {"amount": 1850, "currency": "AUD"}, "total_tax_money": {"amount": 168, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 1850, "currency": "AUD"}, "variation_total_price_money": {"amount": 1850, "currency": "AUD"}, "applied_taxes": [{"uid": "03086022-a688-429d-9792-f49ac1c262f4", "tax_uid": "03086022-a688-429d-9792-f49ac1c262f4", "applied_money": {"amount": 168, "currency": "AUD"}, "auto_applied": true}], "item_type": "ITEM", "total_service_charge_money": {"amount": 0, "currency": "AUD"}}], "taxes": [{"uid": "03086022-a688-429d-9792-f49ac1c262f4", "catalog_object_id": "AUSSALESTAXMLAZ3WM6R0PZ8", "catalog_version": 1721300646775, "name": "GST", "percentage": "10", "type": "INCLUSIVE", "applied_money": {"amount": 564, "currency": "AUD"}, "scope": "LINE_ITEM"}], "created_at": "2025-05-24T12:41:33Z", "updated_at": "2025-05-24T12:41:36.000Z", "state": "COMPLETED", "total_tax_money": {"amount": 564, "currency": "AUD"}, "total_discount_money": {"amount": 0, "currency": "AUD"}, "total_tip_money": {"amount": 0, "currency": "AUD"}, "total_money": {"amount": 6200, "currency": "AUD"}, "closed_at": "2025-05-24T12:41:36.000Z", "tenders": [{"id": "FgNjyQMuIQ0K0FppXZUau6SPvaB", "location_id": "LXRJ28YWPJNBZ", "transaction_id": "Ss2FuuYXAYClAoSH3fhaEt4eV", "created_at": "2025-05-24T12:42:47Z", "amount_money": {"amount": 6200, "currency": "AUD"}, "processing_fee_money": {"amount": 99, "currency": "AUD"}, "type": "CARD", "card_details": {"status": "CAPTURED", "card": {"card_brand": "EFTPOS", "last_4": "9816", "fingerprint": "sq-1-0ASpNTM-jq6ngJbQ0CmRAJafWeUqfJszAUBFosWjG3NoAkiH-rJfxiAory_SH8d2ag"}, "entry_method": "CONTACTLESS"}}], "total_service_charge_money": {"amount": 0, "currency": "AUD"}, "return_amounts": {"total_money": {"amount": 0, "currency": "AUD"}, "tax_money": {"amount": 0, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "net_amounts": {"total_money": {"amount": 6200, "currency": "AUD"}, "tax_money": {"amount": 564, "currency": "AUD"}, "discount_money": {"amount": 0, "currency": "AUD"}, "tip_money": {"amount": 0, "currency": "AUD"}, "service_charge_money": {"amount": 0, "currency": "AUD"}}, "source": {}, "ticket_name": "5ta", "net_amount_due_money": {"amount": 0, "currency": "AUD"}}], "cursor": "gwQvCwppKJRuZIUW4iv5HMAlvmvFeYjufrvkmiMBZBGJeHGlJST6AwSXPweoKShMUVFP2FHUpG"}