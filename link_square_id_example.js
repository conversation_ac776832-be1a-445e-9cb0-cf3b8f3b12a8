// Example: How to link Square ID using the API
// You can run this in your browser console or create a simple script

async function linkItemToSquare(localItemId, squareItemId) {
  try {
    const response = await fetch('/api/square/simple-sync', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        itemId: localItemId,
        squareId: squareItemId
      })
    });

    const result = await response.json();
    console.log('Link result:', result);
    return result;
  } catch (error) {
    console.error('Error linking item:', error);
  }
}

// Example usage:
// linkItemToSquare('your-local-item-uuid', 'SQUARE_CATALOG_ITEM_ID');

// To find your local item IDs, you can also use:
async function findLocalItems(searchTerm) {
  try {
    const response = await fetch(`/api/items/menu?search=${encodeURIComponent(searchTerm)}`);
    const result = await response.json();
    console.log('Found items:', result.data);
    return result.data;
  } catch (error) {
    console.error('Error finding items:', error);
  }
}

// Example: Find pork chop items
// findLocalItems('pork');
