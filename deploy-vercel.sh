#!/bin/bash

# Colors for better readability
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Deploying Vietnamese Restaurant Inventory to Vercel...${NC}"

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo -e "${YELLOW}Vercel CLI not found. Installing...${NC}"
    npm install -g vercel
fi

# Login to Vercel (if not already logged in)
echo -e "${BLUE}Checking Vercel authentication...${NC}"
vercel whoami || vercel login

# Set environment variables for production
echo -e "${BLUE}Setting up environment variables...${NC}"
echo "Please make sure to set the following environment variables in your Vercel dashboard:"
echo "1. MONGODB_URI - Your MongoDB connection string"
echo "2. MONGODB_DB_NAME - restaurant-inventory"
echo "3. JWT_SECRET - A secure random string"
echo "4. SQUARE_ACCESS_TOKEN - Your Square API access token (optional)"
echo "5. SQUARE_LOCATION_ID - Your Square location ID (optional)"
echo ""

# Build and deploy
echo -e "${BLUE}Building and deploying to Vercel...${NC}"
vercel --prod

echo -e "${GREEN}Deployment completed!${NC}"
echo -e "${YELLOW}Don't forget to:${NC}"
echo "1. Set up your MongoDB Atlas database"
echo "2. Configure environment variables in Vercel dashboard"
echo "3. Test the application"
