#!/bin/bash

# Colors for better readability
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the EC2 public IP address
PUBLIC_IP=$(curl -s http://checkip.amazonaws.com)
PORT=3000

# Function to check Supabase configuration
check_supabase_config() {
  if [ ! -f .env.local ]; then
    echo -e "${RED}Error: .env.local file not found. Please create it with your Supabase credentials.${NC}" >&2
    return 1
  fi

  if ! grep -q "NEXT_PUBLIC_SUPABASE_URL" .env.local || ! grep -q "NEXT_PUBLIC_SUPABASE_ANON_KEY" .env.local; then
    echo -e "${RED}Error: Supabase configuration missing in .env.local. Please add NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY.${NC}" >&2
    return 1
  fi

  echo -e "${GREEN}Supabase configuration found.${NC}" >&2
  return 0
}

# Function to display usage information
show_usage() {
  echo -e "${BLUE}Restaurant Inventory Management System${NC}"
  echo -e "${YELLOW}Usage:${NC} $0 [command]"
  echo ""
  echo "Commands:"
  echo "  start       - Start the application with PM2"
  echo "  stop        - Stop the application"
  echo "  restart     - Restart the application"
  echo "  status      - Show application status"
  echo "  logs        - Show application logs"
  echo "  rebuild     - Rebuild and restart the application"
  echo "  dev         - Start the application in development mode"
  echo "  deploy      - Deploy the application to EC2 (app-nextjs)"
  echo "  help        - Show this help message"
  echo ""
}

# Function to start the application
start_app() {
  echo -e "${BLUE}Starting Restaurant Inventory Management System...${NC}"

  # Check if PM2 is already running the app
  if pm2 list | grep -q "restaurant-inventory"; then
    echo -e "${YELLOW}Application is already running. Use 'restart' to restart it.${NC}"
    pm2 list
    return
  fi

  # Check Supabase configuration
  if ! check_supabase_config; then
    echo -e "${RED}Please configure Supabase credentials before starting the application.${NC}"
    return 1
  fi

  # Update ecosystem.config.js
  echo "Updating ecosystem.config.js..."
  cat > ecosystem.config.js << EOL
module.exports = {
  apps: [
    {
      name: 'restaurant-inventory',
      script: 'node_modules/.bin/serve',
      args: 'out -p 3000',
      cwd: __dirname,
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        HOST: '0.0.0.0'  // Bind to all network interfaces
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      log_date_format: 'YYYY-MM-DD HH:mm:ss',
      error_file: 'logs/error.log',
      out_file: 'logs/output.log',
      merge_logs: true
    }
  ]
};
EOL

  # Create logs directory if it doesn't exist
  mkdir -p logs

  # Start the application with PM2
  echo "Starting application with PM2..."
  pm2 start ecosystem.config.js

  # Save the PM2 process list
  echo "Saving PM2 process list..."
  pm2 save

  echo -e "${GREEN}Application started successfully!${NC}"
  echo -e "Access URL: ${BLUE}http://$PUBLIC_IP:$PORT${NC}"
  pm2 list
}

# Function to stop the application
stop_app() {
  echo -e "${BLUE}Stopping Restaurant Inventory Management System...${NC}"

  if pm2 list | grep -q "restaurant-inventory"; then
    pm2 stop restaurant-inventory
    echo -e "${GREEN}Application stopped successfully!${NC}"
  else
    echo -e "${YELLOW}Application is not running.${NC}"
  fi
}

# Function to restart the application
restart_app() {
  echo -e "${BLUE}Restarting Restaurant Inventory Management System...${NC}"

  if pm2 list | grep -q "restaurant-inventory"; then
    pm2 restart restaurant-inventory
    echo -e "${GREEN}Application restarted successfully!${NC}"
    echo -e "Access URL: ${BLUE}http://$PUBLIC_IP:$PORT${NC}"
  else
    echo -e "${YELLOW}Application is not running. Starting it...${NC}"
    start_app
  fi
}

# Function to show application status
show_status() {
  echo -e "${BLUE}Restaurant Inventory Management System Status${NC}"
  echo -e "========================================================"

  if pm2 list | grep -q "restaurant-inventory"; then
    pm2 list
    echo -e "Access URL: ${BLUE}http://$PUBLIC_IP:$PORT${NC}"
  else
    echo -e "${YELLOW}Application is not running.${NC}"
  fi
}

# Function to show application logs
show_logs() {
  echo -e "${BLUE}Restaurant Inventory Management System Logs${NC}"
  echo -e "========================================================"

  if pm2 list | grep -q "restaurant-inventory"; then
    echo -e "${YELLOW}Showing last 50 lines of logs. Press Ctrl+C to exit.${NC}"
    pm2 logs restaurant-inventory --lines 50
  else
    echo -e "${YELLOW}Application is not running.${NC}"
  fi
}

# Function to rebuild and restart the application
rebuild_app() {
  echo -e "${BLUE}Rebuilding Restaurant Inventory Management System...${NC}"

  # Stop the application if it's running
  if pm2 list | grep -q "restaurant-inventory"; then
    echo "Stopping application..."
    pm2 stop restaurant-inventory
    pm2 delete restaurant-inventory
  fi

  # Clean previous build artifacts
  echo "Cleaning previous build..."
  rm -rf .next

  # Install dependencies
  echo "Installing dependencies..."
  npm install

  # Get the EC2 public IP address
  PUBLIC_IP=$(curl -s http://checkip.amazonaws.com)

  # Create .env.local file
  echo "Creating .env.local file..."
  cat > .env.local << EOL
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_API_URL=http://${PUBLIC_IP}:3000
EOL
  echo "Please update .env.local with your actual Supabase credentials."

  # Update README.md with the current EC2 public IP
  echo "Updating README.md with current EC2 public IP..."
  if [ -f README.md ]; then
    # Create a temporary file
    TMP_FILE=$(mktemp)

    # Update the README.md file with the current EC2 public IP
    sed -E "s|The application will be accessible at the server's IP address on port 3000.|The application is accessible at http://${PUBLIC_IP}:3000. The login page is at http://${PUBLIC_IP}:3000/auth/login.|g" README.md > "$TMP_FILE"

    # Replace the original file
    mv "$TMP_FILE" README.md

    echo "README.md updated with current EC2 public IP: ${PUBLIC_IP}"
  else
    echo "README.md not found. Skipping update."
  fi

  # Build the Next.js application
  echo "Building Next.js application..."
  NODE_ENV=production npm run build

  # Create the out directory if it doesn't exist
  mkdir -p out

  # Copy the public directory to the out directory
  cp -r public/* out/ 2>/dev/null || true

  # Start the application
  start_app
}

# Function to deploy the application to EC2
deploy_to_ec2() {
  echo -e "${BLUE}Deploying Restaurant Inventory Management System to EC2...${NC}"

  # Create a temporary directory for deployment files
  DEPLOY_DIR=$(mktemp -d)
  echo "Created temporary directory for deployment: $DEPLOY_DIR"

  # Create necessary directories
  mkdir -p "$DEPLOY_DIR/public"
  mkdir -p "$DEPLOY_DIR/src"
  mkdir -p "$DEPLOY_DIR/scripts"

  # Copy the source files
  echo "Copying source files..."
  cp -r src/* "$DEPLOY_DIR/src/" 2>/dev/null || true
  cp -r public/* "$DEPLOY_DIR/public/" 2>/dev/null || true
  cp package.json package-lock.json next.config.js tsconfig.json "$DEPLOY_DIR/"
  cp -r scripts/* "$DEPLOY_DIR/scripts/" 2>/dev/null || true

  # Create .env.production file for EC2
  echo "Creating .env.production file for EC2..."
  cat > "$DEPLOY_DIR/.env.production" << EOL
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NODE_ENV=production
EOL

  # Create a deployment script to run on EC2
  echo "Creating deployment script..."
  cat > "$DEPLOY_DIR/deploy.sh" << 'EOL'
#!/bin/bash

# Stop the application if it's running
if pm2 list | grep -q "restaurant-inventory"; then
  echo "Stopping application..."
  pm2 stop restaurant-inventory
  pm2 delete restaurant-inventory
fi

# Install dependencies
echo "Installing dependencies..."
npm ci

# Copy .env.production to .env.local
cp .env.production .env.local

# Create logs directory if it doesn't exist
mkdir -p logs

# Build the application on the server
echo "Building the application on the server..."
NODE_OPTIONS=--max_old_space_size=4096 NODE_ENV=production npm run build

# Verify the build was successful
if [ ! -f ".next/BUILD_ID" ]; then
  echo "Build failed - BUILD_ID not found"
  exit 1
fi

# Verify that static files were copied correctly
if [ ! -d ".next/standalone/public/_next/static" ]; then
  echo "Static files were not copied correctly"

  # Create the post-build script if it doesn't exist
  if [ ! -f "scripts/post-build.js" ]; then
    echo "Creating post-build script..."
    mkdir -p scripts
    cat > scripts/post-build.js << 'EOF'
const fs = require('fs');
const path = require('path');

// Define paths
const staticDir = path.join(process.cwd(), '.next/static');
const publicDir = path.join(process.cwd(), 'public');
const standaloneDir = path.join(process.cwd(), '.next/standalone');
const standalonePublicDir = path.join(standaloneDir, 'public');
const standaloneStaticDir = path.join(standalonePublicDir, '_next/static');

// Create directories if they don't exist
if (!fs.existsSync(standalonePublicDir)) {
  fs.mkdirSync(standalonePublicDir, { recursive: true });
}

if (!fs.existsSync(standaloneStaticDir)) {
  fs.mkdirSync(standaloneStaticDir, { recursive: true });
}

// Copy static files to standalone/public/_next/static
console.log('Copying static files to standalone directory...');
copyDirectory(staticDir, standaloneStaticDir);

// Copy public files to standalone/public
console.log('Copying public files to standalone directory...');
copyDirectory(publicDir, standalonePublicDir);

console.log('Post-build process completed successfully!');

// Helper function to copy a directory recursively
function copyDirectory(source, destination) {
  if (!fs.existsSync(source)) {
    console.warn(`Source directory does not exist: ${source}`);
    return;
  }

  if (!fs.existsSync(destination)) {
    fs.mkdirSync(destination, { recursive: true });
  }

  const files = fs.readdirSync(source);

  for (const file of files) {
    const sourcePath = path.join(source, file);
    const destPath = path.join(destination, file);

    const stats = fs.statSync(sourcePath);

    if (stats.isDirectory()) {
      copyDirectory(sourcePath, destPath);
    } else {
      fs.copyFileSync(sourcePath, destPath);
    }
  }
}
EOF
  fi

  # Run the post-build script
  echo "Running post-build script..."
  node scripts/post-build.js
fi

# Create ecosystem.config.js
echo "Creating ecosystem.config.js..."
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [
    {
      name: 'restaurant-inventory',
      script: '.next/standalone/server.js',
      cwd: __dirname,
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        HOST: '0.0.0.0'
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      log_date_format: 'YYYY-MM-DD HH:mm:ss',
      error_file: 'logs/error.log',
      out_file: 'logs/output.log',
      merge_logs: true,
      node_args: '--max_old_space_size=4096'
    }
  ]
};
EOF

# Start the application with PM2
echo "Starting application with PM2..."
pm2 start ecosystem.config.js

# Save the PM2 process list
echo "Saving PM2 process list..."
pm2 save

echo "Deployment completed successfully!"
EOL

  # Make the deployment script executable
  chmod +x "$DEPLOY_DIR/deploy.sh"

  # Create a tar archive of the deployment files
  echo "Creating deployment archive..."
  ARCHIVE_NAME="restaurant-inventory-deploy.tar.gz"
  tar -czf "$ARCHIVE_NAME" -C "$DEPLOY_DIR" .

  # Upload the archive to EC2
  echo "Uploading deployment archive to EC2..."
  scp "$ARCHIVE_NAME" app-nextjs:~/

  # Extract and deploy on EC2
  echo "Extracting and deploying on EC2..."
  ssh app-nextjs << 'ENDSSH'
mkdir -p ~/restaurant-inventory
tar -xzf restaurant-inventory-deploy.tar.gz -C ~/restaurant-inventory
cd ~/restaurant-inventory
bash deploy.sh
rm ~/restaurant-inventory-deploy.tar.gz
ENDSSH

  # Clean up
  echo "Cleaning up temporary files..."
  rm -rf "$DEPLOY_DIR"
  rm "$ARCHIVE_NAME"

  echo -e "${GREEN}Deployment to EC2 completed successfully!${NC}"
  echo -e "The application should now be running on your EC2 instance."
  echo -e "You can access it at: ${BLUE}http://$(ssh app-nextjs 'curl -s http://checkip.amazonaws.com'):3000${NC}"
}

# Function to start the application in development mode
start_dev() {
  echo -e "${BLUE}Starting Restaurant Inventory Management System in development mode...${NC}"

  # Stop the application if it's running
  if pm2 list | grep -q "restaurant-inventory"; then
    echo "Stopping application..."
    pm2 stop restaurant-inventory
    pm2 delete restaurant-inventory
  fi

  # Create logs directory if it doesn't exist
  mkdir -p logs

  # Get the EC2 public IP address
  PUBLIC_IP=$(curl -s http://checkip.amazonaws.com)

  # Create .env.local file
  echo "Creating .env.local file..."
  cat > .env.local << EOL
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_API_URL=http://${PUBLIC_IP}:3000
EOL
  echo "Please update .env.local with your actual Supabase credentials."

  # Update ecosystem.config.js for development mode
  echo "Updating ecosystem.config.js for development mode..."
  cat > ecosystem.config.js << EOL
module.exports = {
  apps: [
    {
      name: 'restaurant-inventory',
      script: 'node_modules/next/dist/bin/next',
      args: 'dev',
      cwd: __dirname,
      env: {
        NODE_ENV: 'development',
        PORT: 3000,
        HOST: '0.0.0.0'  // Bind to all network interfaces
      },
      instances: 1,
      autorestart: true,
      watch: true,
      max_memory_restart: '1G',
      log_date_format: 'YYYY-MM-DD HH:mm:ss',
      error_file: 'logs/error.log',
      out_file: 'logs/output.log',
      merge_logs: true
    }
  ]
};
EOL

  # Start the application with PM2
  echo "Starting application with PM2 in development mode..."
  pm2 start ecosystem.config.js

  # Save the PM2 process list
  echo "Saving PM2 process list..."
  pm2 save

  echo -e "${GREEN}Application started in development mode!${NC}"
  echo -e "Access URL: ${BLUE}http://$PUBLIC_IP:$PORT${NC}"
  pm2 list
}

# Main script logic
case "$1" in
  start)
    start_app
    ;;
  stop)
    stop_app
    ;;
  restart)
    restart_app
    ;;
  status)
    show_status
    ;;
  logs)
    show_logs
    ;;
  rebuild)
    rebuild_app
    ;;
  dev)
    start_dev
    ;;
  deploy)
    deploy_to_ec2
    ;;
  help|--help|-h)
    show_usage
    ;;
  *)
    show_usage
    ;;
esac
