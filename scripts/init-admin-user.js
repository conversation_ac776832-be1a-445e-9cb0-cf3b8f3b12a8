const { MongoClient } = require('mongodb');
const { createHash } = require('crypto');

// Connection URL
const url = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const client = new MongoClient(url);

// Database Name
const dbName = process.env.MONGODB_DB_NAME || 'restaurantInventory';

// Hash a password
const hashPassword = (password) => {
  return createHash('sha256').update(password).digest('hex');
};

async function initAdminUser() {
  try {
    // Connect to the MongoDB server
    await client.connect();
    console.log('Connected successfully to MongoDB server');
    
    // Get the database
    const db = client.db(dbName);
    
    // Get the users collection
    const usersCollection = db.collection('users');
    
    // Check if admin user already exists
    const existingAdmin = await usersCollection.findOne({ 
      username: 'admin'
    });
    
    if (existingAdmin) {
      console.log('Admin user already exists');
      return;
    }
    
    // Create admin user
    const adminUser = {
      id: new Date().getTime().toString(),
      username: 'admin',
      email: '<EMAIL>',
      passwordHash: hashPassword('admin123'),
      role: 'admin',
      firstName: 'Admin',
      lastName: 'User',
      createdAt: new Date(),
      isActive: true
    };
    
    await usersCollection.insertOne(adminUser);
    console.log('Admin user created successfully');
    
    console.log('Admin credentials:');
    console.log('Username: admin');
    console.log('Password: admin123');
    console.log('IMPORTANT: Change this password after first login!');
    
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    // Close the connection
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the function
initAdminUser();
