// MongoDB migration script to rename 'meals' collection to 'items'
db = db.getSiblingDB('restaurantInventory');

// Check if meals collection exists
if (db.getCollectionNames().includes('meals')) {
  // Check if items collection already exists
  if (db.getCollectionNames().includes('items')) {
    print('Both meals and items collections exist. Merging data...');
    
    // Get all meals
    const meals = db.meals.find().toArray();
    
    // Insert meals into items collection if they don't already exist
    let inserted = 0;
    let skipped = 0;
    
    for (const meal of meals) {
      // Check if item with same ID already exists
      const existingItem = db.items.findOne({ id: meal.id });
      
      if (!existingItem) {
        // Insert meal into items collection
        db.items.insertOne(meal);
        inserted++;
      } else {
        skipped++;
      }
    }
    
    print(`Merged ${inserted} meals into items collection. Skipped ${skipped} duplicates.`);
  } else {
    // Rename collection
    print('Renaming meals collection to items...');
    db.meals.renameCollection('items');
    print('Collection renamed successfully.');
  }
} else {
  print('Meals collection does not exist. No migration needed.');
}

// Create indexes for better performance
print('Creating indexes on items collection...');
db.items.createIndex({ name: 1 });
db.items.createIndex({ category: 1 });
db.items.createIndex({ squareItemId: 1 });

print('Migration completed.');
