#!/usr/bin/env node

/**
 * Quick Supabase setup script for Vercel deployment
 * 
 * This script helps you set up your Supabase database and create an initial admin user
 * 
 * Usage:
 * node scripts/setup-supabase.js
 */

const { createClient } = require('@supabase/supabase-js');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function main() {
  console.log('🚀 Supabase Setup for Vietnamese Restaurant Inventory\n');
  
  // Get Supabase credentials
  const supabaseUrl = await question('Enter your Supabase Project URL: ');
  const supabaseServiceKey = await question('Enter your Supabase Service Role Key: ');
  
  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase credentials');
    process.exit(1);
  }
  
  const supabase = createClient(supabaseUrl, supabaseServiceKey);
  
  console.log('\n✅ Connected to Supabase');
  
  // Test connection
  try {
    const { data, error } = await supabase.from('users').select('count').limit(1);
    if (error && !error.message.includes('relation "users" does not exist')) {
      throw error;
    }
    console.log('✅ Database connection successful');
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.log('\n📋 Please run the database migrations first:');
    console.log('1. Go to your Supabase dashboard → SQL Editor');
    console.log('2. Run the contents of supabase/migrations/001_create_restaurant_schema.sql');
    console.log('3. Run the contents of supabase/migrations/002_create_rls_policies.sql');
    process.exit(1);
  }
  
  // Create admin user
  console.log('\n👤 Creating admin user...');
  
  const adminEmail = await question('Enter admin email: ');
  const adminPassword = await question('Enter admin password: ');
  const adminUsername = await question('Enter admin username (default: admin): ') || 'admin';
  
  try {
    // Create auth user
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: adminEmail,
      password: adminPassword,
      email_confirm: true
    });
    
    if (authError) {
      throw authError;
    }
    
    console.log('✅ Auth user created');
    
    // Create user profile
    const { data: profileData, error: profileError } = await supabase
      .from('users')
      .insert({
        auth_id: authData.user.id,
        username: adminUsername,
        email: adminEmail,
        first_name: 'Admin',
        last_name: 'User',
        role: 'admin'
      })
      .select()
      .single();
    
    if (profileError) {
      throw profileError;
    }
    
    console.log('✅ User profile created');
    console.log(`✅ Admin user created successfully!`);
    console.log(`   Email: ${adminEmail}`);
    console.log(`   Username: ${adminUsername}`);
    console.log(`   Role: admin`);
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
  }
  
  // Add some sample data
  console.log('\n📦 Adding sample data...');
  
  try {
    // Add sample storage locations
    const { error: storageError } = await supabase
      .from('storage')
      .insert([
        { name: 'Refrigerator', description: 'Cold storage for perishables' },
        { name: 'Freezer', description: 'Frozen storage' },
        { name: 'Pantry', description: 'Dry goods storage' },
        { name: 'Counter', description: 'Room temperature items' }
      ]);
    
    if (storageError) throw storageError;
    console.log('✅ Sample storage locations added');
    
    // Add sample suppliers
    const { error: supplierError } = await supabase
      .from('suppliers')
      .insert([
        { 
          name: 'Fresh Produce Co', 
          contact_person: 'John Smith',
          email: '<EMAIL>',
          phone: '(*************'
        },
        { 
          name: 'Asian Grocery Wholesale', 
          contact_person: 'Lisa Chen',
          email: '<EMAIL>',
          phone: '(*************'
        }
      ]);
    
    if (supplierError) throw supplierError;
    console.log('✅ Sample suppliers added');
    
    // Add sample inventory items
    const { error: inventoryError } = await supabase
      .from('inventory_items')
      .insert([
        {
          name: 'Rice Noodles',
          vietnamese_name: 'Bánh Phở',
          quantity: 50,
          unit: 'kg',
          min_threshold: 10,
          cost_per_unit: 3.50,
          category: 'Noodles',
          storage: 'Pantry'
        },
        {
          name: 'Fish Sauce',
          vietnamese_name: 'Nước Mắm',
          quantity: 20,
          unit: 'bottles',
          min_threshold: 5,
          cost_per_unit: 8.99,
          category: 'Condiments',
          storage: 'Pantry'
        },
        {
          name: 'Beef Brisket',
          vietnamese_name: 'Thịt Bò',
          quantity: 15,
          unit: 'kg',
          min_threshold: 3,
          cost_per_unit: 25.00,
          category: 'Meat',
          storage: 'Refrigerator'
        }
      ]);
    
    if (inventoryError) throw inventoryError;
    console.log('✅ Sample inventory items added');
    
  } catch (error) {
    console.error('⚠️  Error adding sample data:', error.message);
  }
  
  console.log('\n🎉 Supabase setup completed successfully!');
  console.log('\n📋 Next steps:');
  console.log('1. Update your Vercel environment variables:');
  console.log(`   NEXT_PUBLIC_SUPABASE_URL=${supabaseUrl}`);
  console.log(`   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key`);
  console.log(`   SUPABASE_SERVICE_ROLE_KEY=${supabaseServiceKey}`);
  console.log('2. Deploy your application to Vercel');
  console.log('3. Test the login with your admin credentials');
  console.log('4. Start using your new Supabase-powered app!');
  
  rl.close();
}

main().catch(console.error);
