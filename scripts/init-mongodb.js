// MongoDB initialization script
db = db.getSiblingDB('restaurantInventory');

// Create admin user if it doesn't exist
const adminUser = db.users.findOne({ username: 'admin' });
if (!adminUser) {
  db.users.insertOne({
    id: new ObjectId().toString(),
    username: 'admin',
    email: '<EMAIL>',
    // Password: admin123 (hashed)
    password: '$2b$10$3euPcmQFCiblsZeEu5s7p.9MQICWbpMX2gX.M8P0PeP3TrRHZ.6Aq',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin',
    isActive: true,
    createdAt: new Date(),
    lastLogin: null
  });
  print('Admin user created');
}

// Create collections if they don't exist
const collections = [
  'inventory_items',
  'transactions',
  'items',
  'productions',
  'alerts',
  'config',
  'storage'
];

collections.forEach(collection => {
  if (!db.getCollectionNames().includes(collection)) {
    db.createCollection(collection);
    print(`Collection ${collection} created`);
  }
});

// Create indexes for better performance
db.inventory_items.createIndex({ name: 1 });
db.inventory_items.createIndex({ category: 1 });
db.transactions.createIndex({ inventoryItemId: 1 });
db.transactions.createIndex({ date: -1 });
db.items.createIndex({ name: 1 });
db.items.createIndex({ category: 1 });
db.productions.createIndex({ date: -1 });
db.alerts.createIndex({ isRead: 1 });
db.storage.createIndex({ name: 1 });

print('MongoDB initialization completed');
