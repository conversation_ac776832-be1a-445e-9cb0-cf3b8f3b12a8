#!/bin/bash

# Get MongoDB URI from .env.local file
MONGODB_URI=$(grep MONGODB_URI .env.local | cut -d '=' -f2)
MONGODB_DB_NAME=$(grep MONGODB_DB_NAME .env.local | cut -d '=' -f2)

# If not found, use default values
if [ -z "$MONGODB_URI" ]; then
  MONGODB_URI="mongodb://localhost:27017"
fi

if [ -z "$MONGODB_DB_NAME" ]; then
  MONGODB_DB_NAME="restaurantInventory"
fi

echo "Running migration script to rename 'meals' collection to 'items'..."
echo "Using MongoDB URI: $MONGODB_URI"
echo "Using database: $MONGODB_DB_NAME"

# Run the migration script
mongosh "$MONGODB_URI/$MONGODB_DB_NAME" scripts/migrate-meals-to-items.js

echo "Migration completed."
