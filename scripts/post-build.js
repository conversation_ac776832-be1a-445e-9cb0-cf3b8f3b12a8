const fs = require('fs');
const path = require('path');

// Define paths
const staticDir = path.join(process.cwd(), '.next/static');
const publicDir = path.join(process.cwd(), 'public');
const standaloneDir = path.join(process.cwd(), '.next/standalone');
const standalonePublicDir = path.join(standaloneDir, 'public');
const standaloneStaticDir = path.join(standalonePublicDir, '_next/static');

// Create directories if they don't exist
if (!fs.existsSync(standalonePublicDir)) {
  fs.mkdirSync(standalonePublicDir, { recursive: true });
}

if (!fs.existsSync(standaloneStaticDir)) {
  fs.mkdirSync(standaloneStaticDir, { recursive: true });
}

// Copy static files to standalone/public/_next/static
console.log('Copying static files to standalone directory...');
copyDirectory(staticDir, standaloneStaticDir);

// Copy public files to standalone/public
console.log('Copying public files to standalone directory...');
copyDirectory(publicDir, standalonePublicDir);

console.log('Post-build process completed successfully!');

// Helper function to copy a directory recursively
function copyDirectory(source, destination) {
  if (!fs.existsSync(source)) {
    console.warn(`Source directory does not exist: ${source}`);
    return;
  }

  if (!fs.existsSync(destination)) {
    fs.mkdirSync(destination, { recursive: true });
  }

  const files = fs.readdirSync(source);

  for (const file of files) {
    const sourcePath = path.join(source, file);
    const destPath = path.join(destination, file);

    const stats = fs.statSync(sourcePath);

    if (stats.isDirectory()) {
      copyDirectory(sourcePath, destPath);
    } else {
      fs.copyFileSync(sourcePath, destPath);
    }
  }
}
