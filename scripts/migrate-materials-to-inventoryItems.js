// MongoDB migration script to rename 'materials' collection to 'inventoryItems'
db = db.getSiblingDB('restaurantInventory');

// Check if materials collection exists
if (db.getCollectionNames().includes('materials')) {
  // Check if inventoryItems collection already exists
  if (db.getCollectionNames().includes('inventoryItems')) {
    print('Both materials and inventoryItems collections exist. Merging data...');
    
    // Get all materials
    const materials = db.materials.find().toArray();
    
    // Insert materials into inventoryItems collection if they don't already exist
    let inserted = 0;
    let skipped = 0;
    
    for (const material of materials) {
      // Check if item with same ID already exists
      const existingItem = db.inventoryItems.findOne({ id: material.id });
      
      if (!existingItem) {
        // Insert material into inventoryItems collection
        db.inventoryItems.insertOne(material);
        inserted++;
      } else {
        skipped++;
      }
    }
    
    print(`Merged ${inserted} materials into inventoryItems collection. Skipped ${skipped} duplicates.`);
    
    // Drop the materials collection after successful migration
    db.materials.drop();
    print('Dropped materials collection after successful migration.');
  } else {
    // Rename collection
    print('Renaming materials collection to inventoryItems...');
    db.materials.renameCollection('inventoryItems');
    print('Collection renamed successfully.');
  }
} else {
  print('Materials collection does not exist. No migration needed.');
}

// Create indexes for better performance
print('Creating indexes on inventoryItems collection...');
db.inventoryItems.createIndex({ name: 1 });
db.inventoryItems.createIndex({ category: 1 });
db.inventoryItems.createIndex({ storage: 1 });

print('Migration completed.');
