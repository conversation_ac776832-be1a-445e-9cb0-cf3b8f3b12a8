#!/usr/bin/env node

/**
 * Migration script from MongoDB to Supabase
 * 
 * This script helps migrate data from your existing MongoDB database
 * to the new Supabase PostgreSQL database.
 * 
 * Usage:
 * node scripts/migrate-to-supabase.js
 */

const { MongoClient } = require('mongodb');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const MONGODB_DB_NAME = process.env.MONGODB_DB_NAME || 'restaurant-inventory';
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing Supabase configuration. Please check your .env.local file.');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Helper function to convert MongoDB ObjectId to UUID-like string
const convertId = (mongoId) => {
  if (typeof mongoId === 'string') return mongoId;
  return mongoId.toString();
};

// Convert MongoDB user to Supabase format
const convertUser = (mongoUser) => ({
  id: convertId(mongoUser.id || mongoUser._id),
  username: mongoUser.username,
  email: mongoUser.email,
  first_name: mongoUser.firstName || null,
  last_name: mongoUser.lastName || null,
  role: mongoUser.role || 'staff',
  is_active: mongoUser.isActive !== false,
  created_at: mongoUser.createdAt || new Date().toISOString(),
  last_login: mongoUser.lastLogin || null
});

// Convert MongoDB inventory item to Supabase format
const convertInventoryItem = (mongoItem) => ({
  id: convertId(mongoItem.id || mongoItem._id),
  name: mongoItem.name,
  vietnamese_name: mongoItem.vietnameseName || null,
  quantity: parseFloat(mongoItem.quantity) || 0,
  unit: mongoItem.unit,
  min_threshold: mongoItem.minThreshold ? parseFloat(mongoItem.minThreshold) : null,
  cost_per_unit: parseFloat(mongoItem.costPerUnit) || 0,
  category: mongoItem.category || null,
  storage: mongoItem.storage || null,
  supplier_id: mongoItem.supplierId ? convertId(mongoItem.supplierId) : null,
  last_restock_date: mongoItem.lastRestockDate || null,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
});

// Convert MongoDB item to Supabase format
const convertItem = (mongoItem) => ({
  id: convertId(mongoItem.id || mongoItem._id),
  name: mongoItem.name,
  vietnamese_name: mongoItem.vietnameseName || null,
  category: mongoItem.category || null,
  description: mongoItem.description || null,
  preparation_time: mongoItem.preparationTime || null,
  image_url: mongoItem.image || null,
  square_item_id: mongoItem.squareItemId || null,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
});

async function migrateCollection(mongoCollection, supabaseTable, converter, batchSize = 100) {
  console.log(`📦 Migrating ${mongoCollection} to ${supabaseTable}...`);
  
  try {
    // Connect to MongoDB
    const client = new MongoClient(MONGODB_URI);
    await client.connect();
    const db = client.db(MONGODB_DB_NAME);
    
    // Get all documents from MongoDB
    const documents = await db.collection(mongoCollection).find({}).toArray();
    console.log(`   Found ${documents.length} documents in ${mongoCollection}`);
    
    if (documents.length === 0) {
      console.log(`   ⚠️  No documents found in ${mongoCollection}, skipping...`);
      await client.close();
      return;
    }
    
    // Convert documents
    const convertedDocs = documents.map(converter);
    
    // Insert in batches to Supabase
    let inserted = 0;
    for (let i = 0; i < convertedDocs.length; i += batchSize) {
      const batch = convertedDocs.slice(i, i + batchSize);
      
      const { data, error } = await supabase
        .from(supabaseTable)
        .insert(batch)
        .select();
      
      if (error) {
        console.error(`   ❌ Error inserting batch ${Math.floor(i/batchSize) + 1}:`, error);
        // Continue with next batch
      } else {
        inserted += data.length;
        console.log(`   ✅ Inserted batch ${Math.floor(i/batchSize) + 1} (${data.length} records)`);
      }
    }
    
    console.log(`   🎉 Successfully migrated ${inserted}/${documents.length} records from ${mongoCollection}`);
    await client.close();
    
  } catch (error) {
    console.error(`   ❌ Error migrating ${mongoCollection}:`, error);
  }
}

async function main() {
  console.log('🚀 Starting migration from MongoDB to Supabase...\n');
  
  // Test Supabase connection
  try {
    const { data, error } = await supabase.from('users').select('count').limit(1);
    if (error) throw error;
    console.log('✅ Supabase connection successful\n');
  } catch (error) {
    console.error('❌ Failed to connect to Supabase:', error);
    process.exit(1);
  }
  
  // Migrate collections in order (respecting foreign key dependencies)
  
  // 1. Users (no dependencies)
  await migrateCollection('users', 'users', convertUser);
  
  // 2. Suppliers (no dependencies)
  // await migrateCollection('suppliers', 'suppliers', convertSupplier);
  
  // 3. Inventory Items (depends on suppliers)
  await migrateCollection('inventoryItems', 'inventory_items', convertInventoryItem);
  // Also try alternative collection names
  await migrateCollection('inventory_items', 'inventory_items', convertInventoryItem);
  
  // 4. Items/Menu Items (no dependencies)
  await migrateCollection('items', 'items', convertItem);
  await migrateCollection('menuItems', 'items', convertItem);
  
  // 5. Other collections can be added here...
  
  console.log('\n🎉 Migration completed!');
  console.log('\n📋 Next steps:');
  console.log('1. Verify the data in your Supabase dashboard');
  console.log('2. Update your application to use Supabase auth context');
  console.log('3. Test the application thoroughly');
  console.log('4. Update environment variables for production');
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n⚠️  Migration interrupted by user');
  process.exit(0);
});

process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled rejection:', error);
  process.exit(1);
});

// Run the migration
main().catch(console.error);
