const { MongoClient } = require('mongodb');

// Connection URL
const url = 'mongodb://mongodb:27017';
const client = new MongoClient(url);

// Database Name
const dbName = 'restaurantInventory';

async function importData() {
  try {
    // Connect to the MongoDB server
    await client.connect();
    console.log('Connected successfully to MongoDB server');

    // Get the database
    const db = client.db(dbName);

    // Create collections
    const inventoryItemsCollection = db.collection('inventoryItems');
    const locationsCollection = db.collection('locations');
    const categoriesCollection = db.collection('categories');

    // Clear existing data (optional)
    await inventoryItemsCollection.deleteMany({});
    await locationsCollection.deleteMany({});
    await categoriesCollection.deleteMany({});
    console.log('Cleared existing data');

    // Define storage locations
    const locations = [
      {
        id: 'fridge1',
        name: 'Fridge 1',
        type: 'refrigerator',
        temperatureRange: '2-4°C'
      },
      {
        id: 'freezer1',
        name: 'Freezer 1',
        type: 'freezer',
        temperatureRange: '-18°C'
      },
      {
        id: 'dryStock',
        name: 'Dry Stocks',
        type: 'dry-storage',
        temperatureRange: 'room temperature'
      }
    ];

    // Define categories
    const categories = [
      { id: 'meat', name: 'Meat' },
      { id: 'produce', name: 'Produce' },
      { id: 'dryGoods', name: 'Dry Goods' }
    ];

    // Define inventory items with their locations
    const inventoryItems = [
      {
        id: 'porkChop',
        name: 'Pork chop',
        vietnameseName: 'Sườn heo',
        locationId: 'fridge1',
        categoryId: 'meat',
        unit: 'kg',
        quantity: 5,
        minThreshold: 1,
        costPerUnit: 12.50
      },
      {
        id: 'rice',
        name: 'Rice',
        vietnameseName: 'Gạo',
        locationId: 'dryStock',
        categoryId: 'dryGoods',
        unit: 'kg',
        quantity: 25,
        minThreshold: 5,
        costPerUnit: 2.50
      },
      {
        id: 'cucumber',
        name: 'Cucumber',
        vietnameseName: 'Dưa leo',
        locationId: 'fridge1',
        categoryId: 'produce',
        unit: 'kg',
        quantity: 5,
        minThreshold: 1,
        costPerUnit: 3.00
      }
    ];

    // Insert data into collections
    await locationsCollection.insertMany(locations);
    console.log('Locations imported');
    
    await categoriesCollection.insertMany(categories);
    console.log('Categories imported');
    
    await inventoryItemsCollection.insertMany(inventoryItems);
    console.log('Inventory items imported');

    console.log('Data imported successfully');

    // Create indexes for better query performance
    await inventoryItemsCollection.createIndex({ locationId: 1 });
    await inventoryItemsCollection.createIndex({ categoryId: 1 });
    await inventoryItemsCollection.createIndex({ name: 1 });

    console.log('Indexes created successfully');

  } catch (error) {
    console.error('Error importing data:', error);
  } finally {
    // Close the connection
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the import function
importData();
