/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['var(--font-plus-jakarta-sans)', 'sans-serif'],
        mono: ['var(--font-roboto-mono)', 'monospace'],
      },
      colors: {
        viet: {
          red: '#EA4E3D',
          yellow: '#FFE194', // Lighter yellow for the portal
          yellowDark: '#FDB833',
          yellowDarker: '#F9A826', // Even darker yellow
          cream: '#EDE9D5',
          creamDark: '#E5DFC9', // Darker beige for header
          creamDarker: '#D8D0B8', // Even darker beige
          text: '#402014',
        },
        // Sidebar colors with darker yellow variations
        sidebar: {
          default: '#F5D08A', // Darker yellow for sidebar
          light: '#F9E3B7', // Lighter version
          medium: '#F0C16E', // Medium version
          dark: '#E6B254', // Darker version
          darker: '#D9A43C', // Even darker version
          darkest: '#C08C28', // Darkest version
          // Additional colors from user preferences
          olive: '#797D62',
          sage: '#9B9B7A',
          sand: '#F1DCA7',
          gold: '#FFCB69',
          copper: '#D08C60',
          brown: '#997B66',
        },
      },
    },
  },
  plugins: [],
}
