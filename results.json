{"message": "Inventory API test completed", "results": {"directSupabase": {"status": 200, "count": 0, "data": [], "headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "9450b8e56cf45a40-IAD", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/inventory_items?limit=20&select=%2A", "content-profile": "public", "content-range": "*/*", "content-type": "application/json; charset=utf-8", "date": "Sat, 24 May 2025 23:55:09 GMT", "sb-gateway-version": "1", "sb-project-ref": "ayzppqpygicvbptdqrnt", "server": "cloudflare", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}}, "backendAPI": {"status": 200, "count": 0, "data": {"data": [], "pagination": {"page": 1, "limit": 20, "total": 0, "totalPages": 0, "hasMore": false, "hasPrevious": false}}, "headers": {"access-control-allow-headers": "Content-Type, Authorization", "access-control-allow-methods": "GET, POST, PUT, DELETE, OPTIONS", "access-control-allow-origin": "*", "age": "0", "cache-control": "public, max-age=0, must-revalidate", "content-length": "107", "content-security-policy": "default-src 'self'; connect-src 'self' https://ayzppqpygicvbptdqrnt.supabase.co https://*.supabase.co https://connect.squareup.com https://*.squareup.com; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; frame-src 'self';", "content-type": "application/json; charset=utf-8", "date": "Sat, 24 May 2025 23:55:11 GMT", "etag": "\"m2ff1if80k2z\"", "referrer-policy": "strict-origin-when-cross-origin", "server": "Vercel", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "x-content-type-options": "nosniff", "x-frame-options": "DENY", "x-matched-path": "/api/inventory/items", "x-vercel-cache": "MISS", "x-vercel-id": "syd1:iad1::iad1::hqwz6-1748130909723-38b0ac8d9a0c"}}, "comparison": {"directSupabaseCount": 0, "backendAPICount": 0, "serviceRoleKeyCount": 14, "issue": "Unknown issue"}, "serviceRoleKey": {"status": 200, "count": 14, "data": [{"id": "62a0783c-ade8-4b2d-afba-661eb291c948", "name": "123", "vietnamese_name": "123", "quantity": 123, "unit": "cm", "min_threshold": 22, "cost_per_unit": 2, "category": "Beverages", "storage": "freezer", "supplier_id": null, "last_restock_date": null, "created_at": "2025-05-23T12:57:03.279+00:00", "updated_at": "2025-05-23T12:57:03.279+00:00"}, {"id": "00cddb23-c088-4e67-a848-553eeca4f428", "name": "test", "vietnamese_name": "test", "quantity": 1, "unit": "1", "min_threshold": 0, "cost_per_unit": 1, "category": "Cleaning", "storage": "freezer", "supplier_id": null, "last_restock_date": null, "created_at": "2025-05-24T08:32:39.290019+00:00", "updated_at": "2025-05-24T08:32:39.290019+00:00"}, {"id": "f85eff7d-296a-4e8a-9ac1-16a829da35c2", "name": "Rice", "vietnamese_name": "Gạo", "quantity": 50, "unit": "kg", "min_threshold": 10, "cost_per_unit": 2.5, "category": "Grains", "storage": "Dry Storage", "supplier_id": null, "last_restock_date": null, "created_at": "2025-05-24T09:10:11.784+00:00", "updated_at": "2025-05-24T09:10:11.785+00:00"}, {"id": "9522e7f8-be09-4a21-b725-7d70300d3c6c", "name": "Chicken Breast", "vietnamese_name": "Ức gà", "quantity": 25, "unit": "kg", "min_threshold": 5, "cost_per_unit": 8.99, "category": "Meat", "storage": "Refrigerator", "supplier_id": null, "last_restock_date": null, "created_at": "2025-05-24T09:10:12.731+00:00", "updated_at": "2025-05-24T09:10:12.731+00:00"}, {"id": "5daf516d-d491-4660-b72c-1a62942f1a0d", "name": "Fish Sauce", "vietnamese_name": "<PERSON><PERSON><PERSON><PERSON>", "quantity": 12, "unit": "bottle", "min_threshold": 3, "cost_per_unit": 3.5, "category": "Condiments", "storage": "Pantry", "supplier_id": null, "last_restock_date": null, "created_at": "2025-05-24T09:10:13.55+00:00", "updated_at": "2025-05-24T09:10:13.55+00:00"}, {"id": "0b0c63bb-7af1-4c2a-a820-b28892972805", "name": "Rice", "vietnamese_name": "Gạo", "quantity": 50, "unit": "kg", "min_threshold": 10, "cost_per_unit": 2.5, "category": "Grains", "storage": "Dry Storage", "supplier_id": null, "last_restock_date": null, "created_at": "2025-05-24T23:31:41.604+00:00", "updated_at": "2025-05-24T23:31:41.604+00:00"}, {"id": "15bb4b04-7e9b-4e1a-a078-40035149c481", "name": "Chicken Breast", "vietnamese_name": "Ức gà", "quantity": 25, "unit": "kg", "min_threshold": 5, "cost_per_unit": 8.99, "category": "Meat", "storage": "Refrigerator", "supplier_id": null, "last_restock_date": null, "created_at": "2025-05-24T23:31:42.361+00:00", "updated_at": "2025-05-24T23:31:42.361+00:00"}, {"id": "84b2872b-4dfe-4f5f-beb8-fcb8687a660d", "name": "Fish Sauce", "vietnamese_name": "<PERSON><PERSON><PERSON><PERSON>", "quantity": 12, "unit": "bottle", "min_threshold": 3, "cost_per_unit": 3.5, "category": "Condiments", "storage": "Pantry", "supplier_id": null, "last_restock_date": null, "created_at": "2025-05-24T23:31:42.687+00:00", "updated_at": "2025-05-24T23:31:42.687+00:00"}, {"id": "a22ce2dc-3ff2-44b5-9331-af5b275317f9", "name": "Rice", "vietnamese_name": "Gạo", "quantity": 50, "unit": "kg", "min_threshold": 10, "cost_per_unit": 2.5, "category": "Grains", "storage": "Dry Storage", "supplier_id": null, "last_restock_date": null, "created_at": "2025-05-24T23:32:17.446+00:00", "updated_at": "2025-05-24T23:32:17.446+00:00"}, {"id": "718709a8-4bd4-4fea-a5e4-7d587c723736", "name": "Chicken Breast", "vietnamese_name": "Ức gà", "quantity": 25, "unit": "kg", "min_threshold": 5, "cost_per_unit": 8.99, "category": "Meat", "storage": "Refrigerator", "supplier_id": null, "last_restock_date": null, "created_at": "2025-05-24T23:32:18.214+00:00", "updated_at": "2025-05-24T23:32:18.214+00:00"}, {"id": "d79635cb-a1f4-4fac-a1ca-0d08d5fc55ab", "name": "Fish Sauce", "vietnamese_name": "<PERSON><PERSON><PERSON><PERSON>", "quantity": 12, "unit": "bottle", "min_threshold": 3, "cost_per_unit": 3.5, "category": "Condiments", "storage": "Pantry", "supplier_id": null, "last_restock_date": null, "created_at": "2025-05-24T23:32:18.948+00:00", "updated_at": "2025-05-24T23:32:18.948+00:00"}, {"id": "613767dd-e7bb-4583-987a-169dd555f5f1", "name": "Rice", "vietnamese_name": "Gạo", "quantity": 50, "unit": "kg", "min_threshold": 10, "cost_per_unit": 2.5, "category": "Grains", "storage": "Dry Storage", "supplier_id": null, "last_restock_date": null, "created_at": "2025-05-24T23:39:10.173+00:00", "updated_at": "2025-05-24T23:39:10.173+00:00"}, {"id": "b41bddb1-97f5-4946-8c35-187303e5aafc", "name": "Chicken Breast", "vietnamese_name": "Ức gà", "quantity": 25, "unit": "kg", "min_threshold": 5, "cost_per_unit": 8.99, "category": "Meat", "storage": "Refrigerator", "supplier_id": null, "last_restock_date": null, "created_at": "2025-05-24T23:39:10.173+00:00", "updated_at": "2025-05-24T23:39:10.173+00:00"}, {"id": "675ea819-5846-43b2-81c5-cec7b21c20bb", "name": "Fish Sauce", "vietnamese_name": "<PERSON><PERSON><PERSON><PERSON>", "quantity": 12, "unit": "bottle", "min_threshold": 3, "cost_per_unit": 3.5, "category": "Condiments", "storage": "Pantry", "supplier_id": null, "last_restock_date": null, "created_at": "2025-05-24T23:39:10.173+00:00", "updated_at": "2025-05-24T23:39:10.173+00:00"}], "headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "9450b8f33c895a40-IAD", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/inventory_items?limit=20&select=%2A", "content-profile": "public", "content-range": "0-13/*", "content-type": "application/json; charset=utf-8", "date": "Sat, 24 May 2025 23:55:11 GMT", "sb-gateway-version": "1", "sb-project-ref": "ayzppqpygicvbptdqrnt", "server": "cloudflare", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}}}, "environment": {"supabaseUrl": "https://ayzppqpygicvbptdqrnt.supabase.co", "hasAnonKey": true, "hasServiceKey": true, "host": "inventory-management-ivory-nu.vercel.app"}}