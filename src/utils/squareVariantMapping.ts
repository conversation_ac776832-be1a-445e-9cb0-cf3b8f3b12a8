/**
 * Square Variant Mapping Utilities
 *
 * Maps Square order line items to local variants for proper order processing
 */

/**
 * Note: Dine-in/takeaway mapping logic removed.
 * Now using direct Square variation mapping - each Square variation becomes a local variant.
 * This provides exact 1:1 mapping between Square variations and local variants.
 */

export interface SquareOrderLineItem {
  catalog_object_id: string;     // Square Item ID
  catalog_variation_id?: string; // Square Variation ID (for exact matching)
  name: string;                  // Item name
  variation_name: string;        // Square variation name (e.g., "Regular", "Large")
  quantity: string;              // Order quantity as string
  base_price_money: {
    amount: number;              // Price in cents
    currency: string;
  };
}

export interface LocalVariantMapping {
  localItemId: string | null;
  localVariantId: string | null;
  squareItemId: string;
  squareVariationName: string;
  itemName: string;
  quantity: number;
  priceInCents: number;
  matched: boolean;
}

/**
 * Maps Square order line items to local variants
 */
export async function mapSquareOrderToLocalVariants(
  squareLineItems: SquareOrderLineItem[]
): Promise<LocalVariantMapping[]> {
  const mappings: LocalVariantMapping[] = [];

  for (const lineItem of squareLineItems) {
    try {
      // Find local item by Square item ID
      const itemResponse = await fetch(`/api/items?square_item_id=${lineItem.catalog_object_id}`);

      if (!itemResponse.ok) {
        console.warn(`No local item found for Square item ${lineItem.catalog_object_id}`);
        mappings.push({
          localItemId: null,
          localVariantId: null,
          squareItemId: lineItem.catalog_object_id,
          squareVariationName: lineItem.variation_name,
          itemName: lineItem.name,
          quantity: parseInt(lineItem.quantity),
          priceInCents: lineItem.base_price_money.amount,
          matched: false
        });
        continue;
      }

      const items = await itemResponse.json();
      const localItem = items.data?.[0];

      if (!localItem) {
        console.warn(`No local item data for Square item ${lineItem.catalog_object_id}`);
        mappings.push({
          localItemId: null,
          localVariantId: null,
          squareItemId: lineItem.catalog_object_id,
          squareVariationName: lineItem.variation_name,
          itemName: lineItem.name,
          quantity: parseInt(lineItem.quantity),
          priceInCents: lineItem.base_price_money.amount,
          matched: false
        });
        continue;
      }

      // Find matching local variant by Square variation ID or name
      let matchedVariant = null;
      if (localItem.variants && localItem.variants.length > 0) {
        // First try to match by Square variation ID (most accurate)
        if (lineItem.catalog_variation_id) {
          matchedVariant = localItem.variants.find((variant: any) =>
            variant.square_variation_id === lineItem.catalog_variation_id
          );
        }

        // If no ID match, try exact name match
        if (!matchedVariant) {
          matchedVariant = localItem.variants.find((variant: any) =>
            variant.name.toLowerCase() === lineItem.variation_name.toLowerCase()
          );
        }

        // If no exact match, try partial match
        if (!matchedVariant) {
          matchedVariant = localItem.variants.find((variant: any) =>
            variant.name.toLowerCase().includes(lineItem.variation_name.toLowerCase()) ||
            lineItem.variation_name.toLowerCase().includes(variant.name.toLowerCase())
          );
        }

        // If still no match, use the first variant (oldest/default)
        if (!matchedVariant) {
          matchedVariant = localItem.variants[0];
          console.warn(`No variant match for "${lineItem.variation_name}", using default variant "${matchedVariant.name}"`);
        }
      }

      mappings.push({
        localItemId: localItem.id,
        localVariantId: matchedVariant?.id || null,
        squareItemId: lineItem.catalog_object_id,
        squareVariationName: lineItem.variation_name,
        itemName: lineItem.name,
        quantity: parseInt(lineItem.quantity),
        priceInCents: lineItem.base_price_money.amount,
        matched: !!matchedVariant
      });

    } catch (error) {
      console.error(`Error mapping Square item ${lineItem.catalog_object_id}:`, error);
      mappings.push({
        localItemId: null,
        localVariantId: null,
        squareItemId: lineItem.catalog_object_id,
        squareVariationName: lineItem.variation_name,
        itemName: lineItem.name,
        quantity: parseInt(lineItem.quantity),
        priceInCents: lineItem.base_price_money.amount,
        matched: false
      });
    }
  }

  return mappings;
}

/**
 * Validates variant mapping results
 */
export function validateVariantMappings(mappings: LocalVariantMapping[]): {
  totalItems: number;
  matchedItems: number;
  unmatchedItems: number;
  matchRate: number;
  unmatchedDetails: LocalVariantMapping[];
} {
  const totalItems = mappings.length;
  const matchedItems = mappings.filter(m => m.matched).length;
  const unmatchedItems = totalItems - matchedItems;
  const matchRate = totalItems > 0 ? (matchedItems / totalItems) * 100 : 0;
  const unmatchedDetails = mappings.filter(m => !m.matched);

  return {
    totalItems,
    matchedItems,
    unmatchedItems,
    matchRate,
    unmatchedDetails
  };
}

/**
 * Formats mapping results for display
 */
export function formatMappingResults(mappings: LocalVariantMapping[]): string {
  const validation = validateVariantMappings(mappings);

  let result = `Variant Mapping Results:\n`;
  result += `Total Items: ${validation.totalItems}\n`;
  result += `Matched: ${validation.matchedItems} (${validation.matchRate.toFixed(1)}%)\n`;
  result += `Unmatched: ${validation.unmatchedItems}\n\n`;

  if (validation.unmatchedItems > 0) {
    result += `Unmatched Items:\n`;
    validation.unmatchedDetails.forEach(item => {
      result += `- ${item.itemName} (${item.squareVariationName}) - Square ID: ${item.squareItemId}\n`;
    });
  }

  return result;
}
