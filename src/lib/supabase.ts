import { createClient } from '@supabase/supabase-js'

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// Validate required environment variables
if (!supabaseUrl) {
  throw new Error('Missing env.NEXT_PUBLIC_SUPABASE_URL')
}
if (!supabaseAnonKey) {
  throw new Error('Missing env.NEXT_PUBLIC_SUPABASE_ANON_KEY')
}

// Client-side Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Admin client for server-side operations that need elevated permissions
// Only create if service role key is available
export const supabaseAdmin = supabaseServiceRoleKey
  ? createClient(supabaseUrl, supabaseServiceRoleKey)
  : null

// Database types (will be auto-generated later)
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          username: string
          email: string
          first_name: string | null
          last_name: string | null
          role: string
          is_active: boolean
          created_at: string
          last_login: string | null
        }
        Insert: {
          id?: string
          username: string
          email: string
          first_name?: string | null
          last_name?: string | null
          role?: string
          is_active?: boolean
          created_at?: string
          last_login?: string | null
        }
        Update: {
          id?: string
          username?: string
          email?: string
          first_name?: string | null
          last_name?: string | null
          role?: string
          is_active?: boolean
          created_at?: string
          last_login?: string | null
        }
      }
      inventory_items: {
        Row: {
          id: string
          name: string
          vietnamese_name: string | null
          quantity: number
          unit: string
          min_threshold: number | null
          cost_per_unit: number
          category: string | null
          storage: string | null
          supplier_id: string | null
          last_restock_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          vietnamese_name?: string | null
          quantity?: number
          unit: string
          min_threshold?: number | null
          cost_per_unit: number
          category?: string | null
          storage?: string | null
          supplier_id?: string | null
          last_restock_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          vietnamese_name?: string | null
          quantity?: number
          unit?: string
          min_threshold?: number | null
          cost_per_unit?: number
          category?: string | null
          storage?: string | null
          supplier_id?: string | null
          last_restock_date?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      items: {
        Row: {
          id: string
          name: string
          vietnamese_name: string | null
          category: string | null
          description: string | null
          preparation_time: number | null
          image_url: string | null
          square_item_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          vietnamese_name?: string | null
          category?: string | null
          description?: string | null
          preparation_time?: number | null
          image_url?: string | null
          square_item_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          vietnamese_name?: string | null
          category?: string | null
          description?: string | null
          preparation_time?: number | null
          image_url?: string | null
          square_item_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      // Add more table types as needed
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: 'admin' | 'manager' | 'staff' | 'kitchen'
      transaction_type: 'purchase' | 'usage' | 'waste' | 'adjustment'
      variant_type: 'dine_in' | 'takeaway'
      alert_type: 'low_stock' | 'expiration' | 'price_change'
    }
  }
}
