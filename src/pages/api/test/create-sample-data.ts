import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Creating sample data...');

    // Sample inventory items with camelCase field names for backend API
    const sampleInventoryItems = [
      {
        name: 'Rice',
        vietnameseName: 'Gạo',
        category: 'Grains',
        unit: 'kg',
        quantity: 50,
        costPerUnit: 2.50,
        minThreshold: 10,
        storage: 'Dry Storage'
      },
      {
        name: 'Chicken Breast',
        vietnameseName: 'Ức gà',
        category: 'Meat',
        unit: 'kg',
        quantity: 25,
        costPerUnit: 8.99,
        minThreshold: 5,
        storage: 'Refrigerator'
      },
      {
        name: 'Fish Sauce',
        vietnameseName: 'Nước mắm',
        category: 'Condiments',
        unit: 'bottle',
        quantity: 12,
        costPerUnit: 3.50,
        minThreshold: 3,
        storage: 'Pantry'
      }
    ];

    // Sample menu items
    const sampleMenuItems = [
      {
        name: '<PERSON><PERSON> <PERSON>',
        vietnamese_name: 'Phở Bò',
        category: 'main',
        description: 'Traditional Vietnamese beef noodle soup',
        preparation_time: 15,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        name: 'Banh Mi',
        vietnamese_name: 'Bánh Mì',
        category: 'appetizer',
        description: 'Vietnamese sandwich with various fillings',
        preparation_time: 10,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    const results = {
      inventoryItems: [],
      menuItems: [],
      errors: []
    };

    // Create inventory items using backend API (which uses service role key)
    for (const item of sampleInventoryItems) {
      try {
        // Use our backend API which handles authentication properly
        const baseUrl = req.headers.host?.includes('localhost') ? 'http://localhost:3000' : 'https://inventory-management-ivory-nu.vercel.app';
        const response = await fetch(`${baseUrl}/api/inventory/items`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(item),
        });

        if (response.ok) {
          const data = await response.json();
          results.inventoryItems.push(data);
          console.log('✅ Created inventory item:', item.name);
        } else {
          const error = await response.text();
          console.error('❌ Failed to create inventory item:', item.name, error);
          results.errors.push(`Failed to create inventory item ${item.name}: ${error}`);
        }
      } catch (error: any) {
        console.error('❌ Error creating inventory item:', item.name, error);
        results.errors.push(`Error creating inventory item ${item.name}: ${error.message}`);
      }
    }

    // Create menu items
    for (const item of sampleMenuItems) {
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items`, {
          method: 'POST',
          headers: {
            'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
            'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
            'Content-Type': 'application/json',
            'Prefer': 'return=representation',
          },
          body: JSON.stringify(item),
        });

        if (response.ok) {
          const data = await response.json();
          results.menuItems.push(data[0]);
          console.log('✅ Created menu item:', item.name);
        } else {
          const error = await response.text();
          console.error('❌ Failed to create menu item:', item.name, error);
          results.errors.push(`Failed to create menu item ${item.name}: ${error}`);
        }
      } catch (error: any) {
        console.error('❌ Error creating menu item:', item.name, error);
        results.errors.push(`Error creating menu item ${item.name}: ${error.message}`);
      }
    }

    console.log('✅ Sample data creation completed');
    res.status(200).json({
      message: 'Sample data creation completed',
      results
    });

  } catch (error: any) {
    console.error('❌ Error creating sample data:', error);
    res.status(500).json({
      error: 'Failed to create sample data',
      details: error.message
    });
  }
}
