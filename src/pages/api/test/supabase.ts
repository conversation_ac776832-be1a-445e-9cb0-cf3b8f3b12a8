import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Testing Supabase connection...');
    console.log('🔍 Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
    console.log('🔍 Supabase Key exists:', !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);

    // Test inventory items
    const inventoryResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/inventory_items?select=*&limit=5`, {
      method: 'GET',
      headers: {
        'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    console.log('🔍 Inventory response status:', inventoryResponse.status);
    const inventoryData = await inventoryResponse.json();
    console.log('🔍 Inventory data:', inventoryData);

    // Test menu items
    const itemsResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items?select=*&limit=5`, {
      method: 'GET',
      headers: {
        'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    console.log('🔍 Items response status:', itemsResponse.status);
    const itemsData = await itemsResponse.json();
    console.log('🔍 Items data:', itemsData);

    // Test our backend APIs too
    const backendInventoryResponse = await fetch(`${req.headers.host?.includes('localhost') ? 'http://localhost:3000' : 'https://inventory-management-ivory-nu.vercel.app'}/api/inventory/items?limit=5`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const backendInventoryData = backendInventoryResponse.ok ? await backendInventoryResponse.json() : null;

    const backendItemsResponse = await fetch(`${req.headers.host?.includes('localhost') ? 'http://localhost:3000' : 'https://inventory-management-ivory-nu.vercel.app'}/api/items/menu?limit=5`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const backendItemsData = backendItemsResponse.ok ? await backendItemsResponse.json() : null;

    res.status(200).json({
      supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasApiKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      directSupabase: {
        inventory: {
          status: inventoryResponse.status,
          count: Array.isArray(inventoryData) ? inventoryData.length : 0,
          data: inventoryData
        },
        items: {
          status: itemsResponse.status,
          count: Array.isArray(itemsData) ? itemsData.length : 0,
          data: itemsData
        }
      },
      backendAPIs: {
        inventory: {
          status: backendInventoryResponse.status,
          data: backendInventoryData
        },
        items: {
          status: backendItemsResponse.status,
          data: backendItemsData
        }
      }
    });
  } catch (error: any) {
    console.error('❌ Error testing Supabase:', error);
    res.status(500).json({
      error: 'Failed to test Supabase connection',
      details: error.message
    });
  }
}
