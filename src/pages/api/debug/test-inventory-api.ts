import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Testing inventory API call...');

    const results = {
      directSupabase: null,
      backendAPI: null,
      comparison: null
    };

    // Test 1: Direct Supabase call (what should work)
    try {
      console.log('📡 Testing direct Supabase call...');
      const directResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/inventory_items?select=*&limit=20`, {
        method: 'GET',
        headers: {
          'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (directResponse.ok) {
        const directData = await directResponse.json();
        results.directSupabase = {
          status: directResponse.status,
          count: Array.isArray(directData) ? directData.length : 0,
          data: directData,
          headers: Object.fromEntries(directResponse.headers.entries())
        };
        console.log('✅ Direct Supabase call successful:', directData.length, 'items');
      } else {
        const errorText = await directResponse.text();
        results.directSupabase = {
          status: directResponse.status,
          error: errorText,
          headers: Object.fromEntries(directResponse.headers.entries())
        };
        console.error('❌ Direct Supabase call failed:', directResponse.status, errorText);
      }
    } catch (error: any) {
      results.directSupabase = {
        error: error.message,
        exception: true
      };
      console.error('❌ Direct Supabase call exception:', error);
    }

    // Test 2: Backend API call (what the frontend uses)
    try {
      console.log('🔄 Testing backend API call...');
      const baseUrl = req.headers.host?.includes('localhost') ? 'http://localhost:3000' : 'https://inventory-management-ivory-nu.vercel.app';
      const backendResponse = await fetch(`${baseUrl}/api/inventory/items?limit=20`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (backendResponse.ok) {
        const backendData = await backendResponse.json();
        results.backendAPI = {
          status: backendResponse.status,
          count: backendData.data ? backendData.data.length : 0,
          data: backendData,
          headers: Object.fromEntries(backendResponse.headers.entries())
        };
        console.log('✅ Backend API call successful:', backendData.data?.length || 0, 'items');
      } else {
        const errorText = await backendResponse.text();
        results.backendAPI = {
          status: backendResponse.status,
          error: errorText,
          headers: Object.fromEntries(backendResponse.headers.entries())
        };
        console.error('❌ Backend API call failed:', backendResponse.status, errorText);
      }
    } catch (error: any) {
      results.backendAPI = {
        error: error.message,
        exception: true
      };
      console.error('❌ Backend API call exception:', error);
    }

    // Test 3: Service role key call (what should bypass RLS)
    try {
      console.log('🔑 Testing service role key call...');
      const serviceResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/inventory_items?select=*&limit=20`, {
        method: 'GET',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (serviceResponse.ok) {
        const serviceData = await serviceResponse.json();
        results.serviceRoleKey = {
          status: serviceResponse.status,
          count: Array.isArray(serviceData) ? serviceData.length : 0,
          data: serviceData,
          headers: Object.fromEntries(serviceResponse.headers.entries())
        };
        console.log('✅ Service role key call successful:', serviceData.length, 'items');
      } else {
        const errorText = await serviceResponse.text();
        results.serviceRoleKey = {
          status: serviceResponse.status,
          error: errorText,
          headers: Object.fromEntries(serviceResponse.headers.entries())
        };
        console.error('❌ Service role key call failed:', serviceResponse.status, errorText);
      }
    } catch (error: any) {
      results.serviceRoleKey = {
        error: error.message,
        exception: true
      };
      console.error('❌ Service role key call exception:', error);
    }

    // Comparison
    results.comparison = {
      directSupabaseCount: results.directSupabase?.count || 0,
      backendAPICount: results.backendAPI?.count || 0,
      serviceRoleKeyCount: results.serviceRoleKey?.count || 0,
      issue: results.directSupabase?.count > 0 && results.backendAPI?.count === 0 ? 'Backend API not returning data despite direct Supabase working' : 'Unknown issue'
    };

    console.log('✅ Inventory API test completed');
    res.status(200).json({
      message: 'Inventory API test completed',
      results,
      environment: {
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
        hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
        host: req.headers.host
      }
    });

  } catch (error: any) {
    console.error('❌ Error testing inventory API:', error);
    res.status(500).json({
      error: 'Failed to test inventory API',
      details: error.message
    });
  }
}
