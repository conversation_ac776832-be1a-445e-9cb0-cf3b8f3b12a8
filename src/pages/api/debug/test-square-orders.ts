import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Testing Square Orders API...');

    // Get access token from Supabase
    const configResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/square_config?select=access_token&limit=1`, {
      method: 'GET',
      headers: {
        'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    if (!configResponse.ok) {
      return res.status(500).json({ 
        error: 'Failed to get Square config',
        status: configResponse.status 
      });
    }

    const configData = await configResponse.json();
    if (!configData || configData.length === 0 || !configData[0].access_token) {
      return res.status(401).json({ 
        error: 'No Square access token found',
        details: 'Please configure Square API access token first'
      });
    }

    const accessToken = configData[0].access_token;
    console.log('✅ Square access token found');

    // Get locations
    const locationsResponse = await fetch('https://connect.squareup.com/v2/locations', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18',
      },
    });

    if (!locationsResponse.ok) {
      const errorText = await locationsResponse.text();
      return res.status(locationsResponse.status).json({
        error: 'Square locations API error',
        status: locationsResponse.status,
        details: errorText
      });
    }

    const locationsData = await locationsResponse.json();
    const locationIds = (locationsData.locations || []).map((loc: any) => loc.id);
    console.log('✅ Square locations:', locationIds.length);

    // Test orders search (last 7 days)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 7);

    const requestBody = {
      location_ids: locationIds,
      query: {
        filter: {
          date_time_filter: {
            created_at: {
              start_at: `${startDate.toISOString().split('T')[0]}T00:00:00Z`,
              end_at: `${endDate.toISOString().split('T')[0]}T23:59:59Z`
            }
          }
        }
      },
      limit: 10 // Small limit for testing
    };

    console.log('🟨 Testing orders search with:', JSON.stringify(requestBody, null, 2));

    const ordersResponse = await fetch('https://connect.squareup.com/v2/orders/search', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18',
      },
      body: JSON.stringify(requestBody),
    });

    console.log('🔍 Orders response status:', ordersResponse.status);

    if (!ordersResponse.ok) {
      const errorText = await ordersResponse.text();
      return res.status(ordersResponse.status).json({
        error: 'Square orders API error',
        status: ordersResponse.status,
        details: errorText,
        requestBody
      });
    }

    const ordersData = await ordersResponse.json();
    console.log('✅ Orders fetched successfully');

    // Analyze the orders
    const orders = ordersData.orders || [];
    const analysis = {
      totalOrders: orders.length,
      dateRange: {
        start: startDate.toISOString().split('T')[0],
        end: endDate.toISOString().split('T')[0]
      },
      locations: locationIds,
      sampleOrders: orders.slice(0, 3).map((order: any) => ({
        id: order.id,
        state: order.state,
        created_at: order.created_at,
        total_money: order.total_money,
        line_items_count: order.line_items?.length || 0,
        line_items: (order.line_items || []).slice(0, 2).map((item: any) => ({
          catalog_object_id: item.catalog_object_id,
          name: item.name,
          quantity: item.quantity,
          total_money: item.total_money
        }))
      })),
      allCatalogIds: [...new Set(
        orders.flatMap((order: any) => 
          (order.line_items || []).map((item: any) => item.catalog_object_id)
        ).filter(Boolean)
      )]
    };

    res.status(200).json({
      success: true,
      message: 'Square Orders API test completed',
      analysis
    });

  } catch (error: any) {
    console.error('❌ Error in Square orders test:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
