import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Checking database items...');

    // Get all items from database
    const itemsResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items?select=id,name,square_item_id,updated_at&order=name`, {
      method: 'GET',
      headers: {
        'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    if (!itemsResponse.ok) {
      return res.status(itemsResponse.status).json({
        error: 'Failed to fetch items from database',
        status: itemsResponse.status
      });
    }

    const items = await itemsResponse.json();
    console.log('✅ Items fetched from database:', items.length);

    const analysis = {
      totalItems: items.length,
      itemsWithSquareId: items.filter((item: any) => item.square_item_id).length,
      itemsWithoutSquareId: items.filter((item: any) => !item.square_item_id).length,
      recentlyUpdated: items.filter((item: any) => {
        if (!item.updated_at) return false;
        const updatedTime = new Date(item.updated_at).getTime();
        const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
        return updatedTime > fiveMinutesAgo;
      }).length,
      sampleItems: items.slice(0, 10).map((item: any) => ({
        id: item.id,
        name: item.name,
        square_item_id: item.square_item_id,
        hasSquareId: !!item.square_item_id,
        updated_at: item.updated_at
      })),
      allSquareIds: items
        .filter((item: any) => item.square_item_id)
        .map((item: any) => item.square_item_id),
      itemsWithoutSquareIds: items
        .filter((item: any) => !item.square_item_id)
        .map((item: any) => ({
          id: item.id,
          name: item.name,
          updated_at: item.updated_at
        }))
    };

    res.status(200).json({
      success: true,
      message: 'Database items check completed',
      analysis
    });

  } catch (error: any) {
    console.error('❌ Error checking database items:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
