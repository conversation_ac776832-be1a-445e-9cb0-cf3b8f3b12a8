import { NextApiRequest, NextApiResponse } from 'next';

/**
 * Debug endpoint to test transactions table
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    console.log('🔍 Testing transactions table...');

    // Test if transactions table exists
    const testResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/transactions?limit=1`, {
      method: 'GET',
      headers: {
        'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    console.log('🔍 Transactions table test response status:', testResponse.status);

    if (!testResponse.ok) {
      const errorText = await testResponse.text();
      console.error('❌ Transactions table error:', testResponse.status, errorText);
      
      return res.status(200).json({
        status: 'error',
        message: 'Transactions table not accessible',
        error: errorText,
        statusCode: testResponse.status
      });
    }

    const data = await testResponse.json();
    console.log('✅ Transactions table accessible, found', data.length, 'records');

    // Test creating a simple transaction
    if (req.method === 'POST') {
      const testTransaction = {
        inventory_item_id: '00000000-0000-0000-0000-000000000000', // Dummy UUID
        type: 'purchase',
        quantity: 1,
        cost: 0,
        total_cost: 0,
        notes: 'Test transaction',
        date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const createResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/transactions`, {
        method: 'POST',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=representation',
        },
        body: JSON.stringify(testTransaction),
      });

      if (!createResponse.ok) {
        const createErrorText = await createResponse.text();
        console.error('❌ Create transaction error:', createResponse.status, createErrorText);
        
        return res.status(200).json({
          status: 'create_error',
          message: 'Cannot create transactions',
          error: createErrorText,
          statusCode: createResponse.status
        });
      }

      const createdData = await createResponse.json();
      console.log('✅ Test transaction created successfully');

      return res.status(200).json({
        status: 'success',
        message: 'Transactions table fully functional',
        testData: createdData
      });
    }

    return res.status(200).json({
      status: 'accessible',
      message: 'Transactions table is accessible',
      recordCount: data.length,
      sampleData: data
    });

  } catch (error: any) {
    console.error('❌ Error testing transactions:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Internal server error',
      error: error.message
    });
  }
}
