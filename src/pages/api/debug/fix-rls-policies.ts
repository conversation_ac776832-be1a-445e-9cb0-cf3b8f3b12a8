import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔧 Fixing RLS policies for inventory_items table...');

    const results = {
      policies: [],
      errors: []
    };

    // RLS policies to create
    const policies = [
      {
        name: 'Enable read access for inventory_items',
        sql: `
          CREATE POLICY "Enable read access for inventory_items" ON "public"."inventory_items"
          AS PERMISSIVE FOR SELECT
          TO public
          USING (true);
        `
      },
      {
        name: 'Enable insert access for inventory_items',
        sql: `
          CREATE POLICY "Enable insert access for inventory_items" ON "public"."inventory_items"
          AS PERMISSIVE FOR INSERT
          TO public
          WITH CHECK (true);
        `
      },
      {
        name: 'Enable update access for inventory_items',
        sql: `
          CREATE POLICY "Enable update access for inventory_items" ON "public"."inventory_items"
          AS PERMISSIVE FOR UPDATE
          TO public
          USING (true)
          WITH CHECK (true);
        `
      },
      {
        name: 'Enable delete access for inventory_items',
        sql: `
          CREATE POLICY "Enable delete access for inventory_items" ON "public"."inventory_items"
          AS PERMISSIVE FOR DELETE
          TO public
          USING (true);
        `
      }
    ];

    // Execute each policy creation
    for (const policy of policies) {
      try {
        console.log(`🔄 Creating policy: ${policy.name}`);
        
        const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/rpc/exec_sql`, {
          method: 'POST',
          headers: {
            'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || '',
            'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            sql: policy.sql
          }),
        });

        if (response.ok) {
          const data = await response.json();
          results.policies.push({
            name: policy.name,
            status: 'created',
            response: data
          });
          console.log(`✅ Created policy: ${policy.name}`);
        } else {
          const errorText = await response.text();
          console.log(`⚠️ Policy may already exist: ${policy.name} - ${errorText}`);
          results.policies.push({
            name: policy.name,
            status: 'already_exists_or_error',
            error: errorText
          });
        }
      } catch (error: any) {
        console.error(`❌ Error creating policy ${policy.name}:`, error);
        results.errors.push(`Error creating policy ${policy.name}: ${error.message}`);
      }
    }

    // Alternative approach: Use direct SQL execution if RPC doesn't work
    if (results.policies.length === 0) {
      console.log('🔄 Trying alternative approach with direct SQL...');
      
      try {
        // Try to create a simple read policy using PostgREST
        const simplePolicy = `
          DROP POLICY IF EXISTS "inventory_items_read_policy" ON "public"."inventory_items";
          CREATE POLICY "inventory_items_read_policy" ON "public"."inventory_items"
          FOR SELECT USING (true);
        `;

        const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/rpc/exec_sql`, {
          method: 'POST',
          headers: {
            'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || '',
            'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            sql: simplePolicy
          }),
        });

        if (response.ok) {
          results.policies.push({
            name: 'Simple read policy',
            status: 'created_via_alternative',
            response: await response.json()
          });
        } else {
          const errorText = await response.text();
          results.errors.push(`Alternative approach failed: ${errorText}`);
        }
      } catch (error: any) {
        results.errors.push(`Alternative approach error: ${error.message}`);
      }
    }

    console.log('✅ RLS policy fix completed');
    res.status(200).json({
      message: 'RLS policy fix completed',
      results,
      instructions: {
        manual_fix: 'If automatic fix failed, go to Supabase Dashboard > Authentication > RLS and create a policy for inventory_items table that allows SELECT for public role',
        sql_to_run: 'CREATE POLICY "inventory_items_read_policy" ON "public"."inventory_items" FOR SELECT USING (true);'
      }
    });

  } catch (error: any) {
    console.error('❌ Error fixing RLS policies:', error);
    res.status(500).json({
      error: 'Failed to fix RLS policies',
      details: error.message,
      manual_instructions: {
        step1: 'Go to Supabase Dashboard',
        step2: 'Navigate to Authentication > RLS',
        step3: 'Find inventory_items table',
        step4: 'Create new policy: SELECT for public role with USING (true)',
        step5: 'Save and test again'
      }
    });
  }
}
