import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Listing all items from recent orders...');

    // Get access token
    const configResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/square_config?select=access_token&limit=1`, {
      method: 'GET',
      headers: {
        'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    const configData = await configResponse.json();
    const accessToken = configData[0]?.access_token;

    if (!accessToken) {
      return res.status(401).json({ error: 'No Square access token found' });
    }

    // Get locations
    const locationsResponse = await fetch('https://connect.squareup.com/v2/locations', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18',
      },
    });

    const locationsData = await locationsResponse.json();
    const locationIds = (locationsData.locations || []).map((loc: any) => loc.id);

    // Search orders from last 30 days
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    const requestBody = {
      location_ids: locationIds,
      query: {
        filter: {
          date_time_filter: {
            created_at: {
              start_at: `${startDate.toISOString().split('T')[0]}T00:00:00Z`,
              end_at: `${endDate.toISOString().split('T')[0]}T23:59:59Z`
            }
          }
        }
      },
      limit: 1000
    };

    const ordersResponse = await fetch('https://connect.squareup.com/v2/orders/search', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18',
      },
      body: JSON.stringify(requestBody),
    });

    const ordersData = await ordersResponse.json();
    const orders = ordersData.orders || [];

    // Collect all items from orders
    const itemStats: Record<string, {
      catalogId: string;
      name: string;
      totalOrders: number;
      totalQuantity: number;
      totalRevenue: number;
      lastOrderDate: string;
    }> = {};

    orders.forEach((order: any) => {
      (order.line_items || []).forEach((lineItem: any) => {
        const catalogId = lineItem.catalog_object_id;
        const name = lineItem.name || 'Unknown Item';
        const quantity = parseInt(lineItem.quantity) || 0;
        const revenue = lineItem.total_money?.amount || 0;

        if (catalogId) {
          if (!itemStats[catalogId]) {
            itemStats[catalogId] = {
              catalogId,
              name,
              totalOrders: 0,
              totalQuantity: 0,
              totalRevenue: 0,
              lastOrderDate: order.created_at
            };
          }

          itemStats[catalogId].totalOrders++;
          itemStats[catalogId].totalQuantity += quantity;
          itemStats[catalogId].totalRevenue += revenue;
          
          // Update last order date if this order is more recent
          if (new Date(order.created_at) > new Date(itemStats[catalogId].lastOrderDate)) {
            itemStats[catalogId].lastOrderDate = order.created_at;
          }
        }
      });
    });

    // Convert to array and sort by total orders (most popular first)
    const itemList = Object.values(itemStats)
      .map(item => ({
        ...item,
        totalRevenue: item.totalRevenue / 100, // Convert from cents to dollars
        lastOrderDate: new Date(item.lastOrderDate).toLocaleDateString()
      }))
      .sort((a, b) => b.totalOrders - a.totalOrders);

    const result = {
      dateRange: {
        start: startDate.toISOString().split('T')[0],
        end: endDate.toISOString().split('T')[0]
      },
      totalUniqueItems: itemList.length,
      totalOrders: orders.length,
      items: itemList,
      // Top 5 most popular items for quick testing
      topItems: itemList.slice(0, 5).map(item => ({
        catalogId: item.catalogId,
        name: item.name,
        totalOrders: item.totalOrders,
        testUrl: `/api/square/item-orders?itemId=${item.catalogId}&days=30`
      }))
    };

    res.status(200).json(result);

  } catch (error: any) {
    console.error('❌ Error listing order items:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
