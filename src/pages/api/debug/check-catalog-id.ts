import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { catalogId } = req.query;
    
    if (!catalogId) {
      return res.status(400).json({ error: 'catalogId parameter is required' });
    }

    console.log('🔍 Checking catalog ID in orders:', catalogId);

    // Get access token
    const configResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/square_config?select=access_token&limit=1`, {
      method: 'GET',
      headers: {
        'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    const configData = await configResponse.json();
    const accessToken = configData[0]?.access_token;

    if (!accessToken) {
      return res.status(401).json({ error: 'No Square access token found' });
    }

    // Get locations
    const locationsResponse = await fetch('https://connect.squareup.com/v2/locations', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18',
      },
    });

    const locationsData = await locationsResponse.json();
    const locationIds = (locationsData.locations || []).map((loc: any) => loc.id);

    // Search orders from last 30 days
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    const requestBody = {
      location_ids: locationIds,
      query: {
        filter: {
          date_time_filter: {
            created_at: {
              start_at: `${startDate.toISOString().split('T')[0]}T00:00:00Z`,
              end_at: `${endDate.toISOString().split('T')[0]}T23:59:59Z`
            }
          }
        }
      },
      limit: 1000
    };

    const ordersResponse = await fetch('https://connect.squareup.com/v2/orders/search', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18',
      },
      body: JSON.stringify(requestBody),
    });

    const ordersData = await ordersResponse.json();
    const orders = ordersData.orders || [];

    // Check if the catalog ID appears in any orders
    let foundInOrders = false;
    let orderCount = 0;
    let totalQuantity = 0;
    const matchingOrders: any[] = [];

    orders.forEach((order: any) => {
      (order.line_items || []).forEach((lineItem: any) => {
        if (lineItem.catalog_object_id === catalogId) {
          foundInOrders = true;
          orderCount++;
          totalQuantity += parseInt(lineItem.quantity) || 0;
          
          matchingOrders.push({
            orderId: order.id,
            orderDate: order.created_at,
            itemName: lineItem.name,
            quantity: lineItem.quantity,
            totalMoney: lineItem.total_money
          });
        }
      });
    });

    // Get all catalog IDs from orders for comparison
    const allCatalogIds = [...new Set(
      orders.flatMap((order: any) => 
        (order.line_items || []).map((item: any) => item.catalog_object_id)
      ).filter(Boolean)
    )];

    const result = {
      catalogId: catalogId as string,
      foundInOrders,
      analysis: {
        ordersContainingItem: orderCount,
        totalQuantityOrdered: totalQuantity,
        dateRange: {
          start: startDate.toISOString().split('T')[0],
          end: endDate.toISOString().split('T')[0]
        },
        matchingOrders: matchingOrders.slice(0, 5), // Show first 5
        allCatalogIdsInOrders: allCatalogIds,
        isInCatalogList: allCatalogIds.includes(catalogId as string)
      }
    };

    res.status(200).json(result);

  } catch (error: any) {
    console.error('❌ Error checking catalog ID:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
