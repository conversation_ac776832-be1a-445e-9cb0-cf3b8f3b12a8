import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Creating inventory items directly...');

    // Sample inventory items with camelCase field names for backend API
    const sampleInventoryItems = [
      {
        name: 'Rice',
        vietnameseName: 'Gạo',
        category: 'Grains',
        unit: 'kg',
        quantity: 50,
        costPerUnit: 2.50,
        minThreshold: 10,
        storage: 'Dry Storage'
      },
      {
        name: 'Chicken Breast',
        vietnameseName: 'Ức gà',
        category: 'Meat',
        unit: 'kg',
        quantity: 25,
        costPerUnit: 8.99,
        minThreshold: 5,
        storage: 'Refrigerator'
      },
      {
        name: 'Fish Sauce',
        vietnameseName: 'Nước mắm',
        category: 'Condiments',
        unit: 'bottle',
        quantity: 12,
        costPerUnit: 3.50,
        minThreshold: 3,
        storage: 'Pantry'
      }
    ];

    const results = {
      created: [],
      errors: []
    };

    // Create each item using the backend API
    for (const item of sampleInventoryItems) {
      try {
        const baseUrl = req.headers.host?.includes('localhost') ? 'http://localhost:3000' : 'https://inventory-management-ivory-nu.vercel.app';
        const response = await fetch(`${baseUrl}/api/inventory/items`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(item),
        });

        if (response.ok) {
          const data = await response.json();
          results.created.push(data);
          console.log('✅ Created inventory item:', item.name);
        } else {
          const error = await response.text();
          console.error('❌ Failed to create inventory item:', item.name, error);
          results.errors.push(`Failed to create ${item.name}: ${error}`);
        }
      } catch (error: any) {
        console.error('❌ Error creating inventory item:', item.name, error);
        results.errors.push(`Error creating ${item.name}: ${error.message}`);
      }
    }

    console.log('✅ Inventory creation completed');
    res.status(200).json({
      message: 'Inventory creation completed',
      results
    });

  } catch (error: any) {
    console.error('❌ Error creating inventory items:', error);
    res.status(500).json({
      error: 'Failed to create inventory items',
      details: error.message
    });
  }
}
