import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Checking database tables...');

    const results = {
      inventory_items: { count: 0, sample: null, error: null },
      items: { count: 0, sample: null, error: null }
    };

    // Check inventory_items table
    try {
      const inventoryResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/inventory_items?select=*&limit=5`, {
        method: 'GET',
        headers: {
          'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (inventoryResponse.ok) {
        const inventoryData = await inventoryResponse.json();
        results.inventory_items.count = Array.isArray(inventoryData) ? inventoryData.length : 0;
        results.inventory_items.sample = inventoryData;
      } else {
        const errorText = await inventoryResponse.text();
        results.inventory_items.error = `${inventoryResponse.status}: ${errorText}`;
      }
    } catch (error: any) {
      results.inventory_items.error = error.message;
    }

    // Check items table
    try {
      const itemsResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items?select=*&limit=5`, {
        method: 'GET',
        headers: {
          'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (itemsResponse.ok) {
        const itemsData = await itemsResponse.json();
        results.items.count = Array.isArray(itemsData) ? itemsData.length : 0;
        results.items.sample = itemsData;
      } else {
        const errorText = await itemsResponse.text();
        results.items.error = `${itemsResponse.status}: ${errorText}`;
      }
    } catch (error: any) {
      results.items.error = error.message;
    }

    // Get total counts
    try {
      const inventoryCountResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/inventory_items?select=count`, {
        method: 'GET',
        headers: {
          'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'count=exact',
        },
      });

      if (inventoryCountResponse.ok) {
        const totalCount = inventoryCountResponse.headers.get('content-range')?.split('/')[1] || '0';
        results.inventory_items.totalCount = parseInt(totalCount);
      }
    } catch (error) {
      console.warn('Could not get inventory count');
    }

    try {
      const itemsCountResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items?select=count`, {
        method: 'GET',
        headers: {
          'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'count=exact',
        },
      });

      if (itemsCountResponse.ok) {
        const totalCount = itemsCountResponse.headers.get('content-range')?.split('/')[1] || '0';
        results.items.totalCount = parseInt(totalCount);
      }
    } catch (error) {
      console.warn('Could not get items count');
    }

    console.log('✅ Database check completed');
    res.status(200).json({
      message: 'Database tables checked',
      results,
      environment: {
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
        hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY
      }
    });

  } catch (error: any) {
    console.error('❌ Error checking database tables:', error);
    res.status(500).json({
      error: 'Failed to check database tables',
      details: error.message
    });
  }
}
