import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Creating inventory items directly with service role key...');

    // Sample inventory items with exact field names for Supabase
    const sampleInventoryItems = [
      {
        name: 'Rice',
        vietnamese_name: 'Gạo',
        category: 'Grains',
        unit: 'kg',
        quantity: 50,
        cost_per_unit: 2.50,
        min_threshold: 10,
        storage: 'Dry Storage',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        name: 'Chicken Breast',
        vietnamese_name: 'Ức gà',
        category: 'Meat',
        unit: 'kg',
        quantity: 25,
        cost_per_unit: 8.99,
        min_threshold: 5,
        storage: 'Refrigerator',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        name: 'Fish Sauce',
        vietnamese_name: 'Nước mắm',
        category: 'Condiments',
        unit: 'bottle',
        quantity: 12,
        cost_per_unit: 3.50,
        min_threshold: 3,
        storage: 'Pantry',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    const results = {
      created: [],
      errors: [],
      attempts: []
    };

    // Try creating each item directly with service role key
    for (const item of sampleInventoryItems) {
      try {
        console.log(`🔄 Creating ${item.name}...`);
        
        const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/inventory_items`, {
          method: 'POST',
          headers: {
            'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
            'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
            'Content-Type': 'application/json',
            'Prefer': 'return=representation',
          },
          body: JSON.stringify(item),
        });

        const attemptInfo = {
          item: item.name,
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries())
        };

        if (response.ok) {
          const data = await response.json();
          results.created.push(data[0] || data);
          attemptInfo.success = true;
          attemptInfo.data = data;
          console.log(`✅ Created ${item.name}:`, data);
        } else {
          const errorText = await response.text();
          attemptInfo.success = false;
          attemptInfo.error = errorText;
          console.error(`❌ Failed to create ${item.name}:`, response.status, errorText);
          results.errors.push(`Failed to create ${item.name}: ${response.status} - ${errorText}`);
        }

        results.attempts.push(attemptInfo);

      } catch (error: any) {
        console.error(`❌ Exception creating ${item.name}:`, error);
        results.errors.push(`Exception creating ${item.name}: ${error.message}`);
        results.attempts.push({
          item: item.name,
          success: false,
          error: error.message,
          exception: true
        });
      }
    }

    console.log('✅ Direct inventory creation completed');
    res.status(200).json({
      message: 'Direct inventory creation completed',
      results,
      environment: {
        hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
        hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        usingServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY
      }
    });

  } catch (error: any) {
    console.error('❌ Error in direct inventory creation:', error);
    res.status(500).json({
      error: 'Failed to create inventory items directly',
      details: error.message
    });
  }
}
