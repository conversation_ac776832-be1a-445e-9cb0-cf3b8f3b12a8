import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Testing Items API...');

    // Test items API
    const itemsResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items?select=id,name,square_item_id&limit=10`, {
      method: 'GET',
      headers: {
        'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    if (!itemsResponse.ok) {
      return res.status(itemsResponse.status).json({
        error: 'Failed to fetch items from database',
        status: itemsResponse.status
      });
    }

    const itemsData = await itemsResponse.json();
    console.log('✅ Items fetched from database:', itemsData.length);

    const analysis = {
      totalItems: itemsData.length,
      itemsWithSquareId: itemsData.filter((item: any) => item.square_item_id).length,
      itemsWithoutSquareId: itemsData.filter((item: any) => !item.square_item_id).length,
      sampleItems: itemsData.slice(0, 5).map((item: any) => ({
        id: item.id,
        name: item.name,
        square_item_id: item.square_item_id,
        hasSquareId: !!item.square_item_id
      })),
      squareItemIds: itemsData
        .filter((item: any) => item.square_item_id)
        .map((item: any) => item.square_item_id)
    };

    res.status(200).json({
      success: true,
      message: 'Items database test completed',
      analysis
    });

  } catch (error: any) {
    console.error('❌ Error in items test:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
