import { NextApiRequest, NextApiResponse } from 'next';
import { mapSquareOrderToLocalVariants, formatMappingResults } from '@/utils/squareVariantMapping';

/**
 * Test Square Variant Mapping API
 * 
 * This endpoint tests the mapping between Square order line items and local variants
 * using the sample data from result.json
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Sample Square order line items from result.json
    const sampleSquareLineItems = [
      {
        catalog_object_id: "ARS3TZD7Z3EG2LHDRK5BJ6GG",
        name: "Charcoal chicken with broken rice (Cơm gà nướng)",
        variation_name: "Regular",
        quantity: "1",
        base_price_money: {
          amount: 1750,
          currency: "AUD"
        }
      },
      {
        catalog_object_id: "G36Z2AAEAGQFM3NPMBQGCBQN",
        name: "Phở bò (<PERSON><PERSON>o)",
        variation_name: "Regular",
        quantity: "1",
        base_price_money: {
          amount: 1850,
          currency: "AUD"
        }
      },
      {
        catalog_object_id: "XN26JPNMEXTCJJH6O7P4D6F5",
        name: "Hà Nội Vermicelli (Bún chả Hà Nội)",
        variation_name: "Regular",
        quantity: "1",
        base_price_money: {
          amount: 1850,
          currency: "AUD"
        }
      },
      {
        catalog_object_id: "CPD7YUKPSBQCTRDYBLAV7EV4",
        name: "Charcoal Pork Chop Combo",
        variation_name: "Regular",
        quantity: "1",
        base_price_money: {
          amount: 2250,
          currency: "AUD"
        }
      },
      {
        catalog_object_id: "7HDRRPGYGFTZ763Z3U2GBRZK",
        name: "bò lá lốt",
        variation_name: "Regular",
        quantity: "1",
        base_price_money: {
          amount: 1450,
          currency: "AUD"
        }
      },
      {
        catalog_object_id: "C4XTLDOHSNQHGFDYW66NFDTI",
        name: "Peach Iced Tea Orange & Lemongrass (Trà đào cam sả)",
        variation_name: "Large",
        quantity: "2",
        base_price_money: {
          amount: 850,
          currency: "AUD"
        }
      }
    ];

    console.log('🔍 Testing Square variant mapping with sample data...');

    // Use provided line items or sample data
    const lineItems = req.body.lineItems || sampleSquareLineItems;

    // Map Square line items to local variants
    const mappings = await mapSquareOrderToLocalVariants(lineItems);

    // Format results
    const formattedResults = formatMappingResults(mappings);

    console.log('📊 Mapping Results:');
    console.log(formattedResults);

    // Return detailed results
    res.status(200).json({
      success: true,
      mappings,
      summary: formattedResults,
      sampleData: {
        totalLineItems: lineItems.length,
        sampleLineItems: lineItems.slice(0, 3) // Show first 3 for reference
      }
    });

  } catch (error: any) {
    console.error('❌ Error testing variant mapping:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
