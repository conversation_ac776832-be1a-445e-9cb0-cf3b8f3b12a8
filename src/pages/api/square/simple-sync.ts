import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { itemId, squareId } = req.body;

    if (!itemId || !squareId) {
      return res.status(400).json({ 
        error: 'Both itemId and squareId are required',
        example: { itemId: 'uuid', squareId: 'SQUARE_CATALOG_ID' }
      });
    }

    console.log(`🔄 Manually linking item ${itemId} to Square ID ${squareId}`);

    // Update the specific item
    const updateResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items?id=eq.${itemId}`, {
      method: 'PATCH',
      headers: {
        'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
        'Prefer': 'return=representation',
      },
      body: JSON.stringify({
        square_item_id: squareId,
        updated_at: new Date().toISOString()
      }),
    });

    if (!updateResponse.ok) {
      const errorText = await updateResponse.text();
      return res.status(updateResponse.status).json({
        error: 'Failed to update item',
        details: errorText
      });
    }

    const updatedItem = await updateResponse.json();
    console.log('✅ Item updated successfully');

    res.status(200).json({
      success: true,
      message: 'Item linked to Square successfully',
      item: updatedItem[0]
    });

  } catch (error: any) {
    console.error('❌ Error in simple sync:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
