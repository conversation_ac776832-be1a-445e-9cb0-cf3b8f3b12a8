import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method === 'GET') {
      // Check Square configuration
      console.log('🟨 Checking Square config from Supabase...');

      const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/square_config?select=*&limit=1`, {
        method: 'GET',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      console.log('🔍 Config check response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('🔍 Config check data:', data);

        if (data && data.length > 0) {
          console.log('✅ Square configuration found');
          return res.status(200).json({
            configured: true,
            hasAccessToken: !!data[0].access_token,
            lastUpdated: data[0].updated_at,
            accessToken: data[0].access_token
          });
        } else {
          console.log('⚠️ No Square configuration found (table empty)');
          return res.status(200).json({ configured: false });
        }
      } else {
        const errorText = await response.text();
        console.error('❌ Error checking Square config:', response.status, errorText);

        if (response.status === 404 || errorText.includes('does not exist')) {
          console.warn('⚠️ Square config table does not exist yet');
          return res.status(200).json({ configured: false, tableExists: false });
        }

        if (response.status === 401 || response.status === 403) {
          console.warn('⚠️ Square config table access denied');
          return res.status(200).json({ configured: false, accessDenied: true });
        }

        throw new Error(`Failed to check Square configuration: ${response.status} - ${errorText}`);
      }
    } else if (req.method === 'POST') {
      // Save Square configuration
      const { accessToken } = req.body;

      if (!accessToken) {
        return res.status(400).json({ error: 'Access token is required' });
      }

      console.log('🟨 Saving Square access token to Supabase...');

      // Check if any record exists first
      console.log('🔍 Checking for existing records...');
      const existingResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/square_config?select=id&limit=1`, {
        method: 'GET',
        headers: {
          'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      console.log('🔍 Existing check status:', existingResponse.status);
      const existingData = await existingResponse.json();
      console.log('🔍 Existing records:', existingData);

      // Always try to upsert with id=1 to ensure single record
      console.log('🔄 Upserting Square config with id=1...');
      const upsertResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/square_config`, {
        method: 'POST',
        headers: {
          'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=representation,resolution=merge-duplicates',
        },
        body: JSON.stringify({
          id: 1,
          access_token: accessToken,
          updated_at: new Date().toISOString()
        }),
      });

      console.log('🔄 Upsert response status:', upsertResponse.status);
      if (upsertResponse.ok) {
        const data = await upsertResponse.json();
        console.log('✅ Upsert successful:', data);
        return res.status(200).json({ success: true, data });
      } else {
        const errorText = await upsertResponse.text();
        console.error('❌ Upsert failed:', errorText);

        // If upsert fails, try update instead
        if (existingData && existingData.length > 0) {
          console.log('🔄 Fallback: Updating existing record with ID:', existingData[0].id);
          const updateResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/square_config?id=eq.${existingData[0].id}`, {
            method: 'PATCH',
            headers: {
              'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
              'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
              'Content-Type': 'application/json',
              'Prefer': 'return=representation',
            },
            body: JSON.stringify({
              access_token: accessToken,
              updated_at: new Date().toISOString()
            }),
          });

          console.log('🔄 Update response status:', updateResponse.status);
          if (updateResponse.ok) {
            const data = await updateResponse.json();
            console.log('✅ Update successful:', data);
            return res.status(200).json({ success: true, data });
          } else {
            const updateErrorText = await updateResponse.text();
            return res.status(updateResponse.status).json({
              error: `Update failed: ${updateResponse.status} - ${updateErrorText}`
            });
          }
        } else {
          return res.status(upsertResponse.status).json({
            error: `Upsert failed: ${upsertResponse.status} - ${errorText}`
          });
        }
      }
    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error: any) {
    console.error('❌ Error in Square config API:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
