import { NextApiRequest, NextApiResponse } from 'next';

// Helper function to create a default variant when no Square variations exist
async function createDefaultVariant(itemId: string, itemName: string) {
  const variantData = {
    item_id: itemId,
    square_variation_id: null, // No Square variation ID for default
    name: 'Default',
    selling_price: 0, // Default price
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const variantResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/item_variants`, {
    method: 'POST',
    headers: {
      'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
      'Content-Type': 'application/json',
      'Prefer': 'return=representation',
    },
    body: JSON.stringify(variantData),
  });

  if (variantResponse.ok) {
    console.log(`✅ Created default variant for ${itemName}`);
  } else {
    const variantError = await variantResponse.text();
    console.error(`❌ Failed to create default variant for ${itemName}: ${variantError}`);
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { items, overwrite = false } = req.body;

    if (!items || !Array.isArray(items)) {
      return res.status(400).json({ error: 'Items array is required' });
    }

    console.log('🟨 Syncing Square catalog items to Supabase...', { itemCount: items.length, overwrite });

    let syncedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    for (const item of items) {
      try {
        // Check if item already exists
        const existingResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items?select=id&square_item_id=eq.${item.id}&limit=1`, {
          method: 'GET',
          headers: {
            'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
            'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
            'Content-Type': 'application/json',
          },
        });

        const existingData = await existingResponse.json();
        const itemExists = existingData && existingData.length > 0;

        if (itemExists && !overwrite) {
          console.log(`⏭️ Skipping existing item: ${item.name}`);
          skippedCount++;
          continue;
        }

        // Prepare item data for our items table
        const itemData = {
          name: item.name || 'Unnamed Item',
          vietnamese_name: null, // Can be filled in later
          category: item.category || 'uncategorized',
          description: item.description || null,
          preparation_time: null, // Can be filled in later
          image_url: null, // Can be filled in later
          square_item_id: item.id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        let response;
        let itemId;

        if (itemExists && overwrite) {
          // Update existing item
          console.log(`🔄 Updating existing item: ${item.name}`);
          response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items?square_item_id=eq.${item.id}`, {
            method: 'PATCH',
            headers: {
              'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
              'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
              'Content-Type': 'application/json',
              'Prefer': 'return=representation',
            },
            body: JSON.stringify(itemData),
          });

          if (response.ok) {
            const updatedItem = await response.json();
            itemId = updatedItem[0]?.id || existingData[0]?.id;
          }
        } else {
          // Insert new item
          console.log(`➕ Adding new item: ${item.name}`);
          response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items`, {
            method: 'POST',
            headers: {
              'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
              'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
              'Content-Type': 'application/json',
              'Prefer': 'return=representation',
            },
            body: JSON.stringify(itemData),
          });

          if (response.ok) {
            const createdItem = await response.json();
            itemId = createdItem[0]?.id;
          }
        }

        if (response.ok) {
          if (itemId) {
            // If overwriting existing item, delete existing variants first
            if (itemExists && overwrite) {
              console.log(`🗑️ Deleting existing variants for ${item.name}`);
              const deleteVariantsResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/item_variants?item_id=eq.${itemId}`, {
                method: 'DELETE',
                headers: {
                  'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
                  'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
                  'Content-Type': 'application/json',
                },
              });

              if (!deleteVariantsResponse.ok) {
                console.warn(`⚠️ Warning: Could not delete existing variants for ${item.name}`);
              }
            }

            // Handle variations - create local variants matching Square variations exactly
            if (item.variations && item.variations.length > 0) {
              console.log(`🍽️ Syncing ${item.variations.length} variations for ${item.name}`);

              // Filter out deleted variations
              const validVariations = item.variations.filter((v: any) => !v.is_deleted);

              if (validVariations.length > 0) {
                // Sort by creation date to get oldest first
                const sortedVariations = validVariations.sort((a: any, b: any) =>
                  new Date(a.created_at || a.updated_at).getTime() - new Date(b.created_at || b.updated_at).getTime()
                );

                console.log(`🎯 Creating ${sortedVariations.length} local variants matching Square variations`);

                // Create a local variant for each Square variation
                for (let i = 0; i < sortedVariations.length; i++) {
                  const variation = sortedVariations[i];
                  const isOldest = i === 0;

                  const variantData = {
                    item_id: itemId,
                    square_variation_id: variation.id, // Store Square variation ID for mapping
                    name: variation.item_variation_data?.name || `Variation ${i + 1}`,
                    selling_price: (variation.item_variation_data?.price_money?.amount || 0) / 100,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                  };

                  console.log(`🎯 Creating variant ${i + 1}/${sortedVariations.length}: ${variantData.name} ${isOldest ? '(oldest)' : ''} - $${variantData.selling_price}`);

                  const variantResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/item_variants`, {
                    method: 'POST',
                    headers: {
                      'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
                      'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
                      'Content-Type': 'application/json',
                      'Prefer': 'return=representation',
                    },
                    body: JSON.stringify(variantData),
                  });

                  if (variantResponse.ok) {
                    console.log(`✅ Created variant: ${variantData.name}`);
                  } else {
                    const variantError = await variantResponse.text();
                    console.error(`❌ Failed to create variant ${variantData.name}: ${variantError}`);
                  }
                }
              } else {
                console.log(`⚠️ No valid variations found for ${item.name}, creating default variant`);
                // Create default variant if no valid variations
                await createDefaultVariant(itemId, item.name);
              }
            } else {
              console.log(`📝 No variations found for ${item.name}, creating default variant`);
              // Create default variant if no variations exist
              await createDefaultVariant(itemId, item.name);
            }
          }

          syncedCount++;
          console.log(`✅ Successfully synced: ${item.name}`);
        } else {
          const errorText = await response.text();
          const errorMsg = `Failed to sync ${item.name}: ${response.status} - ${errorText}`;
          console.error(`❌ ${errorMsg}`);
          errors.push(errorMsg);
          errorCount++;
        }
      } catch (itemError: any) {
        const errorMsg = `Error syncing ${item.name}: ${itemError.message}`;
        console.error(`❌ ${errorMsg}`);
        errors.push(errorMsg);
        errorCount++;
      }
    }

    const results = {
      total: items.length,
      created: syncedCount, // For simplicity, assume all synced items are created
      updated: 0, // We'll track this properly later if needed
      synced: syncedCount,
      skipped: skippedCount,
      errors: errorCount,
      errorDetails: errors
    };

    console.log('✅ Square catalog sync completed:', results);
    res.status(200).json(results);
  } catch (error: any) {
    console.error('❌ Error in Square catalog sync:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
