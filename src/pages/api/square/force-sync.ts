import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔄 Starting force sync with Square catalog...');

    // Get access token from Supabase
    const configResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/square_config?select=access_token&limit=1`, {
      method: 'GET',
      headers: {
        'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    const configData = await configResponse.json();
    const accessToken = configData[0]?.access_token;

    if (!accessToken) {
      return res.status(401).json({ error: 'No Square access token found' });
    }

    // Fetch Square catalog
    console.log('📥 Fetching Square catalog...');
    const catalogResponse = await fetch('https://connect.squareup.com/v2/catalog/list?types=ITEM', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18',
      },
    });

    if (!catalogResponse.ok) {
      const errorText = await catalogResponse.text();
      return res.status(catalogResponse.status).json({
        error: 'Square catalog API error',
        details: errorText
      });
    }

    const catalogData = await catalogResponse.json();
    const squareItems = (catalogData.objects || [])
      .filter((obj: any) => obj.type === 'ITEM' && !obj.is_deleted)
      .map((item: any) => ({
        id: item.id,
        name: item.item_data?.name || 'Unnamed Item',
        description: item.item_data?.description || '',
        category: item.item_data?.category_id || 'uncategorized',
        variations: item.item_data?.variations || [],
        updated_at: item.updated_at,
        version: item.version
      }));

    console.log(`📊 Found ${squareItems.length} items in Square catalog`);

    // Get existing items from database
    console.log('📥 Fetching existing items from database...');
    const dbItemsResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items?select=id,name,square_item_id`, {
      method: 'GET',
      headers: {
        'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    const dbItems = await dbItemsResponse.json();
    console.log(`📊 Found ${dbItems.length} items in database`);

    const results = {
      squareItemsFound: squareItems.length,
      databaseItemsFound: dbItems.length,
      updated: 0,
      created: 0,
      matched: 0,
      unmatched: 0,
      errors: [] as string[],
      details: [] as any[]
    };

    // Process each Square item
    for (const squareItem of squareItems) {
      try {
        // Try to find matching database item by name (fuzzy matching)
        const matchingDbItem = dbItems.find((dbItem: any) => {
          const squareName = squareItem.name.toLowerCase().trim();
          const dbName = dbItem.name.toLowerCase().trim();
          
          // Exact match
          if (squareName === dbName) return true;
          
          // Partial match (contains)
          if (squareName.includes(dbName) || dbName.includes(squareName)) return true;
          
          // Remove common words and try again
          const cleanSquare = squareName.replace(/\b(pcs|pc|piece|pieces|the|a|an)\b/g, '').trim();
          const cleanDb = dbName.replace(/\b(pcs|pc|piece|pieces|the|a|an)\b/g, '').trim();
          
          return cleanSquare === cleanDb || cleanSquare.includes(cleanDb) || cleanDb.includes(cleanSquare);
        });

        if (matchingDbItem) {
          // Update existing item with Square ID
          console.log(`🔄 Updating "${matchingDbItem.name}" with Square ID: ${squareItem.id}`);
          
          const updateResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items?id=eq.${matchingDbItem.id}`, {
            method: 'PATCH',
            headers: {
              'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
              'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
              'Content-Type': 'application/json',
              'Prefer': 'return=representation',
            },
            body: JSON.stringify({
              square_item_id: squareItem.id,
              updated_at: new Date().toISOString()
            }),
          });

          if (updateResponse.ok) {
            results.updated++;
            results.matched++;
            results.details.push({
              action: 'updated',
              dbItem: matchingDbItem.name,
              squareItem: squareItem.name,
              squareId: squareItem.id,
              matched: true
            });
          } else {
            const error = `Failed to update ${matchingDbItem.name}`;
            results.errors.push(error);
            console.error('❌', error);
          }
        } else {
          // Create new item from Square
          console.log(`➕ Creating new item from Square: "${squareItem.name}"`);
          
          const createResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items`, {
            method: 'POST',
            headers: {
              'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
              'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
              'Content-Type': 'application/json',
              'Prefer': 'return=representation',
            },
            body: JSON.stringify({
              name: squareItem.name,
              description: squareItem.description,
              category: 'main', // Default category
              square_item_id: squareItem.id,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }),
          });

          if (createResponse.ok) {
            results.created++;
            results.details.push({
              action: 'created',
              squareItem: squareItem.name,
              squareId: squareItem.id,
              matched: false
            });
          } else {
            const error = `Failed to create ${squareItem.name}`;
            results.errors.push(error);
            console.error('❌', error);
          }
        }
      } catch (error: any) {
        const errorMsg = `Error processing ${squareItem.name}: ${error.message}`;
        results.errors.push(errorMsg);
        console.error('❌', errorMsg);
      }
    }

    // Check for unmatched database items
    dbItems.forEach((dbItem: any) => {
      if (!dbItem.square_item_id) {
        results.unmatched++;
        results.details.push({
          action: 'unmatched',
          dbItem: dbItem.name,
          squareId: null,
          matched: false
        });
      }
    });

    console.log('✅ Force sync completed');
    console.log(`📊 Results: ${results.updated} updated, ${results.created} created, ${results.unmatched} unmatched`);

    res.status(200).json({
      success: true,
      message: 'Force sync completed',
      results
    });

  } catch (error: any) {
    console.error('❌ Error in force sync:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
