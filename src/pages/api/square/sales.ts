import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { startDate, endDate, catalogItemId, locationId, page = 1, limit = 50 } = req.body;

    if (!startDate) {
      return res.status(400).json({ error: 'startDate is required' });
    }

    console.log('🟨 Processing Square sales request...', { startDate, endDate, catalogItemId, locationId });

    // First, get the Square access token from Supabase
    const configResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/square_config?select=*&limit=1`, {
      method: 'GET',
      headers: {
        'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    if (!configResponse.ok) {
      return res.status(401).json({ error: 'Square API not configured' });
    }

    const configData = await configResponse.json();
    if (!configData || configData.length === 0 || !configData[0].access_token) {
      return res.status(401).json({ error: 'Square access token not found' });
    }

    const accessToken = configData[0].access_token;

    // Validate and format dates
    const startDateObj = new Date(startDate);
    const endDateObj = endDate ? new Date(endDate) : new Date(startDate);

    if (isNaN(startDateObj.getTime())) {
      return res.status(400).json({ error: 'Invalid start date format' });
    }

    if (isNaN(endDateObj.getTime())) {
      return res.status(400).json({ error: 'Invalid end date format' });
    }

    // Format dates for Square API (RFC 3339 format)
    const startAtISO = startDateObj.toISOString();
    const endAtISO = new Date(endDateObj.getTime() + 24 * 60 * 60 * 1000 - 1).toISOString(); // End of day

    // Calculate pagination
    const pageSize = Math.min(Math.max(1, parseInt(limit.toString())), 200); // Max 200 per page
    const pageNumber = Math.max(1, parseInt(page.toString()));
    const cursor = pageNumber > 1 ? `page_${pageNumber}` : undefined;

    // Build the request body for orders search
    const requestBody: any = {
      query: {
        filter: {
          date_time_filter: {
            created_at: {
              start_at: startAtISO,
              end_at: endAtISO
            }
          }
        }
      },
      limit: pageSize
    };

    // Add cursor for pagination (Square uses cursor-based pagination)
    if (cursor && pageNumber > 1) {
      requestBody.cursor = cursor;
    }

    // Only add location_ids if provided (some Square accounts don't require it)
    if (locationId) {
      requestBody.location_ids = [locationId];
    }

    console.log('🔍 Making Square orders search API request...');
    console.log('🔍 Request body:', JSON.stringify(requestBody, null, 2));

    // Make request to Square API
    const response = await fetch('https://connect.squareup.com/v2/orders/search', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18',
      },
      body: JSON.stringify(requestBody),
    });

    console.log('🔍 Square orders response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Square orders API error:', response.status, errorText);

      let errorMessage = `Square API error: ${response.status}`;
      let errorDetails = errorText;

      try {
        const errorJson = JSON.parse(errorText);
        if (errorJson.errors && errorJson.errors.length > 0) {
          errorMessage = errorJson.errors[0].detail || errorMessage;
          errorDetails = JSON.stringify(errorJson.errors, null, 2);
        }
      } catch (parseError) {
        // Keep original error text if JSON parsing fails
      }

      return res.status(response.status).json({
        error: errorMessage,
        details: errorDetails,
        squareErrorCode: response.status
      });
    }

    const data = await response.json();
    console.log('✅ Square orders fetched successfully');
    console.log('🔍 Orders count:', data.orders?.length || 0);

    // Process orders to extract sales data
    const salesData = {
      orders: data.orders || [],
      totalOrders: data.orders?.length || 0,
      totalRevenue: 0,
      itemSales: {} as Record<string, { name: string; quantity: number; revenue: number }>
    };

    // Calculate totals and item-specific sales
    (data.orders || []).forEach((order: any) => {
      const orderTotal = order.total_money?.amount || 0;
      salesData.totalRevenue += orderTotal;

      // Process line items
      (order.line_items || []).forEach((lineItem: any) => {
        const catalogObjectId = lineItem.catalog_object_id;
        const quantity = parseInt(lineItem.quantity) || 0;
        const revenue = lineItem.total_money?.amount || 0;
        const name = lineItem.name || 'Unknown Item';

        // Filter by catalog item if specified
        if (catalogItemId && catalogObjectId !== catalogItemId) {
          return;
        }

        if (catalogObjectId) {
          if (!salesData.itemSales[catalogObjectId]) {
            salesData.itemSales[catalogObjectId] = {
              name: name,
              quantity: 0,
              revenue: 0
            };
          }
          salesData.itemSales[catalogObjectId].quantity += quantity;
          salesData.itemSales[catalogObjectId].revenue += revenue;
        }
      });
    });

    // Convert revenue from cents to dollars
    salesData.totalRevenue = salesData.totalRevenue / 100;
    Object.values(salesData.itemSales).forEach(item => {
      item.revenue = item.revenue / 100;
    });

    // Format data for the sales page (expects catalogSales array)
    const catalogSales = Object.entries(salesData.itemSales).map(([catalogItemId, item]) => ({
      catalogItemId,
      mealName: item.name,
      vietnameseName: null, // Can be filled in later if we have this data
      quantity: item.quantity,
      revenue: item.revenue,
      orders: 1, // We don't track individual orders per item, so default to 1
    }));

    const formattedResponse = {
      ...salesData,
      catalogSales,
      // Keep the original itemSales for backward compatibility
      itemSales: salesData.itemSales,
      pagination: {
        page: pageNumber,
        limit: pageSize,
        hasMore: data.cursor ? true : false,
        cursor: data.cursor
      },
      dateRange: {
        startDate,
        endDate: endDate || startDate
      }
    };

    res.status(200).json(formattedResponse);
  } catch (error: any) {
    console.error('❌ Error in Square sales proxy:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
