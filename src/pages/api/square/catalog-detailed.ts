import { NextApiRequest, NextApiResponse } from 'next';

/**
 * Fetch detailed Square catalog data using the catalog/list endpoint
 * This gets complete item and variation details for mapping to local items
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Fetching detailed Square catalog data...');

    // Get access token from Supabase (same approach as catalog.ts)
    console.log('🟨 Getting Square access token from Supabase...');
    const configResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/square_config?select=access_token&limit=1`, {
      method: 'GET',
      headers: {
        'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    if (!configResponse.ok) {
      console.error('❌ Failed to get Square config from Supabase:', configResponse.status);
      return res.status(500).json({ error: 'Failed to get Square configuration' });
    }

    const configData = await configResponse.json();
    if (!configData || configData.length === 0) {
      console.error('❌ No Square configuration found in Supabase');
      return res.status(404).json({ error: 'Square not configured. Please add your access token first.' });
    }

    const accessToken = configData[0].access_token;
    console.log('✅ Square access token retrieved from Supabase');

    // Fetch catalog data from Square
    const response = await fetch('https://connect.squareup.com/v2/catalog/list', {
      method: 'GET',
      headers: {
        'Square-Version': '2025-05-21',
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('❌ Square API error:', response.status, errorData);
      return res.status(response.status).json({
        error: 'Square API error',
        details: errorData
      });
    }

    const catalogData = await response.json();
    console.log(`✅ Fetched ${catalogData.objects?.length || 0} catalog objects from Square`);

    // Debug: Log the types of objects we're getting
    const objectTypes = {};
    if (catalogData.objects) {
      catalogData.objects.forEach(obj => {
        objectTypes[obj.type] = (objectTypes[obj.type] || 0) + 1;
      });
    }
    console.log('📊 Object types in catalog:', objectTypes);

    // Debug: Log first few objects to see structure
    if (catalogData.objects && catalogData.objects.length > 0) {
      console.log('🔍 First 3 catalog objects:', catalogData.objects.slice(0, 3));
    }

    // Filter and organize the data
    const items = [];
    const variations = [];
    const categories = [];

    if (catalogData.objects) {
      for (const obj of catalogData.objects) {
        // Debug: Log each object type and whether it's deleted
        console.log(`Processing ${obj.type} - ID: ${obj.id} - Deleted: ${obj.is_deleted}`);

        // Skip deleted objects
        if (obj.is_deleted) {
          console.log(`⏭️ Skipping deleted ${obj.type}: ${obj.id}`);
          continue;
        }

        switch (obj.type) {
          case 'ITEM':
            // Only include items that have item_data
            if (obj.item_data) {
              console.log(`✅ Adding ITEM: ${obj.item_data.name || 'Unnamed'} - ID: ${obj.id}`);
              items.push({
                id: obj.id,
                type: obj.type,
                updated_at: obj.updated_at,
                created_at: obj.created_at,
                version: obj.version,
                is_deleted: obj.is_deleted,
                present_at_all_locations: obj.present_at_all_locations,
                item_data: obj.item_data
              });
            } else {
              console.log(`⚠️ ITEM without item_data: ${obj.id}`);
            }
            break;
          case 'ITEM_VARIATION':
            // Only include variations that have item_variation_data
            if (obj.item_variation_data) {
              console.log(`✅ Adding VARIATION: ${obj.item_variation_data.name || 'Unnamed'} - ID: ${obj.id}`);
              variations.push({
                id: obj.id,
                type: obj.type,
                updated_at: obj.updated_at,
                created_at: obj.created_at,
                version: obj.version,
                is_deleted: obj.is_deleted,
                present_at_all_locations: obj.present_at_all_locations,
                item_variation_data: obj.item_variation_data
              });
            } else {
              console.log(`⚠️ VARIATION without item_variation_data: ${obj.id}`);
            }
            break;
          case 'CATEGORY':
            // Only include categories that have category_data
            if (obj.category_data) {
              console.log(`✅ Adding CATEGORY: ${obj.category_data.name || 'Unnamed'} - ID: ${obj.id}`);
              categories.push({
                id: obj.id,
                type: obj.type,
                updated_at: obj.updated_at,
                created_at: obj.created_at,
                version: obj.version,
                is_deleted: obj.is_deleted,
                present_at_all_locations: obj.present_at_all_locations,
                category_data: obj.category_data
              });
            } else {
              console.log(`⚠️ CATEGORY without category_data: ${obj.id}`);
            }
            break;
          default:
            console.log(`🔍 Other object type: ${obj.type} - ID: ${obj.id}`);
            break;
        }
      }
    }

    // Group variations with their parent items
    const itemsWithVariations = items.map(item => {
      // First, try to find separate variation objects
      let itemVariations = variations.filter(variation =>
        variation.item_variation_data?.item_id === item.id && !variation.is_deleted
      );

      // If no separate variations found, check if item has embedded variations
      if (itemVariations.length === 0 && item.item_data?.variations) {
        console.log(`🔍 Item ${item.item_data.name} has embedded variations:`, item.item_data.variations.length);
        // Convert embedded variations to the same format as separate variations
        itemVariations = item.item_data.variations.map((embeddedVar: any) => ({
          id: embeddedVar.id,
          type: 'ITEM_VARIATION',
          item_variation_data: embeddedVar.item_variation_data || embeddedVar,
          is_deleted: embeddedVar.is_deleted || false
        })).filter((v: any) => !v.is_deleted);
      }

      return {
        ...item,
        variations: itemVariations
      };
    });

    // Filter out deleted items (but allow items without variations)
    const activeItems = itemsWithVariations.filter(item =>
      !item.is_deleted
    );

    // Also create a separate list of items with variations for comparison
    const itemsWithVariationsOnly = itemsWithVariations.filter(item =>
      !item.is_deleted && item.variations.length > 0
    );

    console.log(`📊 Final processed catalog data:
    - Total objects: ${catalogData.objects?.length || 0}
    - Items found: ${items.length}
    - Variations found: ${variations.length}
    - Categories found: ${categories.length}
    - All active items: ${activeItems.length}
    - Items with variations only: ${itemsWithVariationsOnly.length}`);

    // Debug: Log some sample items if we have them
    if (activeItems.length > 0) {
      console.log('🔍 Sample active items:', activeItems.slice(0, 3).map(item => ({
        id: item.id,
        name: item.item_data?.name,
        variationCount: item.variations?.length || 0,
        hasVariations: item.variations?.length > 0
      })));
    } else {
      console.log('⚠️ No active items found! This might be the issue.');
    }

    // Debug: Check if items have embedded variations in item_data
    if (items.length > 0 && variations.length === 0) {
      console.log('🔍 Checking if items have embedded variations...');
      const sampleItem = items[0];
      if (sampleItem.item_data?.variations) {
        console.log('✅ Found embedded variations in item_data:', sampleItem.item_data.variations.length);
      } else {
        console.log('⚠️ No embedded variations found in item_data');
      }
    }

    res.status(200).json({
      success: true,
      summary: {
        totalObjects: catalogData.objects?.length || 0,
        items: items.length,
        variations: variations.length,
        categories: categories.length,
        activeItems: activeItems.length,
        itemsWithVariationsOnly: itemsWithVariationsOnly.length
      },
      data: {
        items: activeItems, // All active items (with or without variations)
        itemsWithVariations: itemsWithVariationsOnly, // Only items that have variations
        categories: categories.filter(cat => !cat.is_deleted),
        allVariations: variations
      },
      debug: {
        objectTypes,
        sampleObjects: catalogData.objects?.slice(0, 3) || []
      },
      rawData: catalogData // Include raw data for debugging
    });

  } catch (error: any) {
    console.error('❌ Error fetching Square catalog:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
