import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get access token from Supabase
    console.log('🟨 Getting Square access token from Supabase...');
    const configResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/square_config?select=access_token&limit=1`, {
      method: 'GET',
      headers: {
        'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    if (!configResponse.ok) {
      console.error('❌ Failed to get Square config from Supabase:', configResponse.status);
      return res.status(500).json({ error: 'Failed to get Square configuration' });
    }

    const configData = await configResponse.json();
    if (!configData || configData.length === 0 || !configData[0].access_token) {
      console.error('❌ No Square access token found in Supabase');
      return res.status(401).json({ error: 'Square API not configured. Please save your access token first.' });
    }

    const accessToken = configData[0].access_token;
    console.log('✅ Square access token retrieved from Supabase');

    console.log('🟨 Proxying Square locations request...');

    // Make request to Square API
    const response = await fetch('https://connect.squareup.com/v2/locations', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18',
      },
    });

    console.log('🔍 Square locations response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Square locations API error:', response.status, errorText);
      return res.status(response.status).json({
        error: `Square API error: ${response.status}`,
        details: errorText
      });
    }

    const data = await response.json();
    console.log('✅ Square locations fetched successfully');
    console.log('🔍 Locations count:', data.locations?.length || 0);

    res.status(200).json({
      locations: data.locations || []
    });
  } catch (error: any) {
    console.error('❌ Error in Square locations proxy:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
