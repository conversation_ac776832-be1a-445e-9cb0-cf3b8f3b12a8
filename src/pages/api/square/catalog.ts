import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { page = '1', limit = '50', cursor } = req.query;
    // Get access token from Supabase
    console.log('🟨 Getting Square access token from Supabase...');
    const configResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/square_config?select=access_token&limit=1`, {
      method: 'GET',
      headers: {
        'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    if (!configResponse.ok) {
      console.error('❌ Failed to get Square config from Supabase:', configResponse.status);
      return res.status(500).json({ error: 'Failed to get Square configuration' });
    }

    const configData = await configResponse.json();
    if (!configData || configData.length === 0 || !configData[0].access_token) {
      console.error('❌ No Square access token found in Supabase');
      return res.status(401).json({ error: 'Square API not configured. Please save your access token first.' });
    }

    const accessToken = configData[0].access_token;
    console.log('✅ Square access token retrieved from Supabase');

    console.log('🟨 Proxying Square catalog request...');

    // Calculate pagination
    const pageSize = Math.min(Math.max(1, parseInt(limit.toString())), 1000); // Max 1000 per page (Square limit)
    const pageNumber = Math.max(1, parseInt(page.toString()));

    // Build Square API URL with pagination
    let squareUrl = `https://connect.squareup.com/v2/catalog/list?limit=${pageSize}`;
    if (cursor) {
      squareUrl += `&cursor=${cursor}`;
    }

    console.log('🔍 Square catalog URL:', squareUrl);

    // Make request to Square API
    const response = await fetch(squareUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18',
      },
    });

    console.log('🔍 Square catalog response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Square catalog API error:', response.status, errorText);
      return res.status(response.status).json({
        error: `Square API error: ${response.status}`,
        details: errorText
      });
    }

    const data = await response.json();
    console.log('✅ Square catalog fetched successfully');
    console.log('🔍 Catalog items count:', data.objects?.length || 0);

    // Filter for ITEM type objects and format for our UI
    const items = (data.objects || [])
      .filter((obj: any) => obj.type === 'ITEM')
      .map((item: any) => ({
        id: item.id,
        name: item.item_data?.name || 'Unnamed Item',
        description: item.item_data?.description || '',
        category: item.item_data?.category_id || 'uncategorized',
        variations: item.item_data?.variations || [],
        type: item.type,
        updated_at: item.updated_at,
        version: item.version,
        is_deleted: item.is_deleted || false,
        present_at_all_locations: item.present_at_all_locations,
        present_at_location_ids: item.present_at_location_ids || []
      }));

    res.status(200).json({
      objects: data.objects || [],
      items: items,
      cursor: data.cursor,
      pagination: {
        page: pageNumber,
        limit: pageSize,
        hasMore: !!data.cursor,
        cursor: data.cursor
      }
    });
  } catch (error: any) {
    console.error('❌ Error in Square catalog proxy:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
