import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { itemId, startDate, endDate, days = 7 } = req.query;

    if (!itemId) {
      return res.status(400).json({ error: 'itemId parameter is required' });
    }

    console.log('🟨 Received itemId:', itemId);

    // Check if this is a Square catalog item ID or local item ID
    let squareItemId = itemId as string;

    // If it looks like a UUID (local item ID), try to get the Square item ID
    if (typeof itemId === 'string' && itemId.includes('-')) {
      console.log('🔍 Looks like a local item ID, fetching Square item ID...');

      const itemResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items?select=square_item_id&id=eq.${itemId}&limit=1`, {
        method: 'GET',
        headers: {
          'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (itemResponse.ok) {
        const itemData = await itemResponse.json();
        if (itemData && itemData.length > 0 && itemData[0].square_item_id) {
          squareItemId = itemData[0].square_item_id;
          console.log('✅ Found Square item ID:', squareItemId);
        } else {
          return res.status(400).json({
            error: 'Selected item is not linked to Square catalog',
            details: 'Please select an item that has been synced from Square catalog'
          });
        }
      } else {
        return res.status(400).json({
          error: 'Item not found in database',
          details: 'The selected item does not exist'
        });
      }
    }

    console.log('🎯 Using Square item ID for search:', squareItemId);

    // Get access token from Supabase
    console.log('🟨 Getting Square access token from Supabase...');
    const configResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/square_config?select=access_token&limit=1`, {
      method: 'GET',
      headers: {
        'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    if (!configResponse.ok) {
      console.error('❌ Failed to get Square config from Supabase:', configResponse.status);
      return res.status(500).json({ error: 'Failed to get Square configuration' });
    }

    const configData = await configResponse.json();
    if (!configData || configData.length === 0 || !configData[0].access_token) {
      console.error('❌ No Square access token found in Supabase');
      return res.status(401).json({ error: 'Square API not configured. Please save your access token first.' });
    }

    const accessToken = configData[0].access_token;
    console.log('✅ Square access token retrieved from Supabase');

    // Calculate date range
    let searchStartDate: Date;
    let searchEndDate: Date;

    if (startDate && endDate) {
      searchStartDate = new Date(startDate as string);
      searchEndDate = new Date(endDate as string);
    } else {
      searchEndDate = new Date();
      searchStartDate = new Date();
      searchStartDate.setDate(searchStartDate.getDate() - parseInt(days as string));
    }

    const startDateStr = searchStartDate.toISOString().split('T')[0];
    const endDateStr = searchEndDate.toISOString().split('T')[0];

    console.log('🟨 Fetching orders for item:', itemId, 'from', startDateStr, 'to', endDateStr);

    // First, get locations from Square API since location_ids is required
    console.log('🟨 Fetching Square locations...');
    const locationsResponse = await fetch('https://connect.squareup.com/v2/locations', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18',
      },
    });

    if (!locationsResponse.ok) {
      const errorText = await locationsResponse.text();
      console.error('❌ Square locations API error:', locationsResponse.status, errorText);
      return res.status(locationsResponse.status).json({
        error: `Square locations API error: ${locationsResponse.status}`,
        details: errorText
      });
    }

    const locationsData = await locationsResponse.json();
    const locationIds = (locationsData.locations || []).map((loc: any) => loc.id);
    console.log('✅ Square locations fetched:', locationIds.length, 'locations');

    if (locationIds.length === 0) {
      return res.status(400).json({
        error: 'No Square locations found',
        details: 'Cannot search orders without at least one location'
      });
    }

    // Build the request body for orders search
    const requestBody: any = {
      location_ids: locationIds,
      query: {
        filter: {
          date_time_filter: {
            created_at: {
              start_at: `${startDateStr}T00:00:00Z`,
              end_at: `${endDateStr}T23:59:59Z`
            }
          }
        }
      },
      limit: 1000 // Higher limit for comprehensive data
    };

    // Make request to Square API
    const response = await fetch('https://connect.squareup.com/v2/orders/search', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18',
      },
      body: JSON.stringify(requestBody),
    });

    console.log('🔍 Square orders response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Square orders API error:', response.status, errorText);
      return res.status(response.status).json({
        error: `Square API error: ${response.status}`,
        details: errorText
      });
    }

    const data = await response.json();
    console.log('✅ Square orders fetched successfully');
    console.log('🔍 Total orders found:', data.orders?.length || 0);

    // Filter orders that contain the specific item
    const itemOrders: any[] = [];
    let totalQuantity = 0;
    let totalRevenue = 0;

    (data.orders || []).forEach((order: any) => {
      const orderItems: any[] = [];
      let orderHasItem = false;

      (order.line_items || []).forEach((lineItem: any) => {
        const catalogObjectId = lineItem.catalog_object_id;
        const quantity = parseInt(lineItem.quantity) || 0;
        const revenue = lineItem.total_money?.amount || 0;
        const name = lineItem.name || 'Unknown Item';

        // Check if this line item matches our target item
        if (catalogObjectId === squareItemId) {
          orderHasItem = true;
          totalQuantity += quantity;
          totalRevenue += revenue;

          orderItems.push({
            catalogObjectId,
            name,
            quantity,
            revenue: revenue / 100, // Convert from cents to dollars
            unitPrice: revenue / quantity / 100 // Price per unit in dollars
          });
        }
      });

      // If this order contains our target item, add it to results
      if (orderHasItem) {
        itemOrders.push({
          orderId: order.id,
          orderDate: order.created_at,
          orderState: order.state,
          locationId: order.location_id,
          totalMoney: order.total_money?.amount ? order.total_money.amount / 100 : 0,
          items: orderItems,
          customerInfo: {
            customerId: order.fulfillments?.[0]?.pickup_details?.recipient?.customer_id || null,
            note: order.fulfillments?.[0]?.pickup_details?.note || null
          }
        });
      }
    });

    console.log(`🍽️ Found ${itemOrders.length} orders containing item ${squareItemId}`);
    console.log(`📊 Total quantity: ${totalQuantity}, Total revenue: $${(totalRevenue / 100).toFixed(2)}`);

    const result = {
      itemId: squareItemId,
      dateRange: {
        startDate: startDateStr,
        endDate: endDateStr,
        days: Math.ceil((searchEndDate.getTime() - searchStartDate.getTime()) / (1000 * 60 * 60 * 24))
      },
      summary: {
        totalOrders: itemOrders.length,
        totalQuantity,
        totalRevenue: totalRevenue / 100,
        averageOrderValue: itemOrders.length > 0 ? (totalRevenue / 100) / itemOrders.length : 0,
        averageQuantityPerOrder: itemOrders.length > 0 ? totalQuantity / itemOrders.length : 0
      },
      orders: itemOrders.sort((a, b) => new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime())
    };

    res.status(200).json(result);
  } catch (error: any) {
    console.error('❌ Error in item orders API:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
