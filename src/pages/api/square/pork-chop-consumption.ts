import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get access token from Supabase
    console.log('🟨 Getting Square access token from Supabase...');
    const configResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/square_config?select=access_token&limit=1`, {
      method: 'GET',
      headers: {
        'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    if (!configResponse.ok) {
      console.error('❌ Failed to get Square config from Supabase:', configResponse.status);
      return res.status(500).json({ error: 'Failed to get Square configuration' });
    }

    const configData = await configResponse.json();
    if (!configData || configData.length === 0 || !configData[0].access_token) {
      console.error('❌ No Square access token found in Supabase');
      return res.status(401).json({ error: 'Square API not configured. Please save your access token first.' });
    }

    const accessToken = configData[0].access_token;
    console.log('✅ Square access token retrieved from Supabase');

    // Calculate date range for last 3 days
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 3);

    const startDateStr = startDate.toISOString().split('T')[0];
    const endDateStr = endDate.toISOString().split('T')[0];

    console.log('🟨 Fetching orders from last 3 days:', { startDateStr, endDateStr });

    // First, get locations from Square API since location_ids is required
    console.log('🟨 Fetching Square locations...');
    const locationsResponse = await fetch('https://connect.squareup.com/v2/locations', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18',
      },
    });

    if (!locationsResponse.ok) {
      const errorText = await locationsResponse.text();
      console.error('❌ Square locations API error:', locationsResponse.status, errorText);
      return res.status(locationsResponse.status).json({
        error: `Square locations API error: ${locationsResponse.status}`,
        details: errorText
      });
    }

    const locationsData = await locationsResponse.json();
    const locationIds = (locationsData.locations || []).map((loc: any) => loc.id);
    console.log('✅ Square locations fetched:', locationIds.length, 'locations');

    if (locationIds.length === 0) {
      return res.status(400).json({
        error: 'No Square locations found',
        details: 'Cannot search orders without at least one location'
      });
    }

    // Build the request body for orders search with location IDs
    const requestBody: any = {
      location_ids: locationIds, // Use all available locations
      query: {
        filter: {
          date_time_filter: {
            created_at: {
              start_at: `${startDateStr}T00:00:00Z`,
              end_at: `${endDateStr}T23:59:59Z`
            }
          }
        }
      },
      limit: 500 // Use same limit as working orders API
    };

    // Make request to Square API
    const response = await fetch('https://connect.squareup.com/v2/orders/search', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18',
      },
      body: JSON.stringify(requestBody),
    });

    console.log('🔍 Square orders response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Square orders API error:', response.status, errorText);
      console.error('❌ Request body was:', JSON.stringify(requestBody, null, 2));
      console.error('❌ Request headers were:', {
        'Authorization': `Bearer ${accessToken.substring(0, 10)}...`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18',
      });

      // Try to parse error details
      let errorDetails = errorText;
      try {
        const errorJson = JSON.parse(errorText);
        errorDetails = errorJson.errors ? JSON.stringify(errorJson.errors, null, 2) : errorText;
      } catch (e) {
        // Keep original error text if not JSON
      }

      return res.status(response.status).json({
        error: `Square API error: ${response.status}`,
        details: errorDetails,
        requestBody: requestBody
      });
    }

    const data = await response.json();
    console.log('✅ Square orders fetched successfully');
    console.log('🔍 Total orders found:', data.orders?.length || 0);

    // Define the pork chop recipe - ingredients consumed per serving
    const porkChopRecipe = {
      'rice': { quantity: 1, unit: 'bowl', description: 'Bowl of rice' },
      'pork_chop': { quantity: 1, unit: 'serve', description: 'Serve pork chop' },
      'cucumber': { quantity: 2, unit: 'slices', description: 'Slices of cucumber' },
      'pickle_veggie': { quantity: 5, unit: 'slices', description: 'Slices of pickle veggie' },
      'sauce_container': { quantity: 1, unit: 'container', description: '35ml sauces container' },
      'fish_sauce': { quantity: 30, unit: 'ml', description: 'Pork chop fish sauce' },
      'banana_leaf': { quantity: 1, unit: 'sheet', description: 'Sheet banana leaf' },
      't3_plate': { quantity: 1, unit: 'plate', description: 'T3 plate' },
      'chopsticks': { quantity: 1, unit: 'pair', description: 'Pair of chopsticks' },
      'fork': { quantity: 1, unit: 'piece', description: 'Fork' },
      'soviet': { quantity: 2, unit: 'pieces', description: 'Soviet (napkins?)' }
    };

    // Process orders to find pork chop orders
    let totalPorkChopServings = 0;
    const porkChopOrders: any[] = [];
    const totalConsumption: Record<string, { quantity: number; unit: string; description: string }> = {};

    // Initialize total consumption
    Object.entries(porkChopRecipe).forEach(([key, recipe]) => {
      totalConsumption[key] = {
        quantity: 0,
        unit: recipe.unit,
        description: recipe.description
      };
    });

    (data.orders || []).forEach((order: any) => {
      // Process line items to find pork chop dishes
      (order.line_items || []).forEach((lineItem: any) => {
        const itemName = lineItem.name || '';
        const quantity = parseInt(lineItem.quantity) || 0;

        // Check if this is a pork chop dish (flexible matching)
        const isPorkChop = itemName.toLowerCase().includes('pork chop') ||
                          itemName.toLowerCase().includes('sườn heo') ||
                          itemName.toLowerCase().includes('charcoal pork');

        if (isPorkChop) {
          console.log(`🍖 Found pork chop order: "${itemName}" x${quantity}`);
          totalPorkChopServings += quantity;

          porkChopOrders.push({
            orderId: order.id,
            orderDate: order.created_at,
            itemName: itemName,
            quantity: quantity,
            totalMoney: lineItem.total_money?.amount || 0
          });

          // Calculate consumption for this order
          Object.entries(porkChopRecipe).forEach(([key, recipe]) => {
            totalConsumption[key].quantity += recipe.quantity * quantity;
          });
        }
      });
    });

    console.log(`🍖 Total pork chop servings found: ${totalPorkChopServings}`);

    const result = {
      dateRange: {
        startDate: startDateStr,
        endDate: endDateStr,
        days: 3
      },
      summary: {
        totalOrders: data.orders?.length || 0,
        totalPorkChopServings,
        porkChopOrdersCount: porkChopOrders.length
      },
      porkChopOrders,
      totalConsumption,
      recipe: porkChopRecipe
    };

    res.status(200).json(result);
  } catch (error: any) {
    console.error('❌ Error in pork chop consumption API:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
