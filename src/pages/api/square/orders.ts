import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get access token from request headers
    const accessToken = req.headers.authorization?.replace('Bearer ', '');

    if (!accessToken) {
      return res.status(401).json({ error: 'Access token required' });
    }

    const { startDate, endDate, catalogItemId, locationId } = req.body;

    if (!startDate) {
      return res.status(400).json({ error: 'startDate is required' });
    }

    console.log('🟨 Proxying Square orders search request...', { startDate, endDate, catalogItemId, locationId });

    // Build the request body for orders search
    const requestBody: any = {
      location_ids: locationId ? [locationId] : undefined,
      query: {
        filter: {
          date_time_filter: {
            created_at: {
              start_at: `${startDate}T00:00:00Z`,
              end_at: endDate ? `${endDate}T23:59:59Z` : `${startDate}T23:59:59Z`
            }
          }
        }
      },
      limit: 500
    };

    // Make request to Square API
    const response = await fetch('https://connect.squareup.com/v2/orders/search', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18',
      },
      body: JSON.stringify(requestBody),
    });

    console.log('🔍 Square orders response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Square orders API error:', response.status, errorText);
      return res.status(response.status).json({
        error: `Square API error: ${response.status}`,
        details: errorText
      });
    }

    const data = await response.json();
    console.log('✅ Square orders fetched successfully');
    console.log('🔍 Orders count:', data.orders?.length || 0);

    // Process orders to extract sales data
    const salesData = {
      orders: data.orders || [],
      totalOrders: data.orders?.length || 0,
      totalRevenue: 0,
      itemSales: {} as Record<string, { name: string; quantity: number; revenue: number }>
    };

    // Calculate totals and item-specific sales
    (data.orders || []).forEach((order: any) => {
      const orderTotal = order.total_money?.amount || 0;
      salesData.totalRevenue += orderTotal;

      // Process line items
      (order.line_items || []).forEach((lineItem: any) => {
        const catalogObjectId = lineItem.catalog_object_id;
        const quantity = parseInt(lineItem.quantity) || 0;
        const revenue = lineItem.total_money?.amount || 0;
        const name = lineItem.name || 'Unknown Item';

        // Filter by catalog item if specified
        if (catalogItemId && catalogObjectId !== catalogItemId) {
          return;
        }

        if (catalogObjectId) {
          if (!salesData.itemSales[catalogObjectId]) {
            salesData.itemSales[catalogObjectId] = {
              name: name,
              quantity: 0,
              revenue: 0
            };
          }
          salesData.itemSales[catalogObjectId].quantity += quantity;
          salesData.itemSales[catalogObjectId].revenue += revenue;
        }
      });
    });

    // Convert revenue from cents to dollars
    salesData.totalRevenue = salesData.totalRevenue / 100;
    Object.values(salesData.itemSales).forEach(item => {
      item.revenue = item.revenue / 100;
    });

    // Format data for the sales page (expects catalogSales array)
    const catalogSales = Object.entries(salesData.itemSales).map(([catalogItemId, item]) => ({
      catalogItemId,
      mealName: item.name,
      vietnameseName: null, // Can be filled in later if we have this data
      quantity: item.quantity,
      revenue: item.revenue,
      orders: 1, // We don't track individual orders per item, so default to 1
    }));

    const formattedResponse = {
      ...salesData,
      catalogSales,
      // Keep the original itemSales for backward compatibility
      itemSales: salesData.itemSales
    };

    res.status(200).json(formattedResponse);
  } catch (error: any) {
    console.error('❌ Error in Square orders proxy:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
