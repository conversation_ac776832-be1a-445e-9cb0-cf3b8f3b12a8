import { NextApiRequest, NextApiResponse } from 'next';

/**
 * Clear all inventory items
 * WARNING: This is a destructive operation that deletes ALL inventory items
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'DELETE') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('⚠️ Starting clear all inventory items operation...');

    // Delete all inventory items from Supabase
    const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/inventory_items`, {
      method: 'DELETE',
      headers: {
        'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
        'Prefer': 'return=representation',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Error clearing inventory items:', response.status, errorText);
      return res.status(response.status).json({
        error: `Failed to clear inventory items: ${response.status}`,
        details: errorText
      });
    }

    const deletedItems = await response.json();
    console.log(`✅ Successfully cleared ${deletedItems.length} inventory items`);

    res.status(200).json({
      success: true,
      message: `Successfully cleared ${deletedItems.length} inventory items`,
      deletedCount: deletedItems.length
    });

  } catch (error: any) {
    console.error('❌ Error in clear all inventory:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
