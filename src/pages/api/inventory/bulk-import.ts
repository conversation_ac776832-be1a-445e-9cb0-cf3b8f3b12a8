import { NextApiRequest, NextApiResponse } from 'next';

/**
 * Bulk import inventory items for Vietnamese restaurant
 * This endpoint creates multiple inventory items at once
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🚀 Starting bulk import of inventory items...');
    
    const { items } = req.body;
    
    if (!items || !Array.isArray(items)) {
      return res.status(400).json({ error: 'Items array is required' });
    }

    console.log(`📦 Importing ${items.length} inventory items...`);

    // Validate each item has required fields
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (!item.name || !item.unit || !item.storage) {
        return res.status(400).json({ 
          error: `Item ${i + 1} is missing required fields (name, unit, storage)`,
          item: item
        });
      }
    }

    // Prepare items for bulk insert
    const itemsToInsert = items.map((item: any) => ({
      name: item.name,
      vietnamese_name: item.vietnamese_name || null,
      category: item.category || 'uncategorized',
      unit: item.unit,
      quantity: item.quantity || 0,
      cost_per_unit: item.cost_per_unit || 0,
      min_threshold: item.min_threshold || 5,
      storage: item.storage,
      supplier_id: item.supplier_id || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));

    console.log('📋 Prepared items for insert:', itemsToInsert.length);

    // Insert items into Supabase
    const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/inventory_items`, {
      method: 'POST',
      headers: {
        'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
        'Prefer': 'return=representation',
      },
      body: JSON.stringify(itemsToInsert),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Error inserting inventory items:', response.status, errorText);
      return res.status(response.status).json({
        error: `Failed to insert inventory items: ${response.status}`,
        details: errorText
      });
    }

    const insertedItems = await response.json();
    console.log(`✅ Successfully inserted ${insertedItems.length} inventory items`);

    res.status(201).json({
      success: true,
      message: `Successfully imported ${insertedItems.length} inventory items`,
      items: insertedItems
    });

  } catch (error: any) {
    console.error('❌ Error in bulk import:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
