import { NextApiRequest, NextApiResponse } from 'next';

/**
 * Inventory transactions API
 * Handles adding and retrieving inventory transactions
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method === 'GET') {
      // Get all transactions with optional filters
      const { inventoryItemId, type, startDate, endDate, page = '1', limit = '50' } = req.query;

      console.log('📊 Fetching transactions from Supabase...', { inventoryItemId, type, startDate, endDate });

      // Calculate pagination
      const pageSize = Math.min(Math.max(1, parseInt(limit.toString())), 100);
      const pageNumber = Math.max(1, parseInt(page.toString()));
      const offset = (pageNumber - 1) * pageSize;

      let url = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/transactions?select=*&order=date.desc&limit=${pageSize}&offset=${offset}`;

      // Apply filters
      const filters = [];
      if (inventoryItemId) filters.push(`inventory_item_id=eq.${inventoryItemId}`);
      if (type) filters.push(`type=eq.${type}`);
      if (startDate) filters.push(`date=gte.${startDate}`);
      if (endDate) filters.push(`date=lte.${endDate}`);

      if (filters.length > 0) {
        url += `&${filters.join('&')}`;
      }

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Error fetching transactions:', response.status, errorText);
        return res.status(response.status).json({
          error: `Failed to fetch transactions: ${response.status}`,
          details: errorText
        });
      }

      const data = await response.json();
      console.log('✅ Transactions fetched successfully:', data.length);

      res.status(200).json({
        data: data || [],
        pagination: {
          page: pageNumber,
          limit: pageSize,
          total: data.length
        }
      });

    } else if (req.method === 'POST') {
      // Create new transaction and update inventory
      const transaction = req.body;

      console.log('📊 Creating new transaction:', transaction);

      // Validate required fields
      if (!transaction.inventoryItemId || !transaction.type || transaction.quantity === undefined || transaction.quantity === null) {
        console.error('❌ Missing required fields:', {
          inventoryItemId: transaction.inventoryItemId,
          type: transaction.type,
          quantity: transaction.quantity
        });
        return res.status(400).json({
          error: 'Missing required fields: inventoryItemId, type, quantity',
          received: {
            inventoryItemId: transaction.inventoryItemId,
            type: transaction.type,
            quantity: transaction.quantity
          }
        });
      }

      // Validate quantity is a positive number
      if (typeof transaction.quantity !== 'number' || transaction.quantity <= 0) {
        console.error('❌ Invalid quantity:', transaction.quantity);
        return res.status(400).json({
          error: 'Quantity must be a positive number',
          received: transaction.quantity
        });
      }

      // Map camelCase to snake_case for Supabase
      const supabaseTransaction = {
        inventory_item_id: transaction.inventoryItemId,
        type: transaction.type,
        quantity: transaction.quantity,
        cost: transaction.cost || 0,
        notes: transaction.notes || null,
        date: transaction.date || new Date().toISOString()
      };

      // Insert transaction
      const transactionResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/transactions`, {
        method: 'POST',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=representation',
        },
        body: JSON.stringify(supabaseTransaction),
      });

      if (!transactionResponse.ok) {
        const errorText = await transactionResponse.text();
        console.error('❌ Error creating transaction:', transactionResponse.status, errorText);
        return res.status(transactionResponse.status).json({
          error: `Failed to create transaction: ${transactionResponse.status}`,
          details: errorText
        });
      }

      const transactionData = await transactionResponse.json();
      const createdTransaction = Array.isArray(transactionData) ? transactionData[0] : transactionData;

      // Update inventory quantity based on transaction type
      let quantityChange = 0;
      if (transaction.type === 'purchase' || transaction.type === 'adjustment_in') {
        quantityChange = transaction.quantity; // Add to inventory
      } else if (transaction.type === 'sale' || transaction.type === 'usage' || transaction.type === 'adjustment_out') {
        quantityChange = -transaction.quantity; // Remove from inventory
      }

      if (quantityChange !== 0) {
        // Get current inventory item
        const inventoryResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/inventory_items?select=quantity&id=eq.${transaction.inventoryItemId}`, {
          method: 'GET',
          headers: {
            'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
            'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
            'Content-Type': 'application/json',
          },
        });

        if (inventoryResponse.ok) {
          const inventoryData = await inventoryResponse.json();
          if (inventoryData.length > 0) {
            const currentQuantity = inventoryData[0].quantity || 0;
            const newQuantity = Math.max(0, currentQuantity + quantityChange); // Don't allow negative quantities

            // Update inventory quantity
            const updateResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/inventory_items?id=eq.${transaction.inventoryItemId}`, {
              method: 'PATCH',
              headers: {
                'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
                'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
                'Content-Type': 'application/json',
                'Prefer': 'return=representation',
              },
              body: JSON.stringify({
                quantity: newQuantity,
                last_restock_date: transaction.type === 'purchase' ? new Date().toISOString() : undefined,
                updated_at: new Date().toISOString()
              }),
            });

            if (!updateResponse.ok) {
              console.error('❌ Warning: Failed to update inventory quantity');
            } else {
              console.log(`✅ Updated inventory quantity: ${currentQuantity} → ${newQuantity}`);
            }
          }
        }
      }

      // Map response back to camelCase
      const mappedTransaction = {
        id: createdTransaction.id,
        inventoryItemId: createdTransaction.inventory_item_id,
        type: createdTransaction.type,
        quantity: createdTransaction.quantity,
        cost: createdTransaction.cost,
        notes: createdTransaction.notes,
        date: createdTransaction.date,
        createdAt: createdTransaction.created_at
      };

      console.log('✅ Transaction created successfully:', mappedTransaction.id);
      res.status(201).json({
        success: true,
        transaction: mappedTransaction
      });

    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error: any) {
    console.error('❌ Error in transactions API:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
