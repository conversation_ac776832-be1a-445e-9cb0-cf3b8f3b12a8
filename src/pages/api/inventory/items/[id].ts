import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query;

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Invalid item ID' });
  }

  try {
    if (req.method === 'GET') {
      // Get inventory item by ID
      console.log('📦 Fetching inventory item by ID:', id);

      const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/inventory_items?select=*&id=eq.${id}`, {
        method: 'GET',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Error fetching inventory item:', response.status, errorText);
        return res.status(response.status).json({
          error: `Failed to fetch inventory item: ${response.status}`,
          details: errorText
        });
      }

      const data = await response.json();
      if (data.length === 0) {
        return res.status(404).json({ error: 'Inventory item not found' });
      }

      // Map the response to camelCase
      const item = data[0];
      const mappedItem = {
        id: item.id,
        name: item.name,
        vietnameseName: item.vietnamese_name,
        quantity: item.quantity,
        unit: item.unit,
        minThreshold: item.min_threshold,
        costPerUnit: item.cost_per_unit,
        supplierId: item.supplier_id,
        lastRestockDate: item.last_restock_date,
        category: item.category,
        storage: item.storage,
        createdAt: item.created_at,
        updatedAt: item.updated_at
      };

      console.log('✅ Inventory item fetched successfully:', mappedItem.name);
      res.status(200).json(mappedItem);

    } else if (req.method === 'PATCH') {
      // Update inventory item
      const updates = req.body;

      console.log('📦 Updating inventory item:', id, 'with data:', updates);

      // Map camelCase fields to snake_case for Supabase
      const supabaseUpdates: any = {
        updated_at: new Date().toISOString()
      };

      // Only include fields that are provided
      if (updates.name !== undefined) supabaseUpdates.name = updates.name;
      if (updates.vietnameseName !== undefined) supabaseUpdates.vietnamese_name = updates.vietnameseName;
      if (updates.quantity !== undefined) supabaseUpdates.quantity = updates.quantity;
      if (updates.unit !== undefined) supabaseUpdates.unit = updates.unit;
      if (updates.minThreshold !== undefined) supabaseUpdates.min_threshold = updates.minThreshold;
      if (updates.costPerUnit !== undefined) supabaseUpdates.cost_per_unit = updates.costPerUnit;
      if (updates.supplierId !== undefined) supabaseUpdates.supplier_id = updates.supplierId;
      if (updates.lastRestockDate !== undefined) supabaseUpdates.last_restock_date = updates.lastRestockDate;
      if (updates.category !== undefined) supabaseUpdates.category = updates.category;
      if (updates.storage !== undefined) supabaseUpdates.storage = updates.storage;

      console.log('📦 Mapped updates for Supabase:', supabaseUpdates);

      const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/inventory_items?id=eq.${id}`, {
        method: 'PATCH',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=representation',
        },
        body: JSON.stringify(supabaseUpdates),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Error updating inventory item:', response.status, errorText);
        return res.status(response.status).json({
          error: `Failed to update inventory item: ${response.status}`,
          details: errorText
        });
      }

      const data = await response.json();
      if (data.length === 0) {
        return res.status(404).json({ error: 'Inventory item not found' });
      }

      // Map the response back to camelCase
      const updatedItem = data[0];
      const mappedItem = {
        id: updatedItem.id,
        name: updatedItem.name,
        vietnameseName: updatedItem.vietnamese_name,
        quantity: updatedItem.quantity,
        unit: updatedItem.unit,
        minThreshold: updatedItem.min_threshold,
        costPerUnit: updatedItem.cost_per_unit,
        supplierId: updatedItem.supplier_id,
        lastRestockDate: updatedItem.last_restock_date,
        category: updatedItem.category,
        storage: updatedItem.storage,
        createdAt: updatedItem.created_at,
        updatedAt: updatedItem.updated_at
      };

      console.log('✅ Inventory item updated successfully:', mappedItem.name);
      res.status(200).json(mappedItem);

    } else if (req.method === 'DELETE') {
      // Delete inventory item
      console.log('📦 Deleting inventory item:', id);

      const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/inventory_items?id=eq.${id}`, {
        method: 'DELETE',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Error deleting inventory item:', response.status, errorText);
        return res.status(response.status).json({
          error: `Failed to delete inventory item: ${response.status}`,
          details: errorText
        });
      }

      console.log('✅ Inventory item deleted successfully');
      res.status(200).json({ success: true });

    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error: any) {
    console.error('❌ Error in inventory item API:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
