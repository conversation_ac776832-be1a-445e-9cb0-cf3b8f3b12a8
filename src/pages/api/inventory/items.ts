import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method === 'GET') {
      // Get all inventory items
      const { category, search, belowThreshold, supplierId, page = '1', limit = '20' } = req.query;

      console.log('📦 Fetching inventory items from Supabase...', { category, search, belowThreshold, supplierId, page, limit });

      // Calculate pagination
      const pageSize = Math.min(Math.max(1, parseInt(limit.toString())), 100); // Max 100 per page
      const pageNumber = Math.max(1, parseInt(page.toString()));
      const offset = (pageNumber - 1) * pageSize;

      let url = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/inventory_items?select=*&order=name&limit=${pageSize}&offset=${offset}`;

      // Apply filters
      const filters = [];
      if (category) filters.push(`category=eq.${category}`);
      if (supplierId) filters.push(`supplier_id=eq.${supplierId}`);
      if (belowThreshold === 'true') filters.push(`quantity=lt.min_threshold`);
      if (search) filters.push(`or=(name.ilike.*${search}*,vietnamese_name.ilike.*${search}*)`);

      if (filters.length > 0) {
        url += `&${filters.join('&')}`;
      }

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Error fetching inventory items:', response.status, errorText);
        return res.status(response.status).json({
          error: `Failed to fetch inventory items: ${response.status}`,
          details: errorText
        });
      }

      const data = await response.json();
      console.log('🔍 Raw inventory data from Supabase:', data?.length || 0, 'items');

      // Ensure data is an array and map field names from snake_case to camelCase
      const rawItems = Array.isArray(data) ? data : [];
      const inventoryItems = rawItems.map((item: any) => ({
        id: item.id,
        name: item.name,
        vietnameseName: item.vietnamese_name,
        quantity: item.quantity,
        unit: item.unit,
        minThreshold: item.min_threshold,
        costPerUnit: item.cost_per_unit,
        supplierId: item.supplier_id,
        lastRestockDate: item.last_restock_date,
        category: item.category,
        storage: item.storage,
        createdAt: item.created_at,
        updatedAt: item.updated_at
      }));

      // Get total count for pagination (separate request)
      let countUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/inventory_items?select=count`;
      if (filters && filters.length > 0) {
        countUrl += `&${filters.join('&')}`;
      }

      const countResponse = await fetch(countUrl, {
        method: 'GET',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'count=exact',
        },
      });

      const totalCount = countResponse.headers.get('content-range')?.split('/')[1] || '0';
      const total = parseInt(totalCount);
      const totalPages = Math.ceil(total / pageSize);

      console.log('✅ Inventory items fetched successfully:', inventoryItems.length, 'of', total);

      res.status(200).json({
        data: inventoryItems,
        pagination: {
          page: pageNumber,
          limit: pageSize,
          total,
          totalPages,
          hasMore: pageNumber < totalPages,
          hasPrevious: pageNumber > 1
        }
      });

    } else if (req.method === 'POST') {
      // Create new inventory item
      const newItem = req.body;

      console.log('📦 Creating new inventory item:', newItem.name);
      console.log('📦 Raw item data:', newItem);

      // Map camelCase fields to snake_case for Supabase with proper null handling
      const supabaseItem = {
        name: newItem.name,
        vietnamese_name: newItem.vietnameseName || null,
        quantity: newItem.quantity,
        unit: newItem.unit,
        min_threshold: newItem.minThreshold || 0,
        cost_per_unit: newItem.costPerUnit || 0,
        supplier_id: newItem.supplierId && newItem.supplierId.trim() !== '' ? newItem.supplierId : null,
        last_restock_date: newItem.lastRestockDate || null,
        category: newItem.category || null,
        storage: newItem.storage || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('📦 Prepared Supabase item data:', supabaseItem);

      const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/inventory_items`, {
        method: 'POST',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=representation',
        },
        body: JSON.stringify(supabaseItem),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Error creating inventory item:', response.status, errorText);
        return res.status(response.status).json({
          error: `Failed to create inventory item: ${response.status}`,
          details: errorText
        });
      }

      const data = await response.json();
      const createdItem = Array.isArray(data) ? data[0] : data;

      // Map the response back to camelCase
      const mappedItem = {
        id: createdItem.id,
        name: createdItem.name,
        vietnameseName: createdItem.vietnamese_name,
        quantity: createdItem.quantity,
        unit: createdItem.unit,
        minThreshold: createdItem.min_threshold,
        costPerUnit: createdItem.cost_per_unit,
        supplierId: createdItem.supplier_id,
        lastRestockDate: createdItem.last_restock_date,
        category: createdItem.category,
        storage: createdItem.storage,
        createdAt: createdItem.created_at,
        updatedAt: createdItem.updated_at
      };

      console.log('✅ Inventory item created successfully:', mappedItem.name);
      res.status(201).json(mappedItem);

    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error: any) {
    console.error('❌ Error in inventory items API:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
