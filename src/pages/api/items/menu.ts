import { NextApiRequest, NextApiResponse } from 'next';

// Function to transform Square category IDs to readable names
function transformCategory(category: string | null): string {
  if (!category) return 'Uncategorized';

  // If it looks like a Square category ID (long alphanumeric string), transform it
  if (category.length > 20 && /^[A-Z0-9]+$/.test(category)) {
    // Common Square category mappings - you can expand this based on your actual categories
    const categoryMappings: { [key: string]: string } = {
      'QMK6JDT6WGXQFN5SYIGOMOH3': 'Main Dishes',
      // Add more mappings as needed
    };

    return categoryMappings[category] || 'Other';
  }

  // If it's already a readable name, return as is
  return category;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method === 'GET') {
      // Get all menu items
      const { category, search, page = '1', limit = '20' } = req.query;

      console.log('🍽️ Fetching menu items from Supabase...', { category, search, page, limit });

      // Calculate pagination
      const pageSize = Math.min(Math.max(1, parseInt(limit.toString())), 100); // Max 100 per page
      const pageNumber = Math.max(1, parseInt(page.toString()));
      const offset = (pageNumber - 1) * pageSize;

      let url = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items?select=*,item_variants(*)&order=name&limit=${pageSize}&offset=${offset}`;

      // Apply filters
      const filters = [];
      if (category) filters.push(`category=eq.${category}`);
      if (search) filters.push(`or=(name.ilike.*${search}*,vietnamese_name.ilike.*${search}*)`);

      if (filters.length > 0) {
        url += `&${filters.join('&')}`;
      }

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Error fetching menu items:', response.status, errorText);
        return res.status(response.status).json({
          error: `Failed to fetch menu items: ${response.status}`,
          details: errorText
        });
      }

      const data = await response.json();
      console.log('🔍 Raw menu items data from Supabase:', data?.length || 0, 'items');

      // Ensure data is an array and transform the data
      const rawItems = Array.isArray(data) ? data : [];

      // Transform the data to match frontend expectations
      const menuItems = rawItems.map((item: any) => ({
        id: item.id,
        name: item.name,
        vietnameseName: item.vietnamese_name,
        category: transformCategory(item.category),
        description: item.description,
        preparationTime: item.preparation_time,
        image: item.image_url,
        squareItemId: item.square_item_id,
        // Transform variants from database format to frontend format
        variants: (item.item_variants || []).map((variant: any) => ({
          id: variant.id,
          type: variant.type,
          name: variant.name,
          sellingPrice: variant.selling_price,
          additionalIngredients: [], // Will be populated separately if needed
          nonInventoryItems: [] // Will be populated separately if needed
        })),
        // Base ingredients will be populated separately if needed
        baseIngredients: [],
        createdAt: item.created_at,
        updatedAt: item.updated_at
      }));

      // Get total count for pagination (separate request)
      let countUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items?select=count`;
      if (filters && filters.length > 0) {
        countUrl += `&${filters.join('&')}`;
      }

      const countResponse = await fetch(countUrl, {
        method: 'GET',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'count=exact',
        },
      });

      const totalCount = countResponse.headers.get('content-range')?.split('/')[1] || '0';
      const total = parseInt(totalCount);
      const totalPages = Math.ceil(total / pageSize);

      console.log('✅ Menu items fetched successfully:', menuItems.length, 'of', total);

      res.status(200).json({
        data: menuItems,
        pagination: {
          page: pageNumber,
          limit: pageSize,
          total,
          totalPages,
          hasMore: pageNumber < totalPages,
          hasPrevious: pageNumber > 1
        }
      });

    } else if (req.method === 'POST') {
      // Create new menu item
      const itemData = req.body;

      console.log('🍽️ Creating new menu item:', itemData.name);
      console.log('🍽️ Item data payload:', JSON.stringify(itemData, null, 2));

      // Separate main item fields from variants and ingredients
      const { variants, baseIngredients, ...mainItemData } = itemData;

      // Create main item (excluding variants and ingredients)
      const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items`, {
        method: 'POST',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=representation',
        },
        body: JSON.stringify({
          ...mainItemData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Error creating menu item:', response.status, errorText);
        return res.status(response.status).json({
          error: `Failed to create menu item: ${response.status}`,
          details: errorText
        });
      }

      const data = await response.json();
      const createdItem = data[0];

      if (!createdItem) {
        return res.status(500).json({ error: 'Failed to create menu item - no data returned' });
      }

      console.log('✅ Menu item created successfully:', createdItem.name);

      // Handle variants if provided
      if (variants && Array.isArray(variants) && variants.length > 0) {
        console.log('🍽️ Creating variants for item:', createdItem.id);

        const variantData = variants.map((variant: any) => ({
          item_id: createdItem.id,
          name: variant.name,
          type: variant.type,
          selling_price: variant.sellingPrice,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }));

        const insertVariantsResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/item_variants`, {
          method: 'POST',
          headers: {
            'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
            'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
            'Content-Type': 'application/json',
            'Prefer': 'return=representation',
          },
          body: JSON.stringify(variantData),
        });

        if (!insertVariantsResponse.ok) {
          const errorText = await insertVariantsResponse.text();
          console.error('❌ Error creating variants:', insertVariantsResponse.status, errorText);
          // Don't fail the whole operation, just warn
          console.warn('⚠️ Item created but variants failed to save');
        } else {
          console.log('✅ Variants created successfully');

          // Now handle ingredients for each variant
          const insertedVariants = await insertVariantsResponse.json();
          console.log('🍽️ Inserted variants:', insertedVariants);

          // Handle base ingredients first (save with variant_id = null for global ingredients)
          if (baseIngredients && Array.isArray(baseIngredients) && baseIngredients.length > 0) {
            console.log('🍽️ Creating base ingredients:', baseIngredients);

            const baseIngredientData = baseIngredients.map((ingredient: any) => ({
              item_id: createdItem.id,
              variant_id: null, // Base ingredients are not tied to specific variants
              inventory_item_id: ingredient.inventoryItemId,
              quantity_per_serving: Number(ingredient.quantityPerServing || 0),
              is_base_ingredient: true,
              created_at: new Date().toISOString()
            }));

            const insertBaseIngredientsResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/item_ingredients`, {
              method: 'POST',
              headers: {
                'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
                'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
                'Content-Type': 'application/json',
                'Prefer': 'return=representation',
              },
              body: JSON.stringify(baseIngredientData),
            });

            if (!insertBaseIngredientsResponse.ok) {
              const errorText = await insertBaseIngredientsResponse.text();
              console.error('❌ Error inserting base ingredients:', errorText);
            } else {
              console.log('✅ Base ingredients inserted successfully');
            }
          }

          // Insert ingredients for each variant
          for (let i = 0; i < variants.length; i++) {
            const variant = variants[i];
            const insertedVariant = insertedVariants[i];

            if (variant.additionalIngredients && variant.additionalIngredients.length > 0) {
              console.log(`🍽️ Creating ingredients for variant ${insertedVariant.id}:`, variant.additionalIngredients);

              const ingredientData = variant.additionalIngredients.map((ingredient: any) => ({
                item_id: createdItem.id,
                variant_id: insertedVariant.id,
                inventory_item_id: ingredient.inventoryItemId,
                quantity_per_serving: Number(ingredient.quantityPerServing || 0),
                is_base_ingredient: false,
                created_at: new Date().toISOString()
              }));

              const insertIngredientsResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/item_ingredients`, {
                method: 'POST',
                headers: {
                  'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
                  'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
                  'Content-Type': 'application/json',
                  'Prefer': 'return=representation',
                },
                body: JSON.stringify(ingredientData),
              });

              if (!insertIngredientsResponse.ok) {
                const errorText = await insertIngredientsResponse.text();
                console.error(`❌ Error inserting ingredients for variant ${insertedVariant.id}:`, errorText);
              } else {
                console.log(`✅ Ingredients inserted for variant ${insertedVariant.id}`);
              }
            }
          }
        }
      }

      // Return the created item with variants and ingredients
      const finalResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items?select=*,item_variants(*),item_ingredients(*)&id=eq.${createdItem.id}`, {
        method: 'GET',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (finalResponse.ok) {
        const finalData = await finalResponse.json();
        if (finalData.length > 0) {
          res.status(201).json(finalData[0]);
        } else {
          res.status(201).json(createdItem);
        }
      } else {
        res.status(201).json(createdItem);
      }

    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error: any) {
    console.error('❌ Error in menu items API:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
