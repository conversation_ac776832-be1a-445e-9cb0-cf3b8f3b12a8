import { NextApiRequest, NextApiResponse } from 'next';

// Function to transform Square category IDs to readable names
function transformCategory(category: string | null): string {
  if (!category) return 'Uncategorized';

  // If it looks like a Square category ID (long alphanumeric string), transform it
  if (category.length > 20 && /^[A-Z0-9]+$/.test(category)) {
    // Common Square category mappings - you can expand this based on your actual categories
    const categoryMappings: { [key: string]: string } = {
      'QMK6JDT6WGXQFN5SYIGOMOH3': 'Main Dishes',
      // Add more mappings as needed
    };

    return categoryMappings[category] || 'Other';
  }

  // If it's already a readable name, return as is
  return category;
}

// Function to transform database item to frontend format
function transformItem(item: any) {
  return {
    id: item.id,
    name: item.name,
    vietnameseName: item.vietnamese_name,
    category: transformCategory(item.category),
    description: item.description,
    preparationTime: item.preparation_time,
    image: item.image_url,
    squareItemId: item.square_item_id,
    // Transform variants from database format to frontend format
    variants: (item.item_variants || []).map((variant: any) => ({
      id: variant.id,
      type: variant.type,
      name: variant.name,
      sellingPrice: variant.selling_price,
      additionalIngredients: [], // Will be populated separately if needed
      nonInventoryItems: [] // Will be populated separately if needed
    })),
    // Base ingredients with inventory item names
    baseIngredients: (item.item_ingredients || [])
      .filter((ing: any) => ing.variant_id === null)
      .map((ing: any) => ({
        inventoryItemId: ing.inventory_item_id,
        inventoryItemName: ing.inventory_items?.name || 'Unknown Item',
        quantityPerServing: ing.quantity_per_serving
      })),
    createdAt: item.created_at,
    updatedAt: item.updated_at
  };
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query;

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Invalid item ID' });
  }

  try {
    if (req.method === 'GET') {
      // Get menu item by ID
      console.log('🍽️ Fetching menu item by ID:', id);

      const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items?select=*,item_variants(*),item_ingredients(*,inventory_items(id,name))&id=eq.${id}`, {
        method: 'GET',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Error fetching menu item:', response.status, errorText);
        return res.status(response.status).json({
          error: `Failed to fetch menu item: ${response.status}`,
          details: errorText
        });
      }

      const data = await response.json();
      if (data.length === 0) {
        return res.status(404).json({ error: 'Menu item not found' });
      }

      // Transform the data to match frontend expectations
      const transformedItem = transformItem(data[0]);

      console.log('✅ Menu item fetched successfully:', transformedItem.name);
      res.status(200).json(transformedItem);

    } else if (req.method === 'PATCH') {
      // Update menu item
      const updates = req.body;

      console.log('🍽️ Updating menu item:', id);
      console.log('🍽️ Update payload:', JSON.stringify(updates, null, 2));

      // Separate main item fields from variants and ingredients
      const { variants, baseIngredients, ...itemUpdates } = updates;

      // Update main item (excluding variants and ingredients)
      const itemResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items?id=eq.${id}`, {
        method: 'PATCH',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=representation',
        },
        body: JSON.stringify({
          ...itemUpdates,
          updated_at: new Date().toISOString()
        }),
      });

      if (!itemResponse.ok) {
        const errorText = await itemResponse.text();
        console.error('❌ Error updating menu item:', itemResponse.status, errorText);
        return res.status(itemResponse.status).json({
          error: `Failed to update menu item: ${itemResponse.status}`,
          details: errorText
        });
      }

      const itemData = await itemResponse.json();
      if (itemData.length === 0) {
        return res.status(404).json({ error: 'Menu item not found' });
      }

      // Handle base ingredients even if no variants (for manual items)
      if (baseIngredients && Array.isArray(baseIngredients) && baseIngredients.length > 0) {
        console.log('🍽️ Creating base ingredients (no variants):', baseIngredients);

        // Delete existing base ingredients
        const deleteBaseIngredientsResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/item_ingredients?item_id=eq.${id}&variant_id=is.null`, {
          method: 'DELETE',
          headers: {
            'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
            'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
            'Content-Type': 'application/json',
          },
        });

        if (!deleteBaseIngredientsResponse.ok) {
          console.warn('⚠️ Warning: Could not delete existing base ingredients');
        }

        const baseIngredientData = baseIngredients.map((ingredient: any) => ({
          item_id: id,
          variant_id: null, // Base ingredients are not tied to specific variants
          inventory_item_id: ingredient.inventoryItemId,
          quantity_per_serving: Number(ingredient.quantityPerServing || 0),
          is_base_ingredient: true,
          created_at: new Date().toISOString()
        }));

        const insertBaseIngredientsResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/item_ingredients`, {
          method: 'POST',
          headers: {
            'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
            'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
            'Content-Type': 'application/json',
            'Prefer': 'return=representation',
          },
          body: JSON.stringify(baseIngredientData),
        });

        if (!insertBaseIngredientsResponse.ok) {
          const errorText = await insertBaseIngredientsResponse.text();
          console.error('❌ Error inserting base ingredients (no variants):', errorText);
        } else {
          console.log('✅ Base ingredients inserted successfully (no variants)');
        }
      }

      // Handle variants if provided
      if (variants && Array.isArray(variants)) {
        console.log('🍽️ Updating variants for item:', id);

        // Delete existing variants (use service role for permissions)
        const deleteVariantsResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/item_variants?item_id=eq.${id}`, {
          method: 'DELETE',
          headers: {
            'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
            'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
            'Content-Type': 'application/json',
          },
        });

        if (!deleteVariantsResponse.ok) {
          console.warn('⚠️ Warning: Could not delete existing variants');
        }

        // Insert new variants
        if (variants.length > 0) {
          console.log('🍽️ Processing variants:', JSON.stringify(variants, null, 2));

          const variantData = variants.map((variant: any, index: number) => {
            console.log(`🍽️ Processing variant ${index}:`, variant);

            // Handle different possible field names for selling price
            const sellingPrice = variant.sellingPrice || variant.selling_price || variant.price || 0;

            const mappedVariant = {
              item_id: id,
              name: variant.name || `Variant ${index + 1}`,
              type: variant.type || 'dine_in',
              selling_price: Number(sellingPrice),
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };

            console.log(`🍽️ Mapped variant ${index}:`, mappedVariant);
            return mappedVariant;
          });

          console.log('🍽️ Final variant data to insert:', JSON.stringify(variantData, null, 2));

          const insertVariantsResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/item_variants`, {
            method: 'POST',
            headers: {
              'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
              'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
              'Content-Type': 'application/json',
              'Prefer': 'return=representation',
            },
            body: JSON.stringify(variantData),
          });

          if (!insertVariantsResponse.ok) {
            const errorText = await insertVariantsResponse.text();
            console.error('❌ Error inserting variants:', insertVariantsResponse.status, errorText);
            console.error('❌ Variant data that failed:', JSON.stringify(variantData, null, 2));
            return res.status(insertVariantsResponse.status).json({
              error: `Failed to update variants: ${insertVariantsResponse.status}`,
              details: errorText
            });
          } else {
            console.log('✅ Variants inserted successfully');

            // Now handle ingredients for each variant
            const insertedVariants = await insertVariantsResponse.json();
            console.log('🍽️ Inserted variants:', insertedVariants);

            // Delete existing ingredients for all variants of this item
            const deleteIngredientsResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/item_ingredients?item_id=eq.${id}`, {
              method: 'DELETE',
              headers: {
                'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
                'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
                'Content-Type': 'application/json',
              },
            });

            if (!deleteIngredientsResponse.ok) {
              console.warn('⚠️ Warning: Could not delete existing ingredients');
            }

            // Handle base ingredients (save with variant_id = null for global ingredients)
            if (baseIngredients && Array.isArray(baseIngredients) && baseIngredients.length > 0) {
              console.log('🍽️ Creating base ingredients:', baseIngredients);

              const baseIngredientData = baseIngredients.map((ingredient: any) => ({
                item_id: id,
                variant_id: null, // Base ingredients are not tied to specific variants
                inventory_item_id: ingredient.inventoryItemId,
                quantity_per_serving: Number(ingredient.quantityPerServing || 0),
                is_base_ingredient: true,
                created_at: new Date().toISOString()
              }));

              const insertBaseIngredientsResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/item_ingredients`, {
                method: 'POST',
                headers: {
                  'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
                  'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
                  'Content-Type': 'application/json',
                  'Prefer': 'return=representation',
                },
                body: JSON.stringify(baseIngredientData),
              });

              if (!insertBaseIngredientsResponse.ok) {
                const errorText = await insertBaseIngredientsResponse.text();
                console.error('❌ Error inserting base ingredients:', errorText);
              } else {
                console.log('✅ Base ingredients inserted successfully');
              }
            }

            // Insert new ingredients for each variant
            for (let i = 0; i < variants.length; i++) {
              const variant = variants[i];
              const insertedVariant = insertedVariants[i];

              if (variant.additionalIngredients && variant.additionalIngredients.length > 0) {
                console.log(`🍽️ Creating ingredients for variant ${insertedVariant.id}:`, variant.additionalIngredients);

                const ingredientData = variant.additionalIngredients.map((ingredient: any) => ({
                  item_id: id,
                  variant_id: insertedVariant.id,
                  inventory_item_id: ingredient.inventoryItemId,
                  quantity_per_serving: Number(ingredient.quantityPerServing || 0),
                  is_base_ingredient: false,
                  created_at: new Date().toISOString()
                }));

                const insertIngredientsResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/item_ingredients`, {
                  method: 'POST',
                  headers: {
                    'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
                    'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
                    'Content-Type': 'application/json',
                    'Prefer': 'return=representation',
                  },
                  body: JSON.stringify(ingredientData),
                });

                if (!insertIngredientsResponse.ok) {
                  const errorText = await insertIngredientsResponse.text();
                  console.error(`❌ Error inserting ingredients for variant ${insertedVariant.id}:`, errorText);
                } else {
                  console.log(`✅ Ingredients inserted for variant ${insertedVariant.id}`);
                }
              }
            }
          }
        }
      }

      console.log('✅ Menu item updated successfully:', itemData[0].name);

      // Return the updated item with variants and ingredients
      const finalResponse = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items?select=*,item_variants(*),item_ingredients(*,inventory_items(id,name))&id=eq.${id}`, {
        method: 'GET',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (finalResponse.ok) {
        const finalData = await finalResponse.json();
        if (finalData.length > 0) {
          const transformedItem = transformItem(finalData[0]);
          res.status(200).json(transformedItem);
        } else {
          const transformedItem = transformItem(itemData[0]);
          res.status(200).json(transformedItem);
        }
      } else {
        const transformedItem = transformItem(itemData[0]);
        res.status(200).json(transformedItem);
      }

    } else if (req.method === 'DELETE') {
      // Delete menu item
      console.log('🍽️ Deleting menu item:', id);

      const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/items?id=eq.${id}`, {
        method: 'DELETE',
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Error deleting menu item:', response.status, errorText);
        return res.status(response.status).json({
          error: `Failed to delete menu item: ${response.status}`,
          details: errorText
        });
      }

      console.log('✅ Menu item deleted successfully');
      res.status(200).json({ success: true });

    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error: any) {
    console.error('❌ Error in menu item API:', error);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message
    });
  }
}
