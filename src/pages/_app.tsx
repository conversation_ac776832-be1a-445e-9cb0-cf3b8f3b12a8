import type { AppProps } from 'next/app';
import { useEffect } from 'react';

// Custom App component to handle webpack-internal URLs
export default function App({ Component, pageProps }: AppProps) {
  // Add a global handler for webpack-internal URLs
  useEffect(() => {
    // Override fetch to handle webpack-internal URLs
    const originalFetch = window.fetch;
    window.fetch = function(input: RequestInfo | URL, init?: RequestInit) {
      // Check if the URL is webpack-internal
      if (typeof input === 'string' && input.startsWith('webpack-internal://')) {
        console.warn('Intercepted webpack-internal fetch request:', input);
        // Return a mock response for webpack-internal URLs
        return Promise.resolve(new Response(JSON.stringify({ success: true }), {
          status: 200,
          headers: {
            'Content-Type': 'application/json'
          }
        }));
      }
      
      // Otherwise, use the original fetch
      return originalFetch.call(this, input, init);
    };
    
    // Cleanup function to restore original fetch
    return () => {
      window.fetch = originalFetch;
    };
  }, []);
  
  return <Component {...pageProps} />;
}
