import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// This function can be marked `async` if using `await` inside
export function middleware(request: NextRequest) {
  // Get the URL of the request
  const url = new URL(request.url);

  // IMPORTANT: Completely bypass middleware for webpack-internal URLs and Next.js internal resources
  // This prevents issues with webpack-internal module loading
  if (url.protocol === 'webpack-internal:' ||
      url.protocol === 'webpack:' ||
      url.protocol === 'webpack-hmr:' ||
      url.pathname.includes('webpack-internal') ||
      url.pathname.includes('webpack-hmr') ||
      url.pathname.includes('webpack-dev-server') ||
      url.pathname.includes('_next/static') ||
      url.pathname.includes('_next/webpack') ||
      url.pathname.includes('__nextjs_original-stack-frame')) {
    return NextResponse.next();
  }

  // Check if the request is using a supported scheme (http or https)
  const isSecureScheme = url.protocol === 'https:' || url.protocol === 'http:';

  // Clone the response to add CORS headers
  const response = NextResponse.next();

  // Add CORS headers to all responses
  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // Add additional security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return new NextResponse(null, {
      status: 204,
      headers: response.headers,
    });
  }

  // If the scheme is not supported, redirect to https or return an error
  if (!isSecureScheme) {
    if (process.env.NODE_ENV === 'production') {
      // In production, redirect to https
      const secureUrl = new URL(request.url);
      secureUrl.protocol = 'https:';
      return NextResponse.redirect(secureUrl);
    } else if (request.headers.get('content-type')?.includes('application/json')) {
      // For API requests in development, return a helpful error
      return new NextResponse(
        JSON.stringify({
          error: 'CORS Error: Unsupported scheme',
          message: 'CORS requests must use http:// or https:// schemes',
          currentUrl: request.url
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            ...response.headers
          }
        }
      );
    }
  }

  return response;
}

// Apply this middleware to all routes except Next.js internal paths
export const config = {
  matcher: [
    // Apply to all API routes
    '/api/:path*',
    // Apply to all page routes, but exclude Next.js internal paths and webpack-internal URLs
    '/((?!_next/static|_next/image|_next/webpack|_next/webpack-hmr|webpack-hmr|webpack-internal|webpack-dev-server|__nextjs_original-stack-frame|favicon.ico).*)',
  ],
};
