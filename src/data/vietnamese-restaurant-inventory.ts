/**
 * Vietnamese Restaurant Inventory Data
 * Complete inventory items organized by storage location
 */

export const vietnameseRestaurantInventory = [
  // FRIDGE 1 - Proteins and Prepared Foods
  {
    name: "Pork chop",
    vietnamese_name: "Sườn heo",
    category: "proteins",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 8.50,
    min_threshold: 10,
    storage: "FRIDGE_1"
  },
  {
    name: "Grilled chicken",
    vietnamese_name: "Gà nướng",
    category: "proteins",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 7.00,
    min_threshold: 8,
    storage: "FRIDGE_1"
  },
  {
    name: "Grilled pork",
    vietnamese_name: "Thịt nướng",
    category: "proteins",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 9.00,
    min_threshold: 10,
    storage: "FRIDGE_1"
  },
  {
    name: "Meatballs",
    vietnamese_name: "Chả nướng",
    category: "proteins",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 1.50,
    min_threshold: 20,
    storage: "FRIDGE_1"
  },
  {
    name: "Betle leaf skewers",
    vietnamese_name: "Bò lá lốt",
    category: "proteins",
    unit: "skewers",
    quantity: 0,
    cost_per_unit: 2.50,
    min_threshold: 15,
    storage: "FRIDGE_1"
  },
  {
    name: "Spare ribs",
    vietnamese_name: "Sườn gà",
    category: "proteins",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 6.50,
    min_threshold: 8,
    storage: "FRIDGE_1"
  },
  {
    name: "Grilled pork sausage",
    vietnamese_name: "Nem nướng",
    category: "proteins",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 3.00,
    min_threshold: 12,
    storage: "FRIDGE_1"
  },
  {
    name: "Vietnamese deep fry pork rolls",
    vietnamese_name: "Nem chua rán",
    category: "proteins",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 2.80,
    min_threshold: 15,
    storage: "FRIDGE_1"
  },
  {
    name: "Fresh coconuts",
    vietnamese_name: "Nước dừa tươi",
    category: "beverages",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 4.50,
    min_threshold: 6,
    storage: "FRIDGE_1"
  },
  {
    name: "Green mango",
    vietnamese_name: "Xoài xanh",
    category: "fruits",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 8.00,
    min_threshold: 2,
    storage: "FRIDGE_1"
  },
  {
    name: "Vermicelli fish sauce",
    vietnamese_name: "Nước mắm bún",
    category: "sauces",
    unit: "ml",
    quantity: 0,
    cost_per_unit: 0.02,
    min_threshold: 500,
    storage: "FRIDGE_1"
  },
  {
    name: "Pork chop sauce",
    vietnamese_name: "Nước mắm pork chop",
    category: "sauces",
    unit: "ml",
    quantity: 0,
    cost_per_unit: 0.02,
    min_threshold: 500,
    storage: "FRIDGE_1"
  },
  {
    name: "Shrimp",
    vietnamese_name: "Tép",
    category: "seafood",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 25.00,
    min_threshold: 1,
    storage: "FRIDGE_1"
  },
  {
    name: "Fried onions",
    vietnamese_name: "Tỏi chiên",
    category: "condiments",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 12.00,
    min_threshold: 0.5,
    storage: "FRIDGE_1"
  },
  {
    name: "Fried garlic",
    vietnamese_name: "Hành chiên",
    category: "condiments",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 15.00,
    min_threshold: 0.5,
    storage: "FRIDGE_1"
  },

  // FREEZER 1 - Frozen Items
  {
    name: "Pork spring rolls",
    vietnamese_name: "Chả giò heo",
    category: "frozen_foods",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 1.80,
    min_threshold: 30,
    storage: "FREEZER_1"
  },
  {
    name: "Prawn spring rolls",
    vietnamese_name: "Chả giò tôm",
    category: "frozen_foods",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 2.20,
    min_threshold: 25,
    storage: "FREEZER_1"
  },
  {
    name: "Ice",
    vietnamese_name: "Đá",
    category: "frozen_foods",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 2.00,
    min_threshold: 10,
    storage: "FREEZER_1"
  },
  {
    name: "Frozen corn",
    vietnamese_name: "Bắp ngô",
    category: "frozen_foods",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 6.50,
    min_threshold: 2,
    storage: "FREEZER_1"
  },
  {
    name: "Banana leaf",
    vietnamese_name: "Lá chuối",
    category: "frozen_foods",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 0.50,
    min_threshold: 20,
    storage: "FREEZER_1"
  },

  // FRIDGE 2 - Fresh Vegetables and Herbs
  {
    name: "Iceberg lettuce",
    vietnamese_name: "Xà lách bắp",
    category: "vegetables",
    unit: "heads",
    quantity: 0,
    cost_per_unit: 3.50,
    min_threshold: 5,
    storage: "FRIDGE_2"
  },
  {
    name: "Vietnamese lettuce",
    vietnamese_name: "Xà lách Việt Nam",
    category: "vegetables",
    unit: "bunches",
    quantity: 0,
    cost_per_unit: 2.80,
    min_threshold: 8,
    storage: "FRIDGE_2"
  },
  {
    name: "Mints",
    vietnamese_name: "Húng lủi",
    category: "herbs",
    unit: "bunches",
    quantity: 0,
    cost_per_unit: 2.50,
    min_threshold: 6,
    storage: "FRIDGE_2"
  },
  {
    name: "Vietnamese mints",
    vietnamese_name: "Rau răm",
    category: "herbs",
    unit: "bunches",
    quantity: 0,
    cost_per_unit: 2.50,
    min_threshold: 6,
    storage: "FRIDGE_2"
  },
  {
    name: "Coriander",
    vietnamese_name: "Ngò mùi",
    category: "herbs",
    unit: "bunches",
    quantity: 0,
    cost_per_unit: 2.00,
    min_threshold: 8,
    storage: "FRIDGE_2"
  },
  {
    name: "Cucumber",
    vietnamese_name: "Dưa leo",
    category: "vegetables",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 4.50,
    min_threshold: 3,
    storage: "FRIDGE_2"
  },
  {
    name: "Perilla",
    vietnamese_name: "Tía tô",
    category: "herbs",
    unit: "bunches",
    quantity: 0,
    cost_per_unit: 3.00,
    min_threshold: 4,
    storage: "FRIDGE_2"
  },
  {
    name: "Lemongrass",
    vietnamese_name: "Xả",
    category: "herbs",
    unit: "stalks",
    quantity: 0,
    cost_per_unit: 0.50,
    min_threshold: 20,
    storage: "FRIDGE_2"
  },
  {
    name: "Ginger",
    vietnamese_name: "Ngừng",
    category: "herbs",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 12.00,
    min_threshold: 0.5,
    storage: "FRIDGE_2"
  },
  {
    name: "Spring onions",
    vietnamese_name: "Hành lá",
    category: "vegetables",
    unit: "bunches",
    quantity: 0,
    cost_per_unit: 2.50,
    min_threshold: 8,
    storage: "FRIDGE_2"
  },
  {
    name: "Pickled vegetables",
    vietnamese_name: "Đồ chua",
    category: "condiments",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 8.00,
    min_threshold: 1,
    storage: "FRIDGE_2"
  },
  {
    name: "Brown onions",
    vietnamese_name: "Hành củ",
    category: "vegetables",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 3.50,
    min_threshold: 2,
    storage: "FRIDGE_2"
  },
  {
    name: "Pork shredded skin",
    vietnamese_name: "Bì",
    category: "proteins",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 18.00,
    min_threshold: 0.5,
    storage: "FRIDGE_2"
  },
  {
    name: "Steam meatloaf",
    vietnamese_name: "Chả hấp",
    category: "proteins",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 4.50,
    min_threshold: 6,
    storage: "FRIDGE_2"
  },
  {
    name: "Chicken feet sauce",
    vietnamese_name: "Sauce chân gà",
    category: "sauces",
    unit: "ml",
    quantity: 0,
    cost_per_unit: 0.03,
    min_threshold: 300,
    storage: "FRIDGE_2"
  },
  {
    name: "Chicken feet",
    vietnamese_name: "Chân gà",
    category: "proteins",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 8.50,
    min_threshold: 1,
    storage: "FRIDGE_2"
  },
  {
    name: "Eggs",
    vietnamese_name: "Trứng",
    category: "proteins",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 0.80,
    min_threshold: 24,
    storage: "FRIDGE_2"
  },
  {
    name: "Peach bottle",
    vietnamese_name: "Đào",
    category: "fruits",
    unit: "bottles",
    quantity: 0,
    cost_per_unit: 4.50,
    min_threshold: 3,
    storage: "FRIDGE_2"
  },
  {
    name: "Orange",
    vietnamese_name: "Quả cam",
    category: "fruits",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 6.00,
    min_threshold: 2,
    storage: "FRIDGE_2"
  },

  // DRINK FRIDGE - Beverages
  {
    name: "Coke",
    vietnamese_name: "Coca Cola",
    category: "beverages",
    unit: "cans",
    quantity: 0,
    cost_per_unit: 2.50,
    min_threshold: 24,
    storage: "DRINK_FRIDGE"
  },
  {
    name: "Water",
    vietnamese_name: "Nước lọc",
    category: "beverages",
    unit: "bottles",
    quantity: 0,
    cost_per_unit: 1.50,
    min_threshold: 24,
    storage: "DRINK_FRIDGE"
  },
  {
    name: "Fanta",
    vietnamese_name: "Fanta",
    category: "beverages",
    unit: "cans",
    quantity: 0,
    cost_per_unit: 2.50,
    min_threshold: 12,
    storage: "DRINK_FRIDGE"
  },
  {
    name: "Mountain Dew",
    vietnamese_name: "Mountain Dew",
    category: "beverages",
    unit: "cans",
    quantity: 0,
    cost_per_unit: 2.50,
    min_threshold: 12,
    storage: "DRINK_FRIDGE"
  },

  // PACKAGING - Supplies and Containers
  {
    name: "T4 Plate",
    vietnamese_name: "Đĩa cơm lớn",
    category: "packaging",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 0.80,
    min_threshold: 50,
    storage: "PACKAGING"
  },
  {
    name: "T3 Plate",
    vietnamese_name: "Đĩa cơm nhỏ",
    category: "packaging",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 0.70,
    min_threshold: 50,
    storage: "PACKAGING"
  },
  {
    name: "T1 Plate",
    vietnamese_name: "Đĩa đựng đồ ăn nhẹ",
    category: "packaging",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 0.60,
    min_threshold: 30,
    storage: "PACKAGING"
  },
  {
    name: "Chopsticks",
    vietnamese_name: "Đũa",
    category: "packaging",
    unit: "pairs",
    quantity: 0,
    cost_per_unit: 0.10,
    min_threshold: 100,
    storage: "PACKAGING"
  },
  {
    name: "Fork",
    vietnamese_name: "Dĩa",
    category: "packaging",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 0.15,
    min_threshold: 50,
    storage: "PACKAGING"
  },
  {
    name: "Spoons",
    vietnamese_name: "Thìa",
    category: "packaging",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 0.15,
    min_threshold: 50,
    storage: "PACKAGING"
  },
  {
    name: "Sauce container 35ml",
    vietnamese_name: "Hộp sauce nhỏ",
    category: "packaging",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 0.25,
    min_threshold: 100,
    storage: "PACKAGING"
  },
  {
    name: "Sauce container 100ml",
    vietnamese_name: "Hộp sauce lớn",
    category: "packaging",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 0.35,
    min_threshold: 50,
    storage: "PACKAGING"
  },
  {
    name: "Sauce container 228ml",
    vietnamese_name: "Hộp sauce bún chả",
    category: "packaging",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 0.45,
    min_threshold: 30,
    storage: "PACKAGING"
  },
  {
    name: "Bowl/lids",
    vietnamese_name: "Tô bún/Lắp tô",
    category: "packaging",
    unit: "sets",
    quantity: 0,
    cost_per_unit: 0.80,
    min_threshold: 50,
    storage: "PACKAGING"
  },
  {
    name: "Rectangle box 500ml",
    vietnamese_name: "Hộp chân gà",
    category: "packaging",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 0.60,
    min_threshold: 30,
    storage: "PACKAGING"
  },
  {
    name: "Lunch box",
    vietnamese_name: "Hộp take away nhỏ",
    category: "packaging",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 0.90,
    min_threshold: 50,
    storage: "PACKAGING"
  },
  {
    name: "Family box",
    vietnamese_name: "Hộp takeaway lớn",
    category: "packaging",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 1.20,
    min_threshold: 30,
    storage: "PACKAGING"
  },
  {
    name: "Tissue",
    vietnamese_name: "Khăn giấy",
    category: "packaging",
    unit: "packs",
    quantity: 0,
    cost_per_unit: 3.50,
    min_threshold: 10,
    storage: "PACKAGING"
  },
  {
    name: "Skewer",
    vietnamese_name: "Cây xiên",
    category: "packaging",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 0.05,
    min_threshold: 200,
    storage: "PACKAGING"
  },
  {
    name: "Straws",
    vietnamese_name: "Ống hút",
    category: "packaging",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 0.05,
    min_threshold: 100,
    storage: "PACKAGING"
  },
  {
    name: "Cup 700ml",
    vietnamese_name: "Cốc lớn",
    category: "packaging",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 0.40,
    min_threshold: 50,
    storage: "PACKAGING"
  },
  {
    name: "Cup 500ml",
    vietnamese_name: "Cốc nhỏ",
    category: "packaging",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 0.35,
    min_threshold: 50,
    storage: "PACKAGING"
  },
  {
    name: "Lids",
    vietnamese_name: "Lắp",
    category: "packaging",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 0.15,
    min_threshold: 100,
    storage: "PACKAGING"
  },
  {
    name: "Thermal receipt rolls",
    vietnamese_name: "Giấy in recipe",
    category: "packaging",
    unit: "rolls",
    quantity: 0,
    cost_per_unit: 8.50,
    min_threshold: 5,
    storage: "PACKAGING"
  },
  {
    name: "Foil",
    vietnamese_name: "Giấy bạc",
    category: "packaging",
    unit: "rolls",
    quantity: 0,
    cost_per_unit: 12.00,
    min_threshold: 3,
    storage: "PACKAGING"
  },
  {
    name: "Cling wrap",
    vietnamese_name: "Màng bọc",
    category: "packaging",
    unit: "rolls",
    quantity: 0,
    cost_per_unit: 8.50,
    min_threshold: 3,
    storage: "PACKAGING"
  },
  {
    name: "Take away bag S",
    vietnamese_name: "Túi take away nhỏ",
    category: "packaging",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 0.20,
    min_threshold: 100,
    storage: "PACKAGING"
  },
  {
    name: "Take away bag L",
    vietnamese_name: "Túi take away lớn",
    category: "packaging",
    unit: "pieces",
    quantity: 0,
    cost_per_unit: 0.30,
    min_threshold: 50,
    storage: "PACKAGING"
  },

  // DRY STORAGE - Dry Goods and Staples
  {
    name: "Rice",
    vietnamese_name: "Gạo",
    category: "dry_goods",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 3.50,
    min_threshold: 20,
    storage: "DRY_STORAGE"
  },
  {
    name: "Vermicelli",
    vietnamese_name: "Bún",
    category: "dry_goods",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 8.50,
    min_threshold: 5,
    storage: "DRY_STORAGE"
  },
  {
    name: "Rice paper",
    vietnamese_name: "Bánh tráng",
    category: "dry_goods",
    unit: "packs",
    quantity: 0,
    cost_per_unit: 4.50,
    min_threshold: 10,
    storage: "DRY_STORAGE"
  },
  {
    name: "Mayo",
    vietnamese_name: "Mayo",
    category: "condiments",
    unit: "bottles",
    quantity: 0,
    cost_per_unit: 6.50,
    min_threshold: 3,
    storage: "DRY_STORAGE"
  },
  {
    name: "Chilli sauce",
    vietnamese_name: "Tương ớt",
    category: "condiments",
    unit: "bottles",
    quantity: 0,
    cost_per_unit: 4.50,
    min_threshold: 5,
    storage: "DRY_STORAGE"
  },
  {
    name: "Dry shallots",
    vietnamese_name: "Hành sấy",
    category: "condiments",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 18.00,
    min_threshold: 1,
    storage: "DRY_STORAGE"
  },
  {
    name: "Oil",
    vietnamese_name: "Dầu",
    category: "condiments",
    unit: "liters",
    quantity: 0,
    cost_per_unit: 8.50,
    min_threshold: 5,
    storage: "DRY_STORAGE"
  },
  {
    name: "Fish sauce",
    vietnamese_name: "Nước mắm",
    category: "condiments",
    unit: "bottles",
    quantity: 0,
    cost_per_unit: 12.50,
    min_threshold: 3,
    storage: "DRY_STORAGE"
  },
  {
    name: "Sugar",
    vietnamese_name: "Đường",
    category: "dry_goods",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 2.50,
    min_threshold: 5,
    storage: "DRY_STORAGE"
  },
  {
    name: "Salt",
    vietnamese_name: "Muối",
    category: "dry_goods",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 1.50,
    min_threshold: 2,
    storage: "DRY_STORAGE"
  },
  {
    name: "MSG",
    vietnamese_name: "Mì chính/bột ngọt",
    category: "dry_goods",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 4.50,
    min_threshold: 1,
    storage: "DRY_STORAGE"
  },
  {
    name: "Chicken powder",
    vietnamese_name: "Bột gà",
    category: "dry_goods",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 8.50,
    min_threshold: 1,
    storage: "DRY_STORAGE"
  },
  {
    name: "Pepper",
    vietnamese_name: "Tiêu bắc",
    category: "dry_goods",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 25.00,
    min_threshold: 0.5,
    storage: "DRY_STORAGE"
  },
  {
    name: "Charcoal stick",
    vietnamese_name: "Than cây",
    category: "fuel",
    unit: "kg",
    quantity: 0,
    cost_per_unit: 3.50,
    min_threshold: 10,
    storage: "DRY_STORAGE"
  },
  {
    name: "Charcoal bag",
    vietnamese_name: "Than túi",
    category: "fuel",
    unit: "bags",
    quantity: 0,
    cost_per_unit: 15.00,
    min_threshold: 3,
    storage: "DRY_STORAGE"
  },
  {
    name: "Tea box",
    vietnamese_name: "Hộp trà",
    category: "beverages",
    unit: "boxes",
    quantity: 0,
    cost_per_unit: 8.50,
    min_threshold: 5,
    storage: "DRY_STORAGE"
  },
  {
    name: "Sugar syrup",
    vietnamese_name: "Nước đường",
    category: "beverages",
    unit: "bottles",
    quantity: 0,
    cost_per_unit: 6.50,
    min_threshold: 3,
    storage: "DRY_STORAGE"
  },
  {
    name: "Peach syrup",
    vietnamese_name: "Nước cốt đào",
    category: "beverages",
    unit: "bottles",
    quantity: 0,
    cost_per_unit: 8.50,
    min_threshold: 2,
    storage: "DRY_STORAGE"
  },
  {
    name: "Disposal gloves",
    vietnamese_name: "Găng tay",
    category: "supplies",
    unit: "boxes",
    quantity: 0,
    cost_per_unit: 12.50,
    min_threshold: 3,
    storage: "DRY_STORAGE"
  },

  // CHEMICAL STORAGE - Cleaning Supplies
  {
    name: "Pink degreaser",
    vietnamese_name: "Chất tẩy rửa hồng",
    category: "chemicals",
    unit: "bottles",
    quantity: 0,
    cost_per_unit: 15.50,
    min_threshold: 2,
    storage: "CHEMICAL_STORAGE"
  },
  {
    name: "Bleach",
    vietnamese_name: "Nước tẩy",
    category: "chemicals",
    unit: "bottles",
    quantity: 0,
    cost_per_unit: 8.50,
    min_threshold: 3,
    storage: "CHEMICAL_STORAGE"
  },
  {
    name: "Sanitizer",
    vietnamese_name: "Dung dịch khử trùng",
    category: "chemicals",
    unit: "bottles",
    quantity: 0,
    cost_per_unit: 12.50,
    min_threshold: 3,
    storage: "CHEMICAL_STORAGE"
  }
];