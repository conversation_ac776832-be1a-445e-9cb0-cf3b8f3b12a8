'use client';

import {
  InventoryAlert,
  InventoryTransaction,
  InventoryItem,
  Item,
  ItemFilter,
  ItemProduction
} from '@/types/inventory';

// Import Supabase client
import { supabase } from '@/lib/supabase';

// Base API call function - REPLACED WITH SUPABASE
const apiCall = async <T>(
  endpoint: string,
  method: 'GET' | 'POST' | 'PATCH' | 'PUT' | 'DELETE' = 'GET',
  body?: any
): Promise<T> => {
  console.log('⚠️ DEPRECATED: API call to:', endpoint, 'method:', method);
  console.log('⚠️ This should be replaced with direct Supabase calls');

  // For now, throw an error to identify where old API calls are still being used
  throw new Error(`Deprecated API call: ${method} ${endpoint}. Please use Supabase client directly.`);
};

// Inventory Items API - MIGRATED TO SUPABASE
export const inventoryItemsApi = {
  // Get all inventory items
  getAll: async (filters?: {
    category?: string;
    belowThreshold?: boolean;
    supplierId?: string;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<{ data: InventoryItem[]; pagination: any }> => {
    console.log('📦 Fetching inventory items via backend API...');

    try {
      // Build query parameters
      const params = new URLSearchParams();
      if (filters?.category) params.append('category', filters.category);
      if (filters?.search) params.append('search', filters.search);
      if (filters?.belowThreshold) params.append('belowThreshold', 'true');
      if (filters?.supplierId) params.append('supplierId', filters.supplierId);
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());

      const queryString = params.toString();
      const url = `/api/inventory/items${queryString ? `?${queryString}` : ''}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('🔍 Inventory API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Inventory API error:', response.status, errorData);
        throw new Error(`Inventory API error: ${response.status} - ${errorData.error || 'Unknown error'}`);
      }

      const result = await response.json();
      console.log(`✅ Fetched ${result.data?.length || 0} inventory items via backend`);
      return result;
    } catch (error: any) {
      console.error('❌ Error in inventoryItemsApi.getAll:', error);
      throw error;
    }
  },

  // Get an inventory item by ID
  getById: async (id: string): Promise<InventoryItem> => {
    console.log('📦 Fetching inventory item by ID via backend API:', id);

    try {
      const response = await fetch(`/api/inventory/items/${id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('🔍 Get inventory by ID API response status:', response.status);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Inventory item not found');
        }
        const errorData = await response.json();
        console.error('❌ Get inventory by ID API error:', response.status, errorData);
        throw new Error(`Get API error: ${response.status} - ${errorData.error || 'Unknown error'}`);
      }

      const data = await response.json();
      console.log('✅ Fetched inventory item via backend:', data.name);
      return data;
    } catch (error: any) {
      console.error('❌ Error in inventoryItemsApi.getById:', error);
      throw error;
    }
  },

  // Add a new inventory item
  add: async (item: Omit<InventoryItem, 'id'>): Promise<InventoryItem> => {
    console.log('📦 Adding new inventory item via backend API:', item.name);
    console.log('📦 Item data:', item);

    try {
      const response = await fetch('/api/inventory/items', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(item),
      });

      console.log('🔍 Add inventory API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Add inventory API error:', response.status, errorData);
        throw new Error(`Add API error: ${response.status} - ${errorData.error || 'Unknown error'}`);
      }

      const data = await response.json();
      console.log('✅ Added inventory item via backend:', data.name);
      return data;
    } catch (error: any) {
      console.error('❌ Error in inventoryItemsApi.add:', error);
      throw error;
    }
  },

  // Update an inventory item
  update: async (id: string, updates: Partial<InventoryItem>): Promise<InventoryItem> => {
    console.log('📦 Updating inventory item via backend API:', id);
    console.log('📦 Update data:', updates);

    try {
      const response = await fetch(`/api/inventory/items/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      console.log('🔍 Update inventory API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Update inventory API error:', response.status, errorData);
        throw new Error(`Update API error: ${response.status} - ${errorData.error || 'Unknown error'}`);
      }

      const data = await response.json();
      console.log('✅ Updated inventory item via backend:', data.name);
      return data;
    } catch (error: any) {
      console.error('❌ Error in inventoryItemsApi.update:', error);
      throw error;
    }
  },

  // Delete an inventory item
  delete: async (id: string): Promise<{ success: boolean }> => {
    console.log('📦 Deleting inventory item via backend API:', id);

    try {
      const response = await fetch(`/api/inventory/items/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('🔍 Delete inventory API response status:', response.status);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Inventory item not found');
        }
        const errorData = await response.json();
        console.error('❌ Delete inventory API error:', response.status, errorData);
        throw new Error(`Delete API error: ${response.status} - ${errorData.error || 'Unknown error'}`);
      }

      const data = await response.json();
      console.log('✅ Deleted inventory item via backend');
      return data;
    } catch (error: any) {
      console.error('❌ Error in inventoryItemsApi.delete:', error);
      throw error;
    }
  }
};

// Transactions API - MIGRATED TO SUPABASE
export const transactionsApi = {
  // Get all transactions
  getAll: async (filters?: {
    inventoryItemId?: string;
    type?: string;
    startDate?: Date;
    endDate?: Date;
  }): Promise<InventoryTransaction[]> => {
    console.log('📊 Fetching transactions via backend API...');

    try {
      // Build query parameters
      const params = new URLSearchParams();
      if (filters?.inventoryItemId) params.append('inventoryItemId', filters.inventoryItemId);
      if (filters?.type) params.append('type', filters.type);
      if (filters?.startDate) params.append('startDate', filters.startDate.toISOString());
      if (filters?.endDate) params.append('endDate', filters.endDate.toISOString());

      const queryString = params.toString();
      const url = `/api/inventory/transactions${queryString ? `?${queryString}` : ''}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('🔍 Transactions API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Transactions API error:', response.status, errorData);
        // Return empty array for non-critical errors (like table not existing)
        if (response.status === 404 || response.status === 500) {
          console.warn('⚠️ Transactions table may not exist, returning empty array');
          return [];
        }
        throw new Error(`Transactions API error: ${response.status} - ${errorData.error || 'Unknown error'}`);
      }

      const result = await response.json();
      console.log(`✅ Fetched ${result.data?.length || 0} transactions via backend`);
      return result.data || [];
    } catch (error: any) {
      console.warn('⚠️ Error fetching transactions, returning empty array:', error.message);
      return []; // Return empty array if there's an error
    }
  },

  // Add a new transaction
  add: async (transaction: {
    inventoryItemId: string;
    type: string;
    quantity: number;
    date?: Date;
    cost?: number;
    notes?: string;
  }): Promise<{
    success: boolean;
    transaction: InventoryTransaction;
    inventoryItem: InventoryItem;
  }> => {
    console.log('📊 Adding new transaction:', transaction);

    try {
      // Insert transaction
      const { data: transactionData, error: transactionError } = await supabase
        .from('transactions')
        .insert([{
          inventory_item_id: transaction.inventoryItemId,
          type: transaction.type,
          quantity: transaction.quantity,
          cost: transaction.cost || 0,
          notes: transaction.notes || null,
          date: transaction.date || new Date().toISOString()
        }])
        .select()
        .single();

      if (transactionError) {
        console.error('❌ Error adding transaction:', transactionError);
        throw new Error(`Failed to add transaction: ${transactionError.message}`);
      }

      // Get the updated inventory item
      const { data: inventoryData, error: inventoryError } = await supabase
        .from('inventory_items')
        .select('*')
        .eq('id', transaction.inventoryItemId)
        .single();

      if (inventoryError) {
        console.warn('⚠️ Could not fetch updated inventory item:', inventoryError);
      }

      // Map the response
      const mappedTransaction = {
        id: transactionData.id,
        inventoryItemId: transactionData.inventory_item_id,
        type: transactionData.type,
        quantity: transactionData.quantity,
        cost: transactionData.cost,
        notes: transactionData.notes,
        date: transactionData.date,
        createdAt: transactionData.created_at
      };

      const mappedInventoryItem = inventoryData ? {
        id: inventoryData.id,
        name: inventoryData.name,
        vietnameseName: inventoryData.vietnamese_name,
        category: inventoryData.category,
        unit: inventoryData.unit,
        quantity: inventoryData.quantity,
        costPerUnit: inventoryData.cost_per_unit || 0,
        minThreshold: inventoryData.min_threshold || 0,
        storage: inventoryData.storage,
        supplierId: inventoryData.supplier_id,
        lastRestockDate: inventoryData.last_restock_date,
        createdAt: inventoryData.created_at,
        updatedAt: inventoryData.updated_at
      } : {} as InventoryItem;

      console.log('✅ Added transaction successfully');
      return {
        success: true,
        transaction: mappedTransaction,
        inventoryItem: mappedInventoryItem
      };
    } catch (error) {
      console.error('❌ Error in transactionsApi.add:', error);
      throw error;
    }
  }
};

// Alerts API
export const alertsApi = {
  // Get all alerts
  getAll: async (filters?: {
    inventoryItemId?: string;
    type?: string;
    onlyUnread?: boolean;
  }): Promise<InventoryAlert[]> => {
    // Build query string
    let queryString = '';
    if (filters) {
      const params = new URLSearchParams();
      if (filters.inventoryItemId) params.append('inventoryItemId', filters.inventoryItemId);
      if (filters.type) params.append('type', filters.type);
      if (filters.onlyUnread) params.append('onlyUnread', 'true');
      queryString = `?${params.toString()}`;
    }

    return apiCall<InventoryAlert[]>(`/alerts${queryString}`);
  },

  // Mark an alert as read
  markAsRead: async (id: string): Promise<{ success: boolean }> => {
    return apiCall<{ success: boolean }>(`/alerts/${id}`, 'PATCH');
  }
};

// Items API - MIGRATED TO SUPABASE
export const itemsApi = {
  // Get all items
  getAll: async (filters?: ItemFilter & { page?: number; limit?: number }): Promise<{ data: Item[]; pagination: any }> => {
    console.log('🍽️ Fetching items via backend API...');

    try {
      // Build query parameters
      const params = new URLSearchParams();
      if (filters?.category) params.append('category', filters.category);
      if (filters?.search) params.append('search', filters.search);
      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());

      const queryString = params.toString();
      const url = `/api/items/menu${queryString ? `?${queryString}` : ''}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('🔍 Items API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Items API error:', response.status, errorData);
        throw new Error(`Items API error: ${response.status} - ${errorData.error || 'Unknown error'}`);
      }

      const result = await response.json();
      console.log(`✅ Fetched ${result.data?.length || 0} items via backend`);
      return result;
    } catch (error: any) {
      console.error('❌ Error in itemsApi.getAll:', error);
      throw error;
    }
  },

  // Get an item by ID
  getById: async (id: string): Promise<Item> => {
    console.log('🍽️ Fetching item by ID via backend API:', id);

    try {
      const response = await fetch(`/api/items/menu/${id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('🔍 Item by ID API response status:', response.status);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Item not found');
        }
        const errorData = await response.json();
        console.error('❌ Item by ID API error:', response.status, errorData);
        throw new Error(`Item API error: ${response.status} - ${errorData.error || 'Unknown error'}`);
      }

      const data = await response.json();
      console.log('✅ Fetched item via backend:', data.name);
      return data;
    } catch (error: any) {
      console.error('❌ Error in itemsApi.getById:', error);
      throw error;
    }
  },

  // Add a new item
  add: async (item: Omit<Item, 'id'>): Promise<Item> => {
    console.log('🍽️ Adding new item via backend API:', item.name);

    try {
      const response = await fetch('/api/items/menu', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(item),
      });

      console.log('🔍 Add item API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Add item API error:', response.status, errorData);
        throw new Error(`Add API error: ${response.status} - ${errorData.error || 'Unknown error'}`);
      }

      const data = await response.json();
      console.log('✅ Added item via backend:', data.name);
      return data;
    } catch (error: any) {
      console.error('❌ Error in itemsApi.add:', error);
      throw error;
    }
  },

  // Update an item
  update: async (id: string, updates: Partial<Item>): Promise<Item> => {
    console.log('🍽️ Updating item via backend API:', id);

    try {
      const response = await fetch(`/api/items/menu/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      console.log('🔍 Update item API response status:', response.status);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Item not found');
        }
        const errorData = await response.json();
        console.error('❌ Update item API error:', response.status, errorData);
        throw new Error(`Update API error: ${response.status} - ${errorData.error || 'Unknown error'}`);
      }

      const data = await response.json();
      console.log('✅ Updated item via backend:', data.name);
      return data;
    } catch (error: any) {
      console.error('❌ Error in itemsApi.update:', error);
      throw error;
    }
  },

  // Delete an item
  delete: async (id: string): Promise<{ success: boolean }> => {
    console.log('🍽️ Deleting item via backend API:', id);

    try {
      const response = await fetch(`/api/items/menu/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('🔍 Delete item API response status:', response.status);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Item not found');
        }
        const errorData = await response.json();
        console.error('❌ Delete item API error:', response.status, errorData);
        throw new Error(`Delete API error: ${response.status} - ${errorData.error || 'Unknown error'}`);
      }

      const data = await response.json();
      console.log('✅ Deleted item via backend');
      return data;
    } catch (error: any) {
      console.error('❌ Error in itemsApi.delete:', error);
      throw error;
    }
  },

  // Produce an item (deduct ingredients from inventory)
  produce: async (production: {
    itemId: string;
    variantId: string;
    quantity: number;
    notes?: string;
    tableNumber?: string;
    customerName?: string;
  }): Promise<{
    success: boolean;
    production: ItemProduction;
    transactions: InventoryTransaction[];
  }> => {
    console.log('🍽️ Recording item production:', production);

    try {
      // This would need more complex logic to handle inventory deduction
      // For now, just return a placeholder
      throw new Error('Item production not yet implemented with Supabase');
    } catch (error) {
      console.error('❌ Error in itemsApi.produce:', error);
      throw error;
    }
  }
};

// Reports API
export const reportsApi = {
  // Get all reports
  getAll: async (filters?: {
    type?: string;
    startDate?: Date;
    endDate?: Date;
  }): Promise<any[]> => {
    // Build query string
    let queryString = '';
    if (filters) {
      const params = new URLSearchParams();
      if (filters.type) params.append('type', filters.type);
      if (filters.startDate) params.append('startDate', filters.startDate.toISOString());
      if (filters.endDate) params.append('endDate', filters.endDate.toISOString());
      queryString = `?${params.toString()}`;
    }

    return apiCall<any[]>(`/reports${queryString}`);
  },

  // Get a report by ID
  getById: async (id: string): Promise<any> => {
    return apiCall<any>(`/reports/${id}`);
  },

  // Generate a new report
  generate: async (params: {
    type: string;
    startDate?: Date;
    endDate?: Date;
  }): Promise<any> => {
    // Build query string
    const queryParams = new URLSearchParams();
    queryParams.append('type', params.type);
    queryParams.append('generate', 'true');
    if (params.startDate) queryParams.append('startDate', params.startDate.toISOString());
    if (params.endDate) queryParams.append('endDate', params.endDate.toISOString());

    return apiCall<any>(`/reports?${queryParams.toString()}`);
  }
};

// Square API - MIGRATED TO SUPABASE
export const squareApi = {
  // Get sales data from Square
  getSales: async (date: string): Promise<any> => {
    console.log('🟨 Square sales API not yet implemented with Supabase');
    throw new Error('Square sales API not yet implemented with Supabase');
  },

  // Get catalog data from Square
  getCatalog: async (params?: { page?: number; limit?: number; cursor?: string }): Promise<any> => {
    console.log('🟨 Fetching Square catalog via backend API...', params);

    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.cursor) queryParams.append('cursor', params.cursor);

      const queryString = queryParams.toString();
      const url = `/api/square/catalog${queryString ? `?${queryString}` : ''}`;

      console.log('🔍 Making Square catalog API request via backend...');
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('🔍 Square catalog API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Square catalog API error:', response.status, errorData);
        throw new Error(`Square API error: ${response.status} - ${errorData.error || 'Unknown error'}`);
      }

      const data = await response.json();
      console.log('✅ Square catalog fetched successfully via backend');
      console.log('🔍 Catalog items count:', data.items?.length || 0);

      return data;
    } catch (error: any) {
      console.error('❌ Error fetching Square catalog:', error);
      throw error;
    }
  },

  // Get locations from Square
  getLocations: async (): Promise<any> => {
    console.log('🟨 Fetching Square locations via backend API...');

    try {
      console.log('🔍 Making Square locations API request via backend...');
      const response = await fetch('/api/square/locations', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('🔍 Square locations API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Square locations API error:', response.status, errorData);
        throw new Error(`Square API error: ${response.status} - ${errorData.error || 'Unknown error'}`);
      }

      const data = await response.json();
      console.log('✅ Square locations fetched successfully via backend');
      console.log('🔍 Locations count:', data.locations?.length || 0);

      return data;
    } catch (error: any) {
      console.error('❌ Error fetching Square locations:', error);
      throw error;
    }
  },

  // Get sales data for catalog items
  getCatalogSales: async (params: {
    startDate: string;
    endDate?: string;
    catalogItemId?: string;
    locationId?: string;
    page?: number;
    limit?: number;
  }): Promise<any> => {
    console.log('🟨 Fetching Square catalog sales via dedicated sales proxy...', params);

    try {
      console.log('🔍 Making Square sales API request via dedicated proxy...');
      const response = await fetch('/api/square/sales', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });

      console.log('🔍 Square sales proxy response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Square sales proxy error:', response.status, errorData);
        throw new Error(`Square API error: ${response.status} - ${errorData.error || 'Unknown error'}`);
      }

      const data = await response.json();
      console.log('✅ Square sales fetched successfully via dedicated proxy');
      console.log('🔍 Orders count:', data.totalOrders || 0);
      console.log('🔍 Catalog sales count:', data.catalogSales?.length || 0);

      return data;
    } catch (error: any) {
      console.error('❌ Error fetching Square catalog sales:', error);
      throw error;
    }
  },

  // Sync Square catalog items to our items collection
  syncCatalog: async (items: any[], overwrite: boolean = false): Promise<any> => {
    console.log('🟨 Syncing Square catalog items via backend API...', { itemCount: items.length, overwrite });

    try {
      const response = await fetch('/api/square/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ items, overwrite }),
      });

      console.log('🔍 Square sync API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Square sync API error:', response.status, errorData);
        throw new Error(`Square sync API error: ${response.status} - ${errorData.error || 'Unknown error'}`);
      }

      const results = await response.json();
      console.log('✅ Square catalog sync completed via backend:', results);
      return results;
    } catch (error: any) {
      console.error('❌ Error in Square catalog sync:', error);
      throw error;
    }
  },

  // Save Square API configuration
  saveConfig: async (accessToken: string): Promise<any> => {
    console.log('🟨 Saving Square access token via backend API...');

    try {
      const response = await fetch('/api/square/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ accessToken }),
      });

      console.log('🔍 Square config API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Square config API error:', response.status, errorData);
        throw new Error(`Square config API error: ${response.status} - ${errorData.error || 'Unknown error'}`);
      }

      const result = await response.json();
      console.log('✅ Square access token saved successfully via backend');
      return result;
    } catch (error: any) {
      console.error('❌ Error in squareApi.saveConfig:', error);
      throw error;
    }
  },

  // Check if Square API is configured
  checkConfig: async (): Promise<any> => {
    console.log('🟨 Checking Square config via backend API...');

    try {
      const response = await fetch('/api/square/config', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('🔍 Square config API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Square config API error:', response.status, errorData);
        return { configured: false, tableExists: false };
      }

      const result = await response.json();
      console.log('✅ Square config check completed via backend');
      return result;
    } catch (error: any) {
      console.warn('⚠️ Square config check failed:', error);
      return { configured: false, tableExists: false };
    }
  },
};

// Users API
export const usersApi = {
  // Get all users
  getAll: async (filters?: {
    role?: string;
    isActive?: boolean;
    search?: string;
  }): Promise<any[]> => {
    // Build query string
    let queryString = '';
    if (filters) {
      const params = new URLSearchParams();
      if (filters.role) params.append('role', filters.role);
      if (filters.isActive !== undefined) params.append('isActive', filters.isActive.toString());
      if (filters.search) params.append('search', filters.search);
      queryString = `?${params.toString()}`;
    }

    return apiCall<any[]>(`/users${queryString}`);
  },

  // Get a user by ID
  getById: async (id: string): Promise<any> => {
    return apiCall<any>(`/users/${id}`);
  },

  // Update a user
  update: async (id: string, updates: any): Promise<any> => {
    // Remove _id field from updates to prevent MongoDB error
    const { _id, ...updatesWithoutId } = updates;
    return apiCall<any>(`/users/${id}`, 'PATCH', updatesWithoutId);
  },

  // Delete a user
  delete: async (id: string, hardDelete: boolean = false): Promise<{ success: boolean }> => {
    return apiCall<{ success: boolean }>(`/users/${id}?hardDelete=${hardDelete}`, 'DELETE');
  },

  // Change password
  changePassword: async (id: string, currentPassword: string, newPassword: string): Promise<{ success: boolean }> => {
    return apiCall<{ success: boolean }>(`/users/${id}/change-password`, 'POST', {
      currentPassword,
      newPassword
    });
  }
};
