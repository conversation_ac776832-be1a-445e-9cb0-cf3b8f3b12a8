import { Item } from '@/types/inventory';

// Square API configuration
const SQUARE_API_URL = process.env.NEXT_PUBLIC_SQUARE_API_URL || 'https://connect.squareup.com/v2';
const SQUARE_LOCATION_ID = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID;

/**
 * Get sales data from Square POS for a specific date
 */
export const getDailySales = async (date: Date): Promise<{
  success: boolean;
  data?: {
    itemsSold: Array<{
      itemId: string;
      itemName: string;
      quantity: number;
      revenue: number;
    }>;
    totalSales: number;
    orderCount: number;
  };
  error?: string;
}> => {
  try {
    // Get auth token from localStorage
    const authData = localStorage.getItem('auth');
    if (!authData) {
      return { success: false, error: 'Authentication required' };
    }

    const { token } = JSON.parse(authData);

    // Format date for API request (YYYY-MM-DD)
    const formattedDate = date.toISOString().split('T')[0];

    // Make API request to our backend proxy for Square API
    const response = await fetch(`/api/square/sales?date=${formattedDate}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `API error: ${response.status}`);
    }

    const data = await response.json();
    return { success: true, data };
  } catch (error: any) {
    console.error('Error fetching Square POS sales data:', error);
    return { success: false, error: error.message || 'Failed to fetch sales data' };
  }
};

/**
 * Sync item sales data from Square to our system
 */
export const syncItemSales = async (date: Date): Promise<{
  success: boolean;
  message?: string;
  error?: string;
}> => {
  try {
    // Get auth token from localStorage
    const authData = localStorage.getItem('auth');
    if (!authData) {
      return { success: false, error: 'Authentication required' };
    }

    const { token } = JSON.parse(authData);

    // Format date for API request (YYYY-MM-DD)
    const formattedDate = date.toISOString().split('T')[0];

    // Make API request to our backend proxy for Square API
    const response = await fetch(`/api/square/sync`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ date: formattedDate })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      message: `Successfully synced ${data.syncedItems} items from Square POS`
    };
  } catch (error: any) {
    console.error('Error syncing Square POS data:', error);
    return { success: false, error: error.message || 'Failed to sync sales data' };
  }
};