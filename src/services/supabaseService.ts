import { supabase, supabaseAdmin } from '@/lib/supabase';
import { Database } from '@/types/supabase';

// Type aliases for easier use
type InventoryItem = Database['public']['Tables']['inventory_items']['Row'];
type InventoryItemInsert = Database['public']['Tables']['inventory_items']['Insert'];
type InventoryItemUpdate = Database['public']['Tables']['inventory_items']['Update'];

type Item = Database['public']['Tables']['items']['Row'];
type ItemInsert = Database['public']['Tables']['items']['Insert'];
type ItemUpdate = Database['public']['Tables']['items']['Update'];

type User = Database['public']['Tables']['users']['Row'];
type UserInsert = Database['public']['Tables']['users']['Insert'];
type UserUpdate = Database['public']['Tables']['users']['Update'];

// Inventory Items Service
export const inventoryItemsService = {
  // Get all inventory items
  async getAll(filters?: {
    category?: string;
    search?: string;
    belowThreshold?: boolean;
  }) {
    let query = supabase
      .from('inventory_items')
      .select('*, suppliers(name)')
      .order('name');

    if (filters?.category) {
      query = query.eq('category', filters.category);
    }

    if (filters?.search) {
      query = query.or(`name.ilike.%${filters.search}%,vietnamese_name.ilike.%${filters.search}%`);
    }

    if (filters?.belowThreshold) {
      query = query.lt('quantity', 'min_threshold');
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return data;
  },

  // Get inventory item by ID
  async getById(id: string) {
    const { data, error } = await supabase
      .from('inventory_items')
      .select('*, suppliers(name)')
      .eq('id', id)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  },

  // Create new inventory item
  async create(item: InventoryItemInsert) {
    const { data, error } = await supabase
      .from('inventory_items')
      .insert(item)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  },

  // Update inventory item
  async update(id: string, updates: InventoryItemUpdate) {
    const { data, error } = await supabase
      .from('inventory_items')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  },

  // Delete inventory item
  async delete(id: string) {
    const { error } = await supabase
      .from('inventory_items')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  },

  // Get low stock items
  async getLowStock() {
    const { data, error } = await supabase
      .from('inventory_items')
      .select('*')
      .lt('quantity', 'min_threshold')
      .order('name');

    if (error) {
      throw new Error(error.message);
    }

    return data;
  }
};

// Items (Menu Items) Service
export const itemsService = {
  // Get all items with variants and ingredients
  async getAll(filters?: {
    category?: string;
    search?: string;
  }) {
    let query = supabase
      .from('items')
      .select(`
        *,
        item_variants(*),
        item_ingredients(*, inventory_items(name, unit))
      `)
      .order('name');

    if (filters?.category) {
      query = query.eq('category', filters.category);
    }

    if (filters?.search) {
      query = query.or(`name.ilike.%${filters.search}%,vietnamese_name.ilike.%${filters.search}%`);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return data;
  },

  // Get item by ID
  async getById(id: string) {
    const { data, error } = await supabase
      .from('items')
      .select(`
        *,
        item_variants(*),
        item_ingredients(*, inventory_items(name, unit))
      `)
      .eq('id', id)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  },

  // Create new item
  async create(item: ItemInsert) {
    const { data, error } = await supabase
      .from('items')
      .insert(item)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  },

  // Update item
  async update(id: string, updates: ItemUpdate) {
    const { data, error } = await supabase
      .from('items')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  },

  // Delete item
  async delete(id: string) {
    const { error } = await supabase
      .from('items')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  }
};

// Transactions Service
export const transactionsService = {
  // Get all transactions
  async getAll(filters?: {
    inventoryItemId?: string;
    type?: string;
    startDate?: string;
    endDate?: string;
  }) {
    let query = supabase
      .from('transactions')
      .select(`
        *,
        inventory_items(name),
        items(name),
        users(username, first_name, last_name)
      `)
      .order('date', { ascending: false });

    if (filters?.inventoryItemId) {
      query = query.eq('inventory_item_id', filters.inventoryItemId);
    }

    if (filters?.type) {
      query = query.eq('type', filters.type);
    }

    if (filters?.startDate) {
      query = query.gte('date', filters.startDate);
    }

    if (filters?.endDate) {
      query = query.lte('date', filters.endDate);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return data;
  },

  // Create new transaction
  async create(transaction: {
    inventory_item_id: string;
    type: Database['public']['Enums']['transaction_type'];
    quantity: number;
    cost?: number;
    notes?: string;
    item_id?: string;
    user_id?: string;
  }) {
    const { data, error } = await supabase
      .from('transactions')
      .insert(transaction)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    // Update inventory item quantity
    if (transaction.type === 'purchase') {
      await supabase.rpc('update_inventory_quantity', {
        item_id: transaction.inventory_item_id,
        quantity_change: transaction.quantity
      });
    } else if (transaction.type === 'usage' || transaction.type === 'waste') {
      await supabase.rpc('update_inventory_quantity', {
        item_id: transaction.inventory_item_id,
        quantity_change: -transaction.quantity
      });
    }

    return data;
  }
};

// Users Service
export const usersService = {
  // Get all users
  async getAll(filters?: {
    role?: string;
    isActive?: boolean;
    search?: string;
  }) {
    let query = supabase
      .from('users')
      .select('*')
      .order('username');

    if (filters?.role) {
      query = query.eq('role', filters.role);
    }

    if (filters?.isActive !== undefined) {
      query = query.eq('is_active', filters.isActive);
    }

    if (filters?.search) {
      query = query.or(`username.ilike.%${filters.search}%,email.ilike.%${filters.search}%,first_name.ilike.%${filters.search}%,last_name.ilike.%${filters.search}%`);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return data;
  },

  // Get user by ID
  async getById(id: string) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  },

  // Update user
  async update(id: string, updates: UserUpdate) {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  },

  // Delete user (soft delete)
  async delete(id: string, hardDelete = false) {
    if (hardDelete) {
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', id);

      if (error) {
        throw new Error(error.message);
      }
    } else {
      const { error } = await supabase
        .from('users')
        .update({ is_active: false })
        .eq('id', id);

      if (error) {
        throw new Error(error.message);
      }
    }

    return true;
  }
};

// Alerts Service
export const alertsService = {
  // Get all alerts
  async getAll(unreadOnly = false) {
    let query = supabase
      .from('alerts')
      .select(`
        *,
        inventory_items(name)
      `)
      .order('created_at', { ascending: false });

    if (unreadOnly) {
      query = query.eq('is_read', false);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return data;
  },

  // Mark alert as read
  async markAsRead(id: string) {
    const { data, error } = await supabase
      .from('alerts')
      .update({ is_read: true })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  },

  // Create new alert
  async create(alert: {
    inventory_item_id: string;
    type: Database['public']['Enums']['alert_type'];
    message: string;
  }) {
    const { data, error } = await supabase
      .from('alerts')
      .insert(alert)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data;
  }
};
