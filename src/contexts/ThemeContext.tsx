'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define the theme colors based on the Vietnamese restaurant palette
export interface ThemeColors {
  warmYellow: string;     // Base background color
  lightCream: string;     // Top background color
  vibrantRed: string;     // Primary branding color
  softCoral: string;      // Secondary accent color
  lightRed: string;       // Subtext background color
  sidebarColor: string;   // Sidebar background color
  sidebarActiveColor: string; // Sidebar active item background color
}

// Default theme colors - Professional restaurant management style
const defaultThemeColors: ThemeColors = {
  warmYellow: '#FFE194',  // viet.yellow (lighter version)
  lightCream: '#E5DFC9',  // viet.creamDark (darker beige for header)
  vibrantRed: '#EA4E3D',  // viet.red
  softCoral: '#EA4E3D',   // viet.red (same as vibrantRed)
  lightRed: '#EA4E3D',    // viet.red (same as vibrantRed)
  sidebarColor: '#FFFFFF', // Clean white sidebar like MarketMan
  sidebarActiveColor: '#F3F4F6', // Light gray for active items
};

// Define the theme context type
interface ThemeContextType {
  colors: ThemeColors;
  updateColors: (newColors: Partial<ThemeColors>) => void;
  resetColors: () => void;
  loading: boolean;
}

// Create the context with default values
const ThemeContext = createContext<ThemeContextType>({
  colors: defaultThemeColors,
  updateColors: () => {},
  resetColors: () => {},
  loading: false,
});

// Create a provider component
export const ThemeProvider = ({ children }: { children: ReactNode }) => {
  const [colors, setColors] = useState<ThemeColors>(defaultThemeColors);
  const [loading, setLoading] = useState(false);

  // Update CSS variables when colors change
  useEffect(() => {
    if (!loading) {
      // Update CSS variables
      document.documentElement.style.setProperty('--warm-yellow', colors.warmYellow);
      document.documentElement.style.setProperty('--light-cream', colors.lightCream);
      document.documentElement.style.setProperty('--vibrant-red', colors.vibrantRed);
      document.documentElement.style.setProperty('--soft-coral', colors.softCoral);
      document.documentElement.style.setProperty('--light-red', colors.lightRed);
      document.documentElement.style.setProperty('--sidebar-color', colors.sidebarColor || '#FFFFFF');
      document.documentElement.style.setProperty('--sidebar-active-color', colors.sidebarActiveColor || '#F3F4F6');

      // Force sidebar color update
      const sidebarElements = document.querySelectorAll('.sidebar-bg');
      sidebarElements.forEach(el => {
        (el as HTMLElement).style.backgroundColor = colors.sidebarColor || '#FFFFFF';
      });

      const sidebarActiveElements = document.querySelectorAll('.sidebar-active');
      sidebarActiveElements.forEach(el => {
        (el as HTMLElement).style.backgroundColor = colors.sidebarActiveColor || '#F3F4F6';
      });
    }
  }, [colors, loading]);

  // Function to update theme colors (simplified, no API calls)
  const updateColors = (newColors: Partial<ThemeColors>) => {
    const updatedColors = { ...colors, ...newColors };
    setColors(updatedColors);
  };

  // Function to reset theme colors to default
  const resetColors = () => {
    setColors(defaultThemeColors);
  };

  return (
    <ThemeContext.Provider
      value={{
        colors,
        updateColors,
        resetColors,
        loading,
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use the theme context
export const useTheme = () => useContext(ThemeContext);
