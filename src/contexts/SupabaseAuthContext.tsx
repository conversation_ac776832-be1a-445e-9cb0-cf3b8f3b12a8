'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { User as SupabaseUser } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';

// Define the User type from our database
type DatabaseUser = Database['public']['Tables']['users']['Row'];
type UserRole = Database['public']['Enums']['user_role'];

// Combined user type
interface User extends DatabaseUser {
  supabaseUser: SupabaseUser;
}

// Define the AuthContext type
type AuthContextType = {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  signUp: (email: string, password: string, userData: {
    username: string;
    firstName?: string;
    lastName?: string;
    role?: UserRole;
  }) => Promise<void>;
  isAuthenticated: boolean;
};

// Create the context with a default value
const SupabaseAuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  login: async () => {},
  logout: async () => {},
  signUp: async () => {},
  isAuthenticated: false,
});

// Create a provider component
export const SupabaseAuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [sessionChecked, setSessionChecked] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  // Save user to sessionStorage
  const saveUserToSession = (userData: User | null) => {
    try {
      if (userData) {
        sessionStorage.setItem('restaurant_user', JSON.stringify(userData));
      } else {
        sessionStorage.removeItem('restaurant_user');
      }
    } catch (error) {
      console.warn('Failed to save user to sessionStorage:', error);
    }
  };

  // Load user from sessionStorage
  const loadUserFromSession = (): User | null => {
    try {
      const savedUser = sessionStorage.getItem('restaurant_user');
      return savedUser ? JSON.parse(savedUser) : null;
    } catch (error) {
      console.warn('Failed to load user from sessionStorage:', error);
      return null;
    }
  };

  // Initialize auth state
  useEffect(() => {
    // Check sessionStorage first for faster loading
    const savedUser = loadUserFromSession();
    if (savedUser) {
      setUser(savedUser);
      setSessionChecked(true);
    }

    // Get initial session
    const getInitialSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();

      if (session?.user) {
        await fetchUserProfile(session.user);
      } else if (!savedUser) {
        // Only set loading to false if we don't have a saved user
        setLoading(false);
      }

      setSessionChecked(true);
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id);

        if (session?.user) {
          await fetchUserProfile(session.user);
        } else {
          // Only set user to null if this is a sign out event
          if (event === 'SIGNED_OUT') {
            console.log('User signed out, clearing user state');
            setUser(null);
          }
        }
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  // Fetch user profile from our users table
  const fetchUserProfile = async (supabaseUser: SupabaseUser) => {
    try {
      console.log('Fetching user profile for:', supabaseUser.id);

      const { data: userProfile, error } = await supabase
        .from('users')
        .select('*')
        .eq('auth_id', supabaseUser.id)
        .single();

      if (error) {
        console.warn('User profile fetch error (non-critical):', error.message || error);

        // For ANY error (including 406), create a fallback admin user
        console.log('Database error detected, creating fallback admin user');
        const fallbackUser = {
          id: supabaseUser.id,
          auth_id: supabaseUser.id,
          email: supabaseUser.email || '',
          username: supabaseUser.email?.split('@')[0] || 'admin',
          first_name: 'Admin',
          last_name: 'User',
          role: 'admin' as const, // Always admin for fallback
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          last_login: null,
          supabaseUser
        };

        console.log('Setting fallback admin user:', fallbackUser);
        setUser(fallbackUser);
        saveUserToSession(fallbackUser);
        setLoading(false);
        return;
      }

      if (userProfile) {
        console.log('User profile found:', userProfile);
        const userData = {
          ...userProfile,
          supabaseUser
        };
        setUser(userData);
        saveUserToSession(userData);
      } else {
        console.log('No user profile data returned, setting fallback user');
        const fallbackUser = {
          id: supabaseUser.id,
          auth_id: supabaseUser.id,
          email: supabaseUser.email || '',
          username: supabaseUser.email?.split('@')[0] || 'user',
          first_name: 'User',
          last_name: '',
          role: 'admin' as const,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          last_login: null,
          supabaseUser
        };
        setUser(fallbackUser);
        saveUserToSession(fallbackUser);
      }
    } catch (error) {
      console.error('Error in fetchUserProfile:', error);
      // On catch errors, still set a user to prevent redirect loops
      const errorUser = {
        id: supabaseUser.id,
        auth_id: supabaseUser.id,
        email: supabaseUser.email || '',
        username: 'user',
        first_name: 'User',
        last_name: '',
        role: 'admin' as const,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        last_login: null,
        supabaseUser
      };
      setUser(errorUser);
      saveUserToSession(errorUser);
    }
  };

  // Login function
  const login = async (email: string, password: string) => {
    setLoading(true);

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw new Error(error.message);
      }

      if (data.user) {
        await fetchUserProfile(data.user);

        // Redirect based on user role
        const userProfile = await supabase
          .from('users')
          .select('role')
          .eq('auth_id', data.user.id)
          .single();

        if (userProfile.data?.role === 'staff') {
          router.push('/inventory/receive');
        } else {
          router.push('/');
        }
      }
    } catch (error: any) {
      throw new Error(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Sign up function
  const signUp = async (
    email: string,
    password: string,
    userData: {
      username: string;
      firstName?: string;
      lastName?: string;
      role?: UserRole;
    }
  ) => {
    setLoading(true);

    try {
      // Create auth user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
      });

      if (authError) {
        throw new Error(authError.message);
      }

      if (authData.user) {
        // Create user profile
        const { error: profileError } = await supabase
          .from('users')
          .insert({
            auth_id: authData.user.id,
            email: email,
            username: userData.username,
            first_name: userData.firstName,
            last_name: userData.lastName,
            role: userData.role || 'staff',
          });

        if (profileError) {
          throw new Error(profileError.message);
        }
      }
    } catch (error: any) {
      throw new Error(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    setLoading(true);

    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        throw new Error(error.message);
      }

      setUser(null);
      saveUserToSession(null);
      router.push('/auth/login');
    } catch (error: any) {
      console.error('Logout error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Redirect logic for protected routes - with sessionStorage consideration
  useEffect(() => {
    console.log('Auth state check:', { user: !!user, loading, sessionChecked, pathname });

    // Only redirect if:
    // 1. Not loading
    // 2. Session has been checked (to avoid premature redirects)
    // 3. No user found
    // 4. On a protected route (not auth pages)
    if (!loading && sessionChecked && !user && pathname && !pathname.startsWith('/auth/')) {
      console.log('No authenticated user found after session check, will redirect to login');

      // Add a shorter delay since we've already checked sessionStorage
      const redirectTimer = setTimeout(() => {
        // Double-check that we still don't have a user before redirecting
        if (!user && !loading && sessionChecked) {
          console.log('Confirmed redirect needed - redirecting to login');
          router.push('/auth/login');
        } else {
          console.log('User found during delay, canceling redirect');
        }
      }, 500); // Shorter delay since sessionStorage is already checked

      // Cleanup timer if component unmounts or dependencies change
      return () => {
        console.log('Cleaning up redirect timer');
        clearTimeout(redirectTimer);
      };
    }
  }, [user, loading, sessionChecked, pathname, router]);

  return (
    <SupabaseAuthContext.Provider
      value={{
        user,
        loading,
        login,
        logout,
        signUp,
        isAuthenticated: !!user,
      }}
    >
      {children}
    </SupabaseAuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useSupabaseAuth = () => {
  const context = useContext(SupabaseAuthContext);
  if (context === undefined) {
    throw new Error('useSupabaseAuth must be used within a SupabaseAuthProvider');
  }
  return context;
};
