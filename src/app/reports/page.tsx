'use client';

import React, { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';

interface Item {
  id: string;
  name: string;
  squareItemId?: string; // Use camelCase to match API response
  square_item_id?: string; // Keep both for compatibility
}

interface OrderItem {
  catalogObjectId: string;
  name: string;
  quantity: number;
  revenue: number;
  unitPrice: number;
}

interface Order {
  orderId: string;
  orderDate: string;
  orderState: string;
  locationId: string;
  totalMoney: number;
  items: OrderItem[];
  customerInfo: {
    customerId?: string;
    note?: string;
  };
}

interface OrderAnalysis {
  itemId: string;
  dateRange: {
    startDate: string;
    endDate: string;
    days: number;
  };
  summary: {
    totalOrders: number;
    totalQuantity: number;
    totalRevenue: number;
    averageOrderValue: number;
    averageQuantityPerOrder: number;
  };
  orders: Order[];
}

export default function ReportsPage() {
  const [items, setItems] = useState<Item[]>([]);
  const [selectedItemId, setSelectedItemId] = useState<string>('');
  const [dateRange, setDateRange] = useState<number>(7); // Default to 7 days
  const [customStartDate, setCustomStartDate] = useState<string>('');
  const [customEndDate, setCustomEndDate] = useState<string>('');
  const [useCustomDates, setUseCustomDates] = useState<boolean>(false);

  const [orderAnalysis, setOrderAnalysis] = useState<OrderAnalysis | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Load items on component mount
  useEffect(() => {
    const fetchItems = async () => {
      try {
        console.log('🟨 Fetching items for dropdown...');
        // Add cache busting timestamp
        const timestamp = Date.now();
        const response = await fetch(`/api/items/menu?limit=1000&_t=${timestamp}`);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const result = await response.json();
        console.log('✅ Items fetched:', result.data?.length || 0);

        // Show all items for debugging, then filter for Square IDs
        const allItems = result.data || [];
        console.log('🔍 All items sample:', allItems.slice(0, 3));

        // Filter items that have Square item IDs (check both field names)
        const itemsWithSquareId = allItems.filter((item: Item) =>
          item.squareItemId || item.square_item_id
        );

        // For testing: show all items if no Square items found
        if (itemsWithSquareId.length === 0 && allItems.length > 0) {
          console.log('⚠️ No items with Square IDs found, showing all items for testing');
          setItems(allItems);
        } else {
          setItems(itemsWithSquareId);
        }
        console.log('📊 Total items:', allItems.length);
        console.log('📊 Items with Square ID:', itemsWithSquareId.length);
        console.log('🔍 Sample items with Square ID:', itemsWithSquareId.slice(0, 3));
      } catch (err: any) {
        console.error('❌ Error fetching items:', err);
        setError(`Failed to load items: ${err.message}`);
      }
    };

    fetchItems();
  }, []);

  // Handle form submission
  const handleAnalyzeOrders = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedItemId) {
      setError('Please select an item to analyze');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setOrderAnalysis(null);

      console.log('🟨 Analyzing orders for item:', selectedItemId);
      console.log('🔍 Selected item details:', selectedItem);
      console.log('🔍 Item squareItemId:', selectedItem?.squareItemId);
      console.log('🔍 Item square_item_id:', selectedItem?.square_item_id);
      console.log('🔍 Item local id:', selectedItem?.id);

      // Build query parameters
      const params = new URLSearchParams({
        itemId: selectedItemId
      });

      console.log('🌐 API URL will be:', `/api/square/item-orders?${params}`);

      if (useCustomDates && customStartDate && customEndDate) {
        params.append('startDate', customStartDate);
        params.append('endDate', customEndDate);
      } else {
        params.append('days', dateRange.toString());
      }

      const response = await fetch(`/api/square/item-orders?${params}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Order analysis completed:', result);
      console.log('📊 Summary:', result.summary);
      console.log('📋 Orders count:', result.orders?.length);

      // Add debugging information
      if (result.summary?.totalOrders === 0) {
        console.log('⚠️ No orders found for item:', selectedItemId);
        console.log('🔍 Date range:', result.dateRange);
        console.log('💡 Try expanding the date range or selecting a different item');
      } else {
        console.log('🎉 SUCCESS! Found orders:', result.summary.totalOrders);
      }

      // Force state update
      setOrderAnalysis(null); // Clear first
      setTimeout(() => {
        setOrderAnalysis(result); // Then set new data
        console.log('🔄 State updated with:', result.summary.totalOrders, 'orders');
      }, 10);
    } catch (err: any) {
      console.error('❌ Error analyzing orders:', err);
      setError(err.message || 'Failed to analyze orders');
    } finally {
      setLoading(false);
    }
  };

  const selectedItem = items.find(item =>
    item.squareItemId === selectedItemId ||
    item.square_item_id === selectedItemId ||
    item.id === selectedItemId
  );

  return (
    <MainLayout requiredRoles={['admin', 'manager']}>
      <div className="mb-6 flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Item Order Reports</h1>
          <p className="text-gray-600 mt-1">
            Analyze Square order data for specific menu items
          </p>
        </div>
        <button
          onClick={() => window.location.reload()}
          className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
        >
          🔄 Refresh Items
        </button>
      </div>

      {/* Analysis Form */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">Select Item to Analyze</h2>

        <form onSubmit={handleAnalyzeOrders} className="space-y-4">
          {/* Item Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Menu Item
            </label>
            <select
              value={selectedItemId}
              onChange={(e) => setSelectedItemId(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            >
              <option value="">Select an item...</option>
              {items.map((item) => (
                <option key={item.id} value={item.squareItemId || item.square_item_id || item.id}>
                  {item.name} {(item.squareItemId || item.square_item_id) ? '(Square)' : '(Local only)'}
                </option>
              ))}
            </select>
            {items.length === 0 && (
              <div className="text-sm text-gray-500 mt-1">
                <p>No items found. This could mean:</p>
                <ul className="list-disc list-inside ml-4 mt-2">
                  <li>No items exist in the database</li>
                  <li>No items have been synced from Square catalog</li>
                  <li>Database connection issue</li>
                </ul>
                <p className="mt-2">
                  <a href="/debug/reports" className="text-blue-500 hover:underline">
                    → Go to Debug Page to troubleshoot
                  </a>
                </p>
              </div>
            )}
          </div>

          {/* Date Range Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date Range
            </label>
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="radio"
                  checked={!useCustomDates}
                  onChange={() => setUseCustomDates(false)}
                  className="mr-2"
                />
                <span className="text-sm">Preset range:</span>
                <select
                  value={dateRange}
                  onChange={(e) => setDateRange(parseInt(e.target.value))}
                  disabled={useCustomDates}
                  className="ml-2 px-2 py-1 border border-gray-300 rounded text-sm disabled:bg-gray-100"
                >
                  <option value={1}>Last 1 day</option>
                  <option value={3}>Last 3 days</option>
                  <option value={7}>Last 7 days</option>
                  <option value={14}>Last 14 days</option>
                  <option value={30}>Last 30 days</option>
                </select>
              </label>

              <label className="flex items-center">
                <input
                  type="radio"
                  checked={useCustomDates}
                  onChange={() => setUseCustomDates(true)}
                  className="mr-2"
                />
                <span className="text-sm">Custom range:</span>
              </label>

              {useCustomDates && (
                <div className="ml-6 flex space-x-3">
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">Start Date</label>
                    <input
                      type="date"
                      value={customStartDate}
                      onChange={(e) => setCustomStartDate(e.target.value)}
                      className="px-2 py-1 border border-gray-300 rounded text-sm"
                      required={useCustomDates}
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">End Date</label>
                    <input
                      type="date"
                      value={customEndDate}
                      onChange={(e) => setCustomEndDate(e.target.value)}
                      className="px-2 py-1 border border-gray-300 rounded text-sm"
                      required={useCustomDates}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={loading || !selectedItemId}
            className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Analyzing Orders...' : 'Analyze Orders'}
          </button>

          {/* Quick Test Button */}
          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-800 mb-2">
              <strong>Quick Test:</strong> Test with known working item ID
            </p>
            <button
              type="button"
              onClick={() => {
                setSelectedItemId('C4XTLDOHSNQHGFDYW66NFDTI');
                // Trigger form submission after state update
                setTimeout(() => {
                  const form = document.querySelector('form');
                  if (form) {
                    const event = new Event('submit', { bubbles: true, cancelable: true });
                    form.dispatchEvent(event);
                  }
                }, 100);
              }}
              disabled={loading}
              className="px-3 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600 disabled:opacity-50"
            >
              Test with C4XTLDOHSNQHGFDYW66NFDTI (164 orders)
            </button>
          </div>
        </form>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-6">
          <h3 className="font-medium">Error</h3>
          <p>{error}</p>
          <button
            onClick={() => setError(null)}
            className="mt-2 px-3 py-1 bg-red-100 text-red-800 rounded hover:bg-red-200"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        </div>
      )}

      {/* Results Display */}
      {orderAnalysis && !loading && (
        <div className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-white rounded-lg shadow-sm p-4">
              <h3 className="text-sm font-medium text-gray-500">Total Orders</h3>
              <p className="text-2xl font-bold text-blue-600">{orderAnalysis.summary.totalOrders}</p>
            </div>
            <div className="bg-white rounded-lg shadow-sm p-4">
              <h3 className="text-sm font-medium text-gray-500">Total Quantity</h3>
              <p className="text-2xl font-bold text-green-600">{orderAnalysis.summary.totalQuantity}</p>
            </div>
            <div className="bg-white rounded-lg shadow-sm p-4">
              <h3 className="text-sm font-medium text-gray-500">Total Revenue</h3>
              <p className="text-2xl font-bold text-orange-600">${orderAnalysis.summary.totalRevenue.toFixed(2)}</p>
            </div>
            <div className="bg-white rounded-lg shadow-sm p-4">
              <h3 className="text-sm font-medium text-gray-500">Avg Order Value</h3>
              <p className="text-2xl font-bold text-purple-600">${orderAnalysis.summary.averageOrderValue.toFixed(2)}</p>
            </div>
          </div>

          {/* Analysis Header */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-2">
              Order Analysis: {selectedItem?.name}
            </h2>
            <p className="text-gray-600">
              {new Date(orderAnalysis.dateRange.startDate).toLocaleDateString()} - {new Date(orderAnalysis.dateRange.endDate).toLocaleDateString()}
              ({orderAnalysis.dateRange.days} days)
            </p>
            <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Average quantity per order:</span>
                <span className="ml-2 font-medium">{orderAnalysis.summary.averageQuantityPerOrder.toFixed(1)}</span>
              </div>
              <div>
                <span className="text-gray-500">Square Item ID:</span>
                <span className="ml-2 font-mono text-xs">{orderAnalysis.itemId}</span>
              </div>
            </div>
          </div>

          {/* Orders Table */}
          {orderAnalysis.orders.length > 0 ? (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">
                Individual Orders ({orderAnalysis.orders.length})
              </h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Order Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Order ID
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Item Revenue
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unit Price
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Order Total
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {orderAnalysis.orders.map((order) => (
                      <tr key={order.orderId} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(order.orderDate).toLocaleDateString()}
                          <div className="text-xs text-gray-500">
                            {new Date(order.orderDate).toLocaleTimeString()}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                          {order.orderId.substring(0, 8)}...
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <span className="font-medium">{order.items[0]?.quantity || 0}</span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <span className="font-medium text-green-600">
                            ${order.items[0]?.revenue?.toFixed(2) || '0.00'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          ${order.items[0]?.unitPrice?.toFixed(2) || '0.00'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${order.totalMoney.toFixed(2)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            order.orderState === 'COMPLETED'
                              ? 'bg-green-100 text-green-800'
                              : order.orderState === 'OPEN'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {order.orderState}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="text-center py-8">
                <div className="text-gray-400 mb-4">
                  <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Orders Found</h3>
                <p className="text-gray-500">
                  No orders containing "{selectedItem?.name}" were found in the selected date range.
                </p>
                <p className="text-sm text-gray-400 mt-2">
                  Try expanding the date range or selecting a different item.
                </p>
              </div>
            </div>
          )}
        </div>
      )}
    </MainLayout>
  );
}
