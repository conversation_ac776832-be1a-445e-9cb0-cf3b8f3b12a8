'use client'

import { useSession, signOut } from 'next-auth/react'
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext'
import MainLayout from '@/components/layout/MainLayout'

export default function TestSignOut() {
  const { data: session } = useSession()
  const { user, logout, isAuthenticated } = useSupabaseAuth()

  const handleNextAuthSignOut = () => {
    signOut({ callbackUrl: '/auth/nextauth-login' })
  }

  const handleSupabaseSignOut = () => {
    logout()
  }

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Sign Out Test Page</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* NextAuth Session */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">NextAuth Session</h2>
            {session ? (
              <div className="space-y-4">
                <div className="bg-green-50 rounded-lg p-4">
                  <h3 className="font-medium text-green-900 mb-2">✅ NextAuth Active</h3>
                  <div className="text-sm text-green-800 space-y-1">
                    <p><strong>Email:</strong> {session.user?.email}</p>
                    <p><strong>Username:</strong> {session.user?.username}</p>
                    <p><strong>Role:</strong> {session.user?.role}</p>
                  </div>
                </div>
                <button
                  onClick={handleNextAuthSignOut}
                  className="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                >
                  Sign Out (NextAuth)
                </button>
              </div>
            ) : (
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-gray-600">❌ No NextAuth session</p>
                <a
                  href="/auth/nextauth-login"
                  className="mt-2 inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Login with NextAuth
                </a>
              </div>
            )}
          </div>

          {/* Supabase Session */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Supabase Session</h2>
            {isAuthenticated && user ? (
              <div className="space-y-4">
                <div className="bg-blue-50 rounded-lg p-4">
                  <h3 className="font-medium text-blue-900 mb-2">✅ Supabase Active</h3>
                  <div className="text-sm text-blue-800 space-y-1">
                    <p><strong>Email:</strong> {user.email}</p>
                    <p><strong>Username:</strong> {user.username}</p>
                    <p><strong>Role:</strong> {user.role}</p>
                  </div>
                </div>
                <button
                  onClick={handleSupabaseSignOut}
                  className="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                >
                  Sign Out (Supabase)
                </button>
              </div>
            ) : (
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-gray-600">❌ No Supabase session</p>
                <a
                  href="/auth/login"
                  className="mt-2 inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Login with Supabase
                </a>
              </div>
            )}
          </div>
        </div>

        {/* Header Sign Out Test */}
        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Header Sign Out Test</h2>
          <div className="bg-yellow-50 rounded-lg p-4">
            <h3 className="font-medium text-yellow-900 mb-2">🔍 Test Instructions</h3>
            <div className="text-sm text-yellow-800 space-y-2">
              <p>1. Look at the header (top of the page)</p>
              <p>2. Click on the user icon in the top right</p>
              <p>3. Click "Sign out" from the dropdown menu</p>
              <p>4. You should be redirected to the appropriate login page</p>
            </div>
          </div>
          
          <div className="mt-4 text-sm text-gray-600">
            <p><strong>Current Status:</strong></p>
            <ul className="list-disc list-inside space-y-1 mt-2">
              <li>NextAuth Session: {session ? '✅ Active' : '❌ None'}</li>
              <li>Supabase Session: {isAuthenticated ? '✅ Active' : '❌ None'}</li>
              <li>Header will use: {session ? 'NextAuth sign out' : 'Supabase sign out'}</li>
            </ul>
          </div>
        </div>

        {/* Navigation Links */}
        <div className="mt-8 bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Navigation</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a
              href="/dashboard-nextauth"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-center"
            >
              NextAuth Dashboard
            </a>
            <a
              href="/dashboard-light"
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-center"
            >
              Supabase Dashboard
            </a>
            <a
              href="/test-nextauth"
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 text-center"
            >
              NextAuth Test
            </a>
            <a
              href="/auth/nextauth-login"
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-center"
            >
              NextAuth Login
            </a>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
