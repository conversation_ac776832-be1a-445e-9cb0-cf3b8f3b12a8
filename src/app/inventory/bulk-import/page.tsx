'use client';

import React, { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { vietnameseRestaurantInventory } from '@/data/vietnamese-restaurant-inventory';

export default function BulkImportPage() {
  const [importing, setImporting] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [importResults, setImportResults] = useState<any>(null);

  const handleBulkImport = async () => {
    try {
      setImporting(true);
      setError(null);
      setSuccess(null);
      setImportResults(null);

      console.log('🚀 Starting bulk import of', vietnameseRestaurantInventory.length, 'items...');

      const response = await fetch('/api/inventory/bulk-import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          items: vietnameseRestaurantInventory
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}`);
      }

      console.log('✅ Bulk import successful:', data);
      setSuccess(data.message);
      setImportResults(data);

    } catch (err: any) {
      console.error('❌ Bulk import failed:', err);
      setError(err.message || 'Failed to import inventory items');
    } finally {
      setImporting(false);
    }
  };

  const handleClearInventory = async () => {
    if (!confirm('⚠️ This will DELETE ALL existing inventory items. Are you sure?')) {
      return;
    }

    try {
      setImporting(true);
      setError(null);
      setSuccess(null);

      const response = await fetch('/api/inventory/clear-all', {
        method: 'DELETE',
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || `HTTP ${response.status}`);
      }

      setSuccess('✅ All inventory items cleared successfully');

    } catch (err: any) {
      console.error('❌ Clear inventory failed:', err);
      setError(err.message || 'Failed to clear inventory items');
    } finally {
      setImporting(false);
    }
  };

  // Group items by storage location for preview
  const itemsByStorage = vietnameseRestaurantInventory.reduce((acc, item) => {
    if (!acc[item.storage]) {
      acc[item.storage] = [];
    }
    acc[item.storage].push(item);
    return acc;
  }, {} as Record<string, typeof vietnameseRestaurantInventory>);

  return (
    <MainLayout>
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-gray-800 mb-2">Vietnamese Restaurant Inventory Setup</h2>
        <p className="text-gray-600">
          Import all {vietnameseRestaurantInventory.length} inventory items for your Vietnamese restaurant, 
          organized by storage location with Vietnamese names and appropriate units.
        </p>
      </div>

      {/* Action Buttons */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <h3 className="text-lg font-medium mb-4">Import Actions</h3>
        <div className="flex space-x-4">
          <button
            onClick={handleBulkImport}
            disabled={importing}
            className="px-6 py-3 bg-blue-500 text-white rounded-md text-sm font-medium hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {importing ? 'Importing...' : `Import All ${vietnameseRestaurantInventory.length} Items`}
          </button>
          
          <button
            onClick={handleClearInventory}
            disabled={importing}
            className="px-6 py-3 bg-red-500 text-white rounded-md text-sm font-medium hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {importing ? 'Clearing...' : 'Clear All Inventory'}
          </button>
        </div>
      </div>

      {/* Status Messages */}
      {success && (
        <div className="bg-green-50 border border-green-200 text-green-800 rounded-md p-4 mb-6">
          {success}
          {importResults && (
            <div className="mt-2 text-sm">
              <p>✅ Successfully imported {importResults.items?.length || 0} items</p>
            </div>
          )}
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-6">
          {error}
        </div>
      )}

      {/* Preview of Items to Import */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-medium mb-4">Preview: Items to Import</h3>
        
        <div className="space-y-6">
          {Object.entries(itemsByStorage).map(([storage, items]) => (
            <div key={storage} className="border border-gray-200 rounded-md p-4">
              <h4 className="font-medium text-gray-900 mb-3">
                {storage.replace(/_/g, ' ')} ({items.length} items)
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {items.slice(0, 6).map((item, index) => (
                  <div key={index} className="text-sm">
                    <div className="font-medium text-gray-900">{item.name}</div>
                    <div className="text-gray-500">{item.vietnamese_name}</div>
                    <div className="text-gray-400">{item.unit} • {item.category}</div>
                  </div>
                ))}
                {items.length > 6 && (
                  <div className="text-sm text-gray-500 italic">
                    ... and {items.length - 6} more items
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 p-4 bg-gray-50 rounded-md">
          <h4 className="font-medium text-gray-900 mb-2">Summary</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="font-medium">Total Items</div>
              <div className="text-gray-600">{vietnameseRestaurantInventory.length}</div>
            </div>
            <div>
              <div className="font-medium">Storage Locations</div>
              <div className="text-gray-600">{Object.keys(itemsByStorage).length}</div>
            </div>
            <div>
              <div className="font-medium">Categories</div>
              <div className="text-gray-600">
                {new Set(vietnameseRestaurantInventory.map(item => item.category)).size}
              </div>
            </div>
            <div>
              <div className="font-medium">Units</div>
              <div className="text-gray-600">
                {new Set(vietnameseRestaurantInventory.map(item => item.unit)).size}
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
