'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import InventoryItemForm from '@/components/inventory/InventoryItemForm';
import { InventoryItem } from '@/types/inventory';
import { inventoryItemsApi } from '@/services/apiService';
import Link from 'next/link';

export default function EditInventoryItemPage() {
  const params = useParams();
  const [inventoryItem, setInventoryItem] = useState<InventoryItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchInventoryItem = async () => {
      try {
        setLoading(true);
        const id = params.id as string;
        const data = await inventoryItemsApi.getById(id);
        setInventoryItem(data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching inventory item:', err);
        setError('Failed to load inventory item. Please try again.');
        setLoading(false);
      }
    };

    fetchInventoryItem();
  }, [params.id]);

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!inventoryItem) {
    return (
      <MainLayout>
        <div className="text-center py-10">
          <h2 className="text-xl font-semibold text-gray-800">Inventory item not found</h2>
          <p className="mt-2 text-gray-600">The inventory item you're looking for doesn't exist or has been removed.</p>
          <Link href="/inventory" className="mt-4 inline-block text-blue-600 hover:underline">
            Back to Inventory Items
          </Link>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold text-gray-800 mb-6">Edit Inventory Item</h1>
        
        <div className="bg-white shadow-md rounded-lg p-6">
          <InventoryItemForm initialData={inventoryItem} isEditMode={true} />
        </div>
      </div>
    </MainLayout>
  );
}
