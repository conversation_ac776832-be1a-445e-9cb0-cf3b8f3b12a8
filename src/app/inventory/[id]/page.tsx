'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import { InventoryItem, InventoryTransaction } from '@/types/inventory';
import { inventoryItemsApi, transactionsApi } from '@/services/apiService';
import Link from 'next/link';

export default function InventoryItemDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [inventoryItem, setInventoryItem] = useState<InventoryItem | null>(null);
  const [transactions, setTransactions] = useState<InventoryTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const id = params.id as string;

        // Fetch inventory item details
        const itemData = await inventoryItemsApi.getById(id);
        setInventoryItem(itemData);

        // Fetch transactions for this inventory item
        const transactionData = await transactionsApi.getAll({ inventoryItemId: id });
        setTransactions(transactionData);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching inventory item details:', err);
        setError('Failed to load inventory item details. Please try again.');
        setLoading(false);
      }
    };

    fetchData();
  }, [params.id]);

  const handleDelete = async () => {
    if (!inventoryItem) return;

    if (window.confirm(`Are you sure you want to delete ${inventoryItem.name}?`)) {
      try {
        await inventoryItemsApi.delete(inventoryItem.id);
        router.push('/inventory');
      } catch (err) {
        console.error('Error deleting inventory item:', err);
        setError('Failed to delete inventory item. Please try again.');
      }
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!inventoryItem) {
    return (
      <MainLayout>
        <div className="text-center py-10">
          <h2 className="text-xl font-semibold text-gray-800">Inventory item not found</h2>
          <p className="mt-2 text-gray-600">The inventory item you're looking for doesn't exist or has been removed.</p>
          <Link href="/inventory" className="mt-4 inline-block text-blue-600 hover:underline">
            Back to Inventory Items
          </Link>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800">{inventoryItem.name}</h1>
          <div className="flex space-x-3">
            <Link
              href={`/inventory/${inventoryItem.id}/edit`}
              className="bg-blue-500 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Edit
            </Link>
            <Link
              href={`/inventory/receive?inventoryItemId=${inventoryItem.id}`}
              className="bg-green-500 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Add Stock
            </Link>
            <button
              onClick={handleDelete}
              className="bg-red-500 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Delete
            </button>
          </div>
        </div>

        {/* Inventory Item Details */}
        <div className="bg-white shadow-md rounded-lg p-6 mb-6">
          <h2 className="text-lg font-medium mb-4">Inventory Item Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">Name</p>
              <p className="text-base font-medium">{inventoryItem.name}</p>
            </div>
            {inventoryItem.vietnameseName && (
              <div>
                <p className="text-sm text-gray-500">Vietnamese Name</p>
                <p className="text-base font-medium">{inventoryItem.vietnameseName}</p>
              </div>
            )}
            <div>
              <p className="text-sm text-gray-500">Quantity</p>
              <p className="text-base font-medium">
                {inventoryItem.quantity} {inventoryItem.unit}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Cost Per Unit</p>
              <p className="text-base font-medium">${inventoryItem.costPerUnit.toFixed(2)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Total Value</p>
              <p className="text-base font-medium">
                ${(inventoryItem.quantity * inventoryItem.costPerUnit).toFixed(2)}
              </p>
            </div>
            {inventoryItem.minThreshold !== undefined && (
              <div>
                <p className="text-sm text-gray-500">Minimum Threshold</p>
                <p className="text-base font-medium">
                  {inventoryItem.minThreshold} {inventoryItem.unit}
                </p>
              </div>
            )}
            {inventoryItem.category && (
              <div>
                <p className="text-sm text-gray-500">Category</p>
                <p className="text-base font-medium">{inventoryItem.category}</p>
              </div>
            )}
            {inventoryItem.storage && (
              <div>
                <p className="text-sm text-gray-500">Storage Location</p>
                <p className="text-base font-medium">{inventoryItem.storage}</p>
              </div>
            )}
            {inventoryItem.lastRestockDate && (
              <div>
                <p className="text-sm text-gray-500">Last Restock Date</p>
                <p className="text-base font-medium">
                  {new Date(inventoryItem.lastRestockDate).toLocaleDateString()}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Transactions */}
        <div className="bg-white shadow-md rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-medium">Recent Transactions</h2>
            <Link
              href={`/inventory/receive?inventoryItemId=${inventoryItem.id}`}
              className="bg-green-500 hover:bg-green-700 text-white px-3 py-1 rounded-md text-sm font-medium"
            >
              Add Stock
            </Link>
          </div>

          {transactions.length === 0 ? (
            <p className="text-gray-500 text-center py-4">No transactions found for this inventory item.</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Notes
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {transactions.map((transaction) => (
                    <tr key={transaction.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(transaction.date).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          transaction.type === 'purchase' ? 'bg-green-100 text-green-800' :
                          transaction.type === 'usage' ? 'bg-blue-100 text-blue-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {transaction.quantity} {inventoryItem.unit}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500">
                        {transaction.notes || '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
