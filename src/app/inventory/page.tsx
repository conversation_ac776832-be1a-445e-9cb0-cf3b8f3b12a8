'use client';

import React, { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { InventoryItem } from '@/types/inventory';
import { inventoryItemsApi } from '@/services/apiService';
import Link from 'next/link';

export default function InventoryItemsPage() {
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<{
    category?: string;
    belowThreshold?: boolean;
    search?: string;
  }>({});
  const [searchTerm, setSearchTerm] = useState('');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const pageSize = 20;

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setFilter(prev => ({ ...prev, search: searchTerm || undefined }));
      setCurrentPage(1); // Reset to first page when search changes
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Load inventory items when filter or page changes
  useEffect(() => {
    const fetchInventoryItems = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('🔄 Fetching inventory items with filters:', {
          category: filter.category,
          belowThreshold: filter.belowThreshold,
          search: filter.search,
          page: currentPage,
          limit: pageSize
        });

        const result = await inventoryItemsApi.getAll({
          category: filter.category,
          belowThreshold: filter.belowThreshold,
          search: filter.search,
          page: currentPage,
          limit: pageSize
        });

        console.log('✅ Successfully fetched inventory items:', result.data?.length || 0);
        console.log('📊 Pagination info:', result.pagination);

        setInventoryItems(result.data || []);

        // Update pagination state
        if (result.pagination) {
          setTotalPages(result.pagination.totalPages || 1);
          setTotalItems(result.pagination.total || 0);
          setHasMore(result.pagination.hasMore || false);
        }
      } catch (error) {
        console.error('❌ Error fetching inventory items:', error);
        setError(error instanceof Error ? error.message : 'Failed to load inventory items');
        setInventoryItems([]);
      } finally {
        setLoading(false);
      }
    };

    fetchInventoryItems();
  }, [filter, currentPage]);

  // Handle filter changes
  const handleFilterChange = (key: string, value: any) => {
    setFilter((prev: any) => ({
      ...prev,
      [key]: value
    }));
    setCurrentPage(1); // Reset to first page when filter changes
  };

  // Handle page changes
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Handle inventory item deletion
  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this inventory item?')) {
      try {
        console.log('🗑️ Attempting to delete inventory item:', id);
        const result = await inventoryItemsApi.delete(id);
        console.log('🗑️ Delete result:', result);

        if (result.success) {
          console.log('✅ Delete successful, updating UI');
          setInventoryItems(inventoryItems.filter(item => item.id !== id));
        } else {
          console.error('❌ Delete failed - result.success is false');
          alert('Failed to delete inventory item. Please try again.');
        }
      } catch (error) {
        console.error('❌ Error deleting inventory item:', error);
        alert(`Failed to delete inventory item: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  };

  return (
    <MainLayout>
      <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Inventory Items</h1>
          {!loading && (
            <p className="text-sm text-gray-600 mt-1">
              {totalItems > 0 ? (
                <>Showing {((currentPage - 1) * pageSize) + 1}-{Math.min(currentPage * pageSize, totalItems)} of {totalItems} items</>
              ) : (
                'No items found'
              )}
            </p>
          )}
        </div>
        <div className="flex space-x-4 mt-4 sm:mt-0">
          <Link
            href="/inventory/receive"
            className="bg-green-500 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            Receive Inventory
          </Link>
          <Link
            href="/inventory/new"
            className="bg-yellow-500 hover:bg-yellow-700 text-black px-4 py-2 rounded-md text-sm font-medium"
          >
            Add New Inventory Item
          </Link>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white shadow-md rounded-lg p-4 mb-6">
        <h2 className="text-lg font-medium mb-4">Filters</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <input
              type="text"
              id="search"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by name or category"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
              Category
            </label>
            <select
              id="category"
              value={filter.category || ''}
              onChange={(e) => handleFilterChange('category', e.target.value || undefined)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Categories</option>
              <option value="proteins">Proteins</option>
              <option value="vegetables">Vegetables</option>
              <option value="herbs">Herbs</option>
              <option value="seafood">Seafood</option>
              <option value="fruits">Fruits</option>
              <option value="sauces">Sauces</option>
              <option value="condiments">Condiments</option>
              <option value="beverages">Beverages</option>
              <option value="frozen_foods">Frozen Foods</option>
              <option value="dry_goods">Dry Goods</option>
              <option value="packaging">Packaging</option>
              <option value="chemicals">Chemicals</option>
              <option value="fuel">Fuel</option>
              <option value="supplies">Supplies</option>
            </select>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="belowThreshold"
              checked={!!filter.belowThreshold}
              onChange={(e) => handleFilterChange('belowThreshold', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="belowThreshold" className="ml-2 block text-sm text-gray-700">
              Show only items below threshold
            </label>
          </div>
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-6">
          <h3 className="font-medium">Error Loading Inventory</h3>
          <p>{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-2 px-3 py-1 bg-red-100 text-red-800 rounded hover:bg-red-200"
          >
            Retry
          </button>
        </div>
      )}

      {/* Inventory Items Table */}
      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Quantity
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Unit
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan={5} className="px-6 py-8 text-center">
                    <div className="flex justify-center items-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                      <span className="ml-2 text-gray-500">Loading inventory items...</span>
                    </div>
                  </td>
                </tr>
              ) : inventoryItems.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-6 py-8 text-center text-sm text-gray-500">
                    {filter.search || filter.category || filter.belowThreshold ?
                      'No inventory items match your filters' :
                      'No inventory items found. Add some items to get started.'
                    }
                  </td>
                </tr>
              ) : (
                inventoryItems.map((item) => (
                  <tr key={item.id} className={item.quantity <= (item.minThreshold || 0) ? 'bg-red-50' : ''}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{item.name}</div>
                      {item.vietnameseName && (
                        <div className="text-sm text-gray-500">{item.vietnameseName}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.category}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{item.quantity}</div>
                      {item.minThreshold !== undefined && (
                        <div className="text-xs text-gray-500">Min: {item.minThreshold}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.unit}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <Link
                        href={`/inventory/${item.id}`}
                        className="text-blue-600 hover:text-blue-900 mr-3"
                      >
                        View
                      </Link>
                      <Link
                        href={`/inventory/${item.id}/edit`}
                        className="text-indigo-600 hover:text-indigo-900 mr-3"
                      >
                        Edit
                      </Link>
                      <button
                        onClick={() => handleDelete(item.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination Controls */}
      {!loading && !error && totalPages > 1 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6 rounded-lg shadow-sm">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing page <span className="font-medium">{currentPage}</span> of{' '}
                <span className="font-medium">{totalPages}</span>
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>

                {/* Page numbers */}
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        pageNum === currentPage
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}

                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </MainLayout>
  );
}
