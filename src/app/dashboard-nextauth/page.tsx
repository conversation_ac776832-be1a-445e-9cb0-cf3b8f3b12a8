'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import NextAuthMainLayout from '@/components/layout/NextAuthMainLayout'
import Link from 'next/link'

export default function NextAuthDashboard() {
  const { data: session } = useSession()
  const [totalItems, setTotalItems] = useState(0)
  const [lowStockCount, setLowStockCount] = useState(0)
  const [loading, setLoading] = useState(true)

  // Fast data loading with short timeout
  useEffect(() => {
    const loadData = async () => {
      try {
        // Quick timeout to prevent hanging
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 2000) // 2 second timeout

        const response = await fetch('/api/inventory/items?limit=100', {
          signal: controller.signal
        })

        clearTimeout(timeoutId)

        if (response.ok) {
          const data = await response.json()
          setTotalItems(data.items?.length || 0)
          setLowStockCount(data.items?.filter((item: any) => (item.current_stock || 0) <= (item.minimum_stock || 0)).length || 0)
        }
      } catch (error) {
        console.warn('Dashboard data loading failed, using defaults:', error)
        // Use defaults - don't show error to user
        setTotalItems(0)
        setLowStockCount(0)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  const navigationItems = [
    {
      name: 'Inventory Items',
      href: '/inventory',
      icon: '📦',
      description: 'Manage your inventory',
      color: 'bg-blue-50 hover:bg-blue-100 border-blue-200'
    },
    {
      name: 'Menu Items',
      href: '/items',
      icon: '🍜',
      description: 'Manage menu items',
      color: 'bg-green-50 hover:bg-green-100 border-green-200'
    },
    {
      name: 'History',
      href: '/transactions',
      icon: '📋',
      description: 'View transaction history',
      color: 'bg-purple-50 hover:bg-purple-100 border-purple-200'
    },
    {
      name: 'Reports',
      href: '/reports',
      icon: '📊',
      description: 'View reports and analytics',
      color: 'bg-orange-50 hover:bg-orange-100 border-orange-200'
    }
  ]

  return (
    <NextAuthMainLayout>
      <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8 py-4 sm:py-8">
        {/* Header */}
        <div className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-2">
            Welcome back, {session?.user?.firstName || session?.user?.username || 'User'}!
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 mb-6 sm:mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-blue-600 font-semibold">📦</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Items</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {loading ? '...' : totalItems}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                  <span className="text-red-600 font-semibold">⚠️</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Low Stock</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {loading ? '...' : lowStockCount}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Navigation */}
        <div className="mb-6 sm:mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {navigationItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`${item.color} border rounded-lg p-4 transition-colors duration-200 block`}
              >
                <div className="flex items-center">
                  <span className="text-2xl mr-3">{item.icon}</span>
                  <div>
                    <h3 className="font-medium text-gray-900">{item.name}</h3>
                    <p className="text-sm text-gray-600">{item.description}</p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Session Info */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-2">Session Info</h3>
          <div className="text-xs text-gray-600 space-y-1">
            <p><strong>User:</strong> {session?.user?.username}</p>
            <p><strong>Role:</strong> {session?.user?.role}</p>
            <p><strong>Email:</strong> {session?.user?.email}</p>
          </div>
        </div>
      </div>
    </NextAuthMainLayout>
  )
}
