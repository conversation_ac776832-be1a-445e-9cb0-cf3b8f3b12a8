'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import { itemsApi, inventoryItemsApi } from '@/services/apiService';
import { Item, ItemVariant, ItemIngredient, InventoryItem, NonInventoryItem } from '@/types/inventory';

export default function NewItemFullEditPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [loadingInventoryItems, setLoadingInventoryItems] = useState(false);

  // Basic item details
  const [name, setName] = useState('');
  const [vietnameseName, setVietnameseName] = useState('');
  const [category, setCategory] = useState('');
  const [description, setDescription] = useState('');
  const [preparationTime, setPreparationTime] = useState<number | ''>('');

  // Variants
  const [variants, setVariants] = useState<ItemVariant[]>([
    {
      id: `variant-${Date.now()}`,
      type: 'dine_in',
      name: 'Dine In',
      sellingPrice: 0,
      additionalIngredients: [],
      nonInventoryItems: []
    }
  ]);

  // Base ingredients
  const [baseIngredients, setBaseIngredients] = useState<ItemIngredient[]>([]);

  // Fetch inventory items for ingredient selection
  const fetchInventoryItems = async () => {
    if (inventoryItems.length > 0) return; // Already loaded

    try {
      setLoadingInventoryItems(true);
      // Fetch ALL inventory items for ingredient selection (no pagination)
      const result = await inventoryItemsApi.getAll({ limit: 1000 });
      console.log('📦 Loaded inventory items for ingredients:', result.data?.length || 0);
      setInventoryItems(result.data || []);
    } catch (err: any) {
      console.error('Error fetching inventory items:', err);
      setError(err.message || 'Failed to load inventory items');
      setInventoryItems([]);
    } finally {
      setLoadingInventoryItems(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!name) {
      setError('Item name is required');
      return;
    }

    // Validate variants - only if they exist
    // Note: Variants are optional for manual items, they come from Square sync
    for (const variant of variants) {
      if (!variant.name) {
        setError('All variants must have a name');
        return;
      }
    }

    // Validate base ingredients
    for (let i = 0; i < baseIngredients.length; i++) {
      const ingredient = baseIngredients[i];
      if (!ingredient.inventoryItemId) {
        setError(`Base ingredient ${i + 1}: Please select an ingredient`);
        return;
      }
      if (!ingredient.quantityPerServing || ingredient.quantityPerServing <= 0) {
        setError(`Base ingredient ${i + 1}: Quantity must be greater than 0`);
        return;
      }
    }

    // Validate variant ingredients
    for (const variant of variants) {
      for (let i = 0; i < variant.additionalIngredients.length; i++) {
        const ingredient = variant.additionalIngredients[i];
        if (!ingredient.inventoryItemId) {
          setError(`${variant.name} ingredient ${i + 1}: Please select an ingredient`);
          return;
        }
        if (!ingredient.quantityPerServing || ingredient.quantityPerServing <= 0) {
          setError(`${variant.name} ingredient ${i + 1}: Quantity must be greater than 0`);
          return;
        }
      }
    }

    try {
      setLoading(true);
      setError(null);

      // Create new item
      const newItem: Omit<Item, 'id'> = {
        name,
        vietnameseName: vietnameseName || undefined,
        category: category || undefined,
        description: description || undefined,
        preparationTime: preparationTime !== '' ? Number(preparationTime) : undefined,
        baseIngredients,
        variants
      };

      const createdItem = await itemsApi.add(newItem);
      router.push(`/items/${createdItem.id}/full-edit`);
    } catch (err: any) {
      console.error('Error creating item:', err);
      setError(err.message || 'Failed to create item');
      setLoading(false);
    }
  };

  // Note: Variant functions removed - variants now come from Square sync only

  // Add a base ingredient
  const addBaseIngredient = () => {
    if (inventoryItems.length === 0) {
      fetchInventoryItems();
    }

    setBaseIngredients([
      ...baseIngredients,
      {
        inventoryItemId: '',
        quantityPerServing: 0
      }
    ]);
  };

  // Remove a base ingredient
  const removeBaseIngredient = (index: number) => {
    setBaseIngredients(baseIngredients.filter((_, i) => i !== index));
  };

  // Update a base ingredient
  const updateBaseIngredient = (index: number, field: keyof ItemIngredient, value: any) => {
    setBaseIngredients(
      baseIngredients.map((ingredient, i) => {
        if (i === index) {
          return { ...ingredient, [field]: value };
        }
        return ingredient;
      })
    );
  };

  return (
    <MainLayout>
      <div className="mb-6 flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-800">Create New Item</h2>
        <button
          onClick={() => router.push('/items')}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          Cancel
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-6">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium mb-4">Basic Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Name *
              </label>
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label htmlFor="vietnameseName" className="block text-sm font-medium text-gray-700 mb-1">
                Vietnamese Name
              </label>
              <input
                type="text"
                id="vietnameseName"
                value={vietnameseName}
                onChange={(e) => setVietnameseName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                id="category"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select Category</option>
                <option value="main">Main Dishes</option>
                <option value="appetizer">Appetizers</option>
                <option value="dessert">Desserts</option>
                <option value="beverage">Beverages</option>
              </select>
            </div>

            <div>
              <label htmlFor="preparationTime" className="block text-sm font-medium text-gray-700 mb-1">
                Preparation Time (minutes)
              </label>
              <input
                type="number"
                id="preparationTime"
                value={preparationTime}
                onChange={(e) => setPreparationTime(e.target.value ? Number(e.target.value) : '')}
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div className="md:col-span-2">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              ></textarea>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Base Ingredients</h3>
            <button
              type="button"
              onClick={addBaseIngredient}
              className="px-3 py-1 bg-green-500 text-white rounded-md text-sm font-medium hover:bg-green-600"
            >
              Add Ingredient
            </button>
          </div>

          {baseIngredients.length === 0 ? (
            <p className="text-gray-500">No base ingredients added yet.</p>
          ) : (
            <div className="space-y-4">
              {baseIngredients.map((ingredient, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Ingredient
                    </label>
                    <select
                      value={ingredient.inventoryItemId}
                      onChange={(e) => updateBaseIngredient(index, 'inventoryItemId', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      onClick={fetchInventoryItems}
                    >
                      <option value="">Select Ingredient</option>
                      {loadingInventoryItems ? (
                        <option value="" disabled>Loading...</option>
                      ) : (
                        inventoryItems.map((item) => (
                          <option key={item.id} value={item.id}>
                            {item.name} ({item.unit})
                          </option>
                        ))
                      )}
                    </select>
                  </div>
                  <div className="w-32">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Quantity
                    </label>
                    <input
                      type="number"
                      value={ingredient.quantityPerServing}
                      onChange={(e) => updateBaseIngredient(index, 'quantityPerServing', Number(e.target.value))}
                      min="0"
                      step="0.01"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="flex items-end">
                    <button
                      type="button"
                      onClick={() => removeBaseIngredient(index)}
                      className="px-3 py-2 text-red-600 hover:text-red-800"
                    >
                      Remove
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded-md text-sm font-medium hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? 'Creating...' : 'Create Item'}
          </button>
        </div>
      </form>
    </MainLayout>
  );
}
