'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import { itemsApi, inventoryItemsApi } from '@/services/apiService';
import { Item, ItemVariant, ItemIngredient, InventoryItem, NonInventoryItem } from '@/types/inventory';

interface FullEditItemPageProps {
  params: {
    id: string;
  };
}

// Client component that handles the full item edit form
function FullEditItemClient({ id }: { id: string }) {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [changesBanner, setChangesBanner] = useState<string | null>(null);
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [cameFromDetails, setCameFromDetails] = useState(false);
  const [loadingInventoryItems, setLoadingInventoryItems] = useState(false);

  // Basic item details
  const [name, setName] = useState('');
  const [vietnameseName, setVietnameseName] = useState('');
  const [category, setCategory] = useState('');
  const [description, setDescription] = useState('');
  const [preparationTime, setPreparationTime] = useState<number | ''>('');

  // Variants
  const [variants, setVariants] = useState<ItemVariant[]>([]);

  // Base ingredients
  const [baseIngredients, setBaseIngredients] = useState<ItemIngredient[]>([]);

  // Check if user came from details page
  useEffect(() => {
    // Check if the previous page was the details page for this item
    const referrer = document.referrer;
    const detailsPagePattern = new RegExp(`/items/${id}$`);
    setCameFromDetails(detailsPagePattern.test(referrer));
  }, [id]);

  // Smart cancel function
  const handleCancel = () => {
    if (cameFromDetails) {
      // Go back to details page
      router.push(`/items/${id}`);
    } else {
      // Go back to items list
      router.push('/items');
    }
  };

  // Fetch item data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const itemData = await itemsApi.getById(id);

        // Set basic details
        setName(itemData.name);
        setVietnameseName(itemData.vietnameseName || '');
        setCategory(itemData.category || '');
        setDescription(itemData.description || '');
        setPreparationTime(itemData.preparationTime || '');

        // Set variants and ingredients with fallbacks
        // Use the transformed data from the API
        const variantsData = itemData.variants || [];
        setVariants(variantsData);

        // Use the transformed base ingredients from the API
        const baseIngredients = itemData.baseIngredients || [];
        setBaseIngredients(baseIngredients);

        // Fetch inventory items
        await fetchInventoryItems();
      } catch (err: any) {
        console.error('Error fetching item data:', err);
        setError(err.message || 'Failed to load item data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  // Fetch inventory items for ingredient selection
  const fetchInventoryItems = async () => {
    if (inventoryItems.length > 0) return; // Already loaded

    try {
      setLoadingInventoryItems(true);
      // Fetch ALL inventory items for ingredient selection (no pagination)
      const result = await inventoryItemsApi.getAll({ limit: 1000 });
      console.log('📦 Loaded inventory items for ingredients:', result.data?.length || 0);
      setInventoryItems(result.data || []);
    } catch (err: any) {
      console.error('Error fetching inventory items:', err);
      setError(err.message || 'Failed to load inventory items');
      setInventoryItems([]);
    } finally {
      setLoadingInventoryItems(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!name) {
      setError('Item name is required');
      return;
    }

    // Validate variants - only if they exist
    // Note: Variants are optional for manual items, required for Square-synced items
    for (const variant of variants) {
      if (!variant.name) {
        setError('All variants must have a name');
        return;
      }
    }

    // Validate base ingredients
    for (let i = 0; i < baseIngredients.length; i++) {
      const ingredient = baseIngredients[i];
      if (!ingredient.inventoryItemId) {
        setError(`Base ingredient ${i + 1}: Please select an ingredient`);
        return;
      }
      if (!ingredient.quantityPerServing || ingredient.quantityPerServing <= 0) {
        setError(`Base ingredient ${i + 1}: Quantity must be greater than 0`);
        return;
      }
    }

    // Validate variant ingredients
    for (const variant of variants) {
      for (let i = 0; i < variant.additionalIngredients.length; i++) {
        const ingredient = variant.additionalIngredients[i];
        if (!ingredient.inventoryItemId) {
          setError(`${variant.name} ingredient ${i + 1}: Please select an ingredient`);
          return;
        }
        if (!ingredient.quantityPerServing || ingredient.quantityPerServing <= 0) {
          setError(`${variant.name} ingredient ${i + 1}: Quantity must be greater than 0`);
          return;
        }
      }
    }

    try {
      setSaving(true);
      setError(null);

      // Update item
      const updatedItem: Item = {
        id,
        name,
        vietnameseName: vietnameseName || undefined,
        category: category || undefined,
        description: description || undefined,
        preparationTime: preparationTime !== '' ? Number(preparationTime) : undefined,
        baseIngredients,
        variants
      };

      await itemsApi.update(id, updatedItem);
      // Redirect to items list page
      router.push('/items');
    } catch (err: any) {
      console.error('Error updating item:', err);
      setError(err.message || 'Failed to update item');
      setSaving(false);
    }
  };

  // Apply changes without redirecting (stays on edit form)
  const handleApply = async () => {
    // Validate form first
    if (!name) {
      setError('Item name is required');
      return;
    }

    // Validate variants - only if they exist
    // Note: Variants are optional for manual items, required for Square-synced items
    for (const variant of variants) {
      if (!variant.name) {
        setError('All variants must have a name');
        return;
      }
    }

    // Validate base ingredients
    for (let i = 0; i < baseIngredients.length; i++) {
      const ingredient = baseIngredients[i];
      if (!ingredient.inventoryItemId) {
        setError(`Base ingredient ${i + 1}: Please select an ingredient`);
        return;
      }
      if (!ingredient.quantityPerServing || ingredient.quantityPerServing <= 0) {
        setError(`Base ingredient ${i + 1}: Quantity must be greater than 0`);
        return;
      }
    }

    // Validate variant ingredients
    for (const variant of variants) {
      for (let i = 0; i < variant.additionalIngredients.length; i++) {
        const ingredient = variant.additionalIngredients[i];
        if (!ingredient.inventoryItemId) {
          setError(`${variant.name} ingredient ${i + 1}: Please select an ingredient`);
          return;
        }
        if (!ingredient.quantityPerServing || ingredient.quantityPerServing <= 0) {
          setError(`${variant.name} ingredient ${i + 1}: Quantity must be greater than 0`);
          return;
        }
      }
    }

    try {
      setSaving(true);
      setError(null);

      // Update item
      const updatedItem: Item = {
        id,
        name,
        vietnameseName: vietnameseName || undefined,
        category: category || undefined,
        description: description || undefined,
        preparationTime: preparationTime !== '' ? Number(preparationTime) : undefined,
        baseIngredients,
        variants
      };

      await itemsApi.update(id, updatedItem);

      // Create detailed changes summary
      const changes = [];
      if (name) changes.push(`Name: "${name}"`);
      if (vietnameseName) changes.push(`Vietnamese Name: "${vietnameseName}"`);
      if (category) changes.push(`Category: ${category}`);
      if (baseIngredients.length > 0) changes.push(`${baseIngredients.length} base ingredient(s)`);
      if (variants.length > 0) {
        const totalVariantIngredients = variants.reduce((sum, v) => sum + v.additionalIngredients.length, 0);
        changes.push(`${variants.length} variant(s) with ${totalVariantIngredients} additional ingredient(s)`);
      }

      const changesText = changes.length > 0 ? changes.join(', ') : 'Basic information';
      setChangesBanner(`✅ Applied changes: ${changesText}`);
      setError(null);

      // Clear changes banner after 5 seconds
      setTimeout(() => {
        setChangesBanner(null);
      }, 5000);
    } catch (err: any) {
      console.error('Error applying changes:', err);
      setError(err.message || 'Failed to apply changes');
    } finally {
      setSaving(false);
    }
  };

  // Note: addVariant function removed - variants now come from Square sync only

  // Remove a variant
  const removeVariant = (id: string) => {
    setVariants(variants.filter(v => v.id !== id));
  };

  // Update a variant
  const updateVariant = (id: string, field: keyof ItemVariant, value: any) => {
    setVariants(
      variants.map(v => {
        if (v.id === id) {
          return { ...v, [field]: value };
        }
        return v;
      })
    );
  };

  // Add ingredient to a specific variant
  const addVariantIngredient = (variantId: string) => {
    if (inventoryItems.length === 0) {
      fetchInventoryItems();
    }

    setVariants(
      variants.map(v => {
        if (v.id === variantId) {
          return {
            ...v,
            additionalIngredients: [
              ...v.additionalIngredients,
              {
                inventoryItemId: '',
                quantityPerServing: 0
              }
            ]
          };
        }
        return v;
      })
    );
  };

  // Remove ingredient from a specific variant
  const removeVariantIngredient = (variantId: string, ingredientIndex: number) => {
    setVariants(
      variants.map(v => {
        if (v.id === variantId) {
          return {
            ...v,
            additionalIngredients: v.additionalIngredients.filter((_, i) => i !== ingredientIndex)
          };
        }
        return v;
      })
    );
  };

  // Update ingredient in a specific variant
  const updateVariantIngredient = (variantId: string, ingredientIndex: number, field: keyof ItemIngredient, value: any) => {
    setVariants(
      variants.map(v => {
        if (v.id === variantId) {
          return {
            ...v,
            additionalIngredients: v.additionalIngredients.map((ingredient, i) => {
              if (i === ingredientIndex) {
                return { ...ingredient, [field]: value };
              }
              return ingredient;
            })
          };
        }
        return v;
      })
    );
  };

  // Add a base ingredient
  const addBaseIngredient = () => {
    if (inventoryItems.length === 0) {
      fetchInventoryItems();
    }

    setBaseIngredients([
      ...baseIngredients,
      {
        inventoryItemId: '',
        quantityPerServing: 0
      }
    ]);
  };

  // Remove a base ingredient
  const removeBaseIngredient = (index: number) => {
    setBaseIngredients(baseIngredients.filter((_, i) => i !== index));
  };

  // Update a base ingredient
  const updateBaseIngredient = (index: number, field: keyof ItemIngredient, value: any) => {
    setBaseIngredients(
      baseIngredients.map((ingredient, i) => {
        if (i === index) {
          return { ...ingredient, [field]: value };
        }
        return ingredient;
      })
    );
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </MainLayout>
    );
  }

  if (error && !saving) {
    return (
      <MainLayout>
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-6">
          <p>{error}</p>
        </div>
        <button
          onClick={handleCancel}
          className="px-4 py-2 bg-blue-500 text-white rounded-md text-sm font-medium hover:bg-blue-600"
        >
          {cameFromDetails ? 'Back to Item Details' : 'Back to Items List'}
        </button>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="mb-6 flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-800">Full Item Editor: {name}</h2>
        <button
          onClick={handleCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          title={cameFromDetails ? 'Back to item details' : 'Back to items list'}
        >
          Cancel
        </button>
      </div>

      {changesBanner && (
        <div className="bg-green-50 border border-green-200 text-green-800 rounded-md p-4 mb-6">
          {changesBanner}
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-6">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium mb-4">Basic Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Name *
              </label>
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                onFocus={(e) => {
                  // Select all text when focusing on default/placeholder-like values
                  if (e.target.value.trim() === '' || e.target.value === 'Untitled Item') {
                    e.target.select();
                  }
                }}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label htmlFor="vietnameseName" className="block text-sm font-medium text-gray-700 mb-1">
                Vietnamese Name
              </label>
              <input
                type="text"
                id="vietnameseName"
                value={vietnameseName}
                onChange={(e) => setVietnameseName(e.target.value)}
                onFocus={(e) => {
                  // Select all text when focusing on empty or default values
                  if (e.target.value.trim() === '') {
                    e.target.select();
                  }
                }}
                placeholder="Enter Vietnamese name"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                id="category"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select Category</option>
                <option value="main">Main Dishes</option>
                <option value="Main Dishes">Main Dishes (Square)</option>
                <option value="appetizer">Appetizers</option>
                <option value="dessert">Desserts</option>
                <option value="beverage">Beverages</option>
                <option value="Other">Other</option>
                <option value="Uncategorized">Uncategorized</option>
              </select>
            </div>

            <div>
              <label htmlFor="preparationTime" className="block text-sm font-medium text-gray-700 mb-1">
                Preparation Time (minutes)
              </label>
              <input
                type="number"
                id="preparationTime"
                value={preparationTime || ''}
                onChange={(e) => setPreparationTime(e.target.value ? Number(e.target.value) : '')}
                onFocus={(e) => {
                  // Select all text when focusing on 0 or empty values
                  if (e.target.value === '0' || e.target.value === '') {
                    e.target.select();
                  }
                }}
                min="0"
                placeholder="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div className="md:col-span-2">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                onFocus={(e) => {
                  // Select all text when focusing on empty or default values
                  if (e.target.value.trim() === '' || e.target.value === 'Enter description...') {
                    e.target.select();
                  }
                }}
                rows={3}
                placeholder="Enter description..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              ></textarea>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Base Ingredients</h3>
            <button
              type="button"
              onClick={addBaseIngredient}
              className="px-3 py-1 bg-green-500 text-white rounded-md text-sm font-medium hover:bg-green-600"
            >
              Add Ingredient
            </button>
          </div>

          {baseIngredients.length === 0 ? (
            <p className="text-gray-500">No base ingredients added yet.</p>
          ) : (
            <div className="space-y-4">
              {baseIngredients.map((ingredient, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Ingredient
                    </label>
                    <select
                      value={ingredient.inventoryItemId}
                      onChange={(e) => updateBaseIngredient(index, 'inventoryItemId', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      onClick={fetchInventoryItems}
                    >
                      <option value="">Select Ingredient</option>
                      {loadingInventoryItems ? (
                        <option value="" disabled>Loading...</option>
                      ) : (
                        inventoryItems.map((item) => (
                          <option key={item.id} value={item.id}>
                            {item.name} ({item.unit})
                          </option>
                        ))
                      )}
                    </select>
                  </div>
                  <div className="w-32">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Quantity
                    </label>
                    <input
                      type="number"
                      value={ingredient.quantityPerServing || ''}
                      onChange={(e) => updateBaseIngredient(index, 'quantityPerServing', Number(e.target.value) || 0)}
                      onFocus={(e) => {
                        if (e.target.value === '0') {
                          e.target.select();
                        }
                      }}
                      min="0"
                      step="0.01"
                      placeholder="0.00"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="flex items-end">
                    <button
                      type="button"
                      onClick={() => removeBaseIngredient(index)}
                      className="px-3 py-2 text-red-600 hover:text-red-800"
                    >
                      Remove
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Variants</h3>
            <div className="text-sm text-gray-600">
              Variants are synced from Square. Use Square catalog sync to add variants.
            </div>
          </div>

          {variants.length === 0 ? (
            <p className="text-gray-500">No variants added yet.</p>
          ) : (
            <div className="space-y-6">
              {variants.map((variant) => (
                <div key={variant.id} className="border border-gray-200 rounded-md p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h4 className="font-medium">
                      {variant.name} Variant
                    </h4>
                    <button
                      type="button"
                      onClick={() => removeVariant(variant.id)}
                      className="text-red-600 hover:text-red-800"
                      title="Remove variant"
                    >
                      Remove
                    </button>
                  </div>

                  <div className="mb-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Variant Name
                      </label>
                      <input
                        type="text"
                        value={variant.name}
                        onChange={(e) => updateVariant(variant.id, 'name', e.target.value)}
                        onFocus={(e) => {
                          if (e.target.value === 'Dine In' || e.target.value === 'Takeaway') {
                            e.target.select();
                          }
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  {/* Ingredients for this variant */}
                  <div className="border-t border-gray-200 pt-4">
                    <div className="flex justify-between items-center mb-3">
                      <h5 className="text-sm font-medium text-gray-700">Ingredients</h5>
                      <button
                        type="button"
                        onClick={() => addVariantIngredient(variant.id)}
                        className="px-2 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600"
                      >
                        Add Ingredient
                      </button>
                    </div>

                    {variant.additionalIngredients.length === 0 ? (
                      <p className="text-gray-400 text-sm">No ingredients added yet.</p>
                    ) : (
                      <div className="space-y-3">
                        {variant.additionalIngredients.map((ingredient, index) => (
                          <div key={index} className="flex items-center space-x-3">
                            <div className="flex-1">
                              <select
                                value={ingredient.inventoryItemId}
                                onChange={(e) => updateVariantIngredient(variant.id, index, 'inventoryItemId', e.target.value)}
                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                                onClick={fetchInventoryItems}
                              >
                                <option value="">Select Ingredient</option>
                                {loadingInventoryItems ? (
                                  <option value="" disabled>Loading...</option>
                                ) : (
                                  inventoryItems.map((item) => (
                                    <option key={item.id} value={item.id}>
                                      {item.name} ({item.unit})
                                    </option>
                                  ))
                                )}
                              </select>
                            </div>
                            <div className="w-24">
                              <input
                                type="number"
                                value={ingredient.quantityPerServing || ''}
                                onChange={(e) => updateVariantIngredient(variant.id, index, 'quantityPerServing', Number(e.target.value) || 0)}
                                onFocus={(e) => {
                                  if (e.target.value === '0') {
                                    e.target.select();
                                  }
                                }}
                                min="0"
                                step="0.01"
                                placeholder="0.00"
                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
                              />
                            </div>
                            <button
                              type="button"
                              onClick={() => removeVariantIngredient(variant.id, index)}
                              className="px-2 py-1 text-red-600 hover:text-red-800 text-sm"
                            >
                              Remove
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={handleApply}
            disabled={saving}
            className="px-4 py-2 bg-green-500 text-white rounded-md text-sm font-medium hover:bg-green-600 disabled:opacity-50"
          >
            {saving ? 'Applying...' : 'Apply Changes'}
          </button>
          <button
            type="submit"
            disabled={saving}
            className="px-4 py-2 bg-blue-500 text-white rounded-md text-sm font-medium hover:bg-blue-600 disabled:opacity-50"
          >
            {saving ? 'Saving...' : 'Save & Back to List'}
          </button>
        </div>
      </form>
    </MainLayout>
  );
}

// Main page component that handles params
export default function FullEditItemPage({ params }: FullEditItemPageProps) {
  const id = params.id;
  return <FullEditItemClient id={id} />;
}
