'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface EditMealRedirectProps {
  params: {
    id: string;
  };
}

export default function EditMealRedirect({ params }: EditMealRedirectProps) {
  const router = useRouter();
  const id = params.id;

  useEffect(() => {
    router.replace(`/items/${id}/edit`);
  }, [router, id]);

  return (
    <div className="flex items-center justify-center h-screen">
      <div className="text-center">
        <h1 className="text-xl font-semibold mb-2">Redirecting...</h1>
        <p className="text-gray-600">Please wait while we redirect you to the item editor.</p>
      </div>
    </div>
  );
}
