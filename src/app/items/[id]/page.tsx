'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import { itemsApi } from '@/services/apiService';
import { Item } from '@/types/inventory';

interface ItemDetailPageProps {
  params: {
    id: string;
  };
}

// Client component that shows item details
function ItemDetailClient({ id }: { id: string }) {
  const router = useRouter();
  const [item, setItem] = useState<Item | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchItem = async () => {
      try {
        setLoading(true);
        setError(null);
        const itemData = await itemsApi.getById(id);
        setItem(itemData);
      } catch (err: any) {
        console.error('Error fetching item:', err);
        setError(err.message || 'Failed to fetch item');
      } finally {
        setLoading(false);
      }
    };

    fetchItem();
  }, [id]);

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-6">
          <p>{error}</p>
        </div>
        <button
          onClick={() => router.push('/items')}
          className="px-4 py-2 bg-blue-500 text-white rounded-md text-sm font-medium hover:bg-blue-600"
        >
          Back to Items
        </button>
      </MainLayout>
    );
  }

  if (!item) {
    return (
      <MainLayout>
        <div className="text-center py-12">
          <p className="text-gray-500">Item not found</p>
          <button
            onClick={() => router.push('/items')}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md text-sm font-medium hover:bg-blue-600"
          >
            Back to Items
          </button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="mb-6 flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-800">{item.name}</h2>
        <div className="flex space-x-3">
          <button
            onClick={() => router.push('/items')}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            Back to Items
          </button>
          <button
            onClick={() => router.push(`/items/${id}/full-edit`)}
            className="px-4 py-2 bg-blue-500 text-white rounded-md text-sm font-medium hover:bg-blue-600"
          >
            Edit Item
          </button>
        </div>
      </div>

      <div className="space-y-6">
        {/* Basic Information */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium mb-4">Basic Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
              <p className="text-gray-900">{item.name}</p>
            </div>
            {item.vietnameseName && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Vietnamese Name</label>
                <p className="text-gray-900">{item.vietnameseName}</p>
              </div>
            )}
            {item.category && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <p className="text-gray-900 capitalize">{item.category}</p>
              </div>
            )}
            {item.preparationTime && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Preparation Time</label>
                <p className="text-gray-900">{item.preparationTime} minutes</p>
              </div>
            )}
            {item.description && (
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <p className="text-gray-900">{item.description}</p>
              </div>
            )}
          </div>
        </div>

        {/* Base Ingredients */}
        {item.baseIngredients && item.baseIngredients.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium mb-4">Base Ingredients</h3>
            <div className="space-y-3">
              {item.baseIngredients.map((ingredient, index) => (
                <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                  <span className="text-gray-900">{ingredient.inventoryItemName || ingredient.inventoryItemId}</span>
                  <span className="text-gray-600">{ingredient.quantityPerServing} units</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Variants */}
        {item.variants && item.variants.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium mb-4">Variants</h3>
            <div className="space-y-6">
              {item.variants.map((variant) => (
                <div key={variant.id} className="border border-gray-200 rounded-md p-4">
                  <h4 className="font-medium mb-3">{variant.name}</h4>

                  {variant.additionalIngredients && variant.additionalIngredients.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Additional Ingredients</h5>
                      <div className="space-y-2">
                        {variant.additionalIngredients.map((ingredient, index) => (
                          <div key={index} className="flex justify-between items-center py-1">
                            <span className="text-gray-900 text-sm">{ingredient.inventoryItemId}</span>
                            <span className="text-gray-600 text-sm">{ingredient.quantityPerServing} units</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}

// Main page component that handles params
export default function ItemDetailPage({ params }: ItemDetailPageProps) {
  const id = params.id;
  return <ItemDetailClient id={id} />;
}