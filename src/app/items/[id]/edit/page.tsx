'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface EditItemPageProps {
  params: {
    id: string;
  };
}

// Client component that redirects to the full editor
function EditItemClient({ id }: { id: string }) {
  const router = useRouter();

  // Redirect to full editor
  useEffect(() => {
    router.replace(`/items/${id}/full-edit`);
  }, [router, id]);

  // Return a loading indicator while redirecting
  return (
    <div className="flex items-center justify-center h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <h1 className="text-xl font-semibold mb-2">Redirecting to Full Editor...</h1>
        <p className="text-gray-600">Please wait while we redirect you.</p>
      </div>
    </div>
  );
}

// Main page component that handles params
export default function EditItemPage({ params }: EditItemPageProps) {
  const id = params.id;
  return <EditItemClient id={id} />;
}
