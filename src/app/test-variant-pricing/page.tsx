'use client'

import { useState } from 'react'
import { itemsApi } from '@/services/apiService'
import MainLayout from '@/components/layout/MainLayout'

export default function TestVariantPricing() {
  const [testResult, setTestResult] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string>('')

  const testVariantUpdate = async () => {
    setLoading(true)
    setError('')
    setTestResult('')

    try {
      // First, get a real item ID from the API
      console.log('🧪 Fetching existing items to get a real ID...')
      const itemsResponse = await itemsApi.getAll()

      if (!itemsResponse.data || itemsResponse.data.length === 0) {
        throw new Error('No existing items found. Please create an item first.')
      }

      const testItemId = itemsResponse.data[0].id
      console.log('🧪 Using existing item ID:', testItemId)

      // Test data for updating an item with variants
      const testUpdate = {
        name: 'Updated Test Menu Item',
        category: 'Main Course',
        description: 'Test item for variant pricing - UPDATED',
        variants: [
          {
            id: 'variant-1',
            type: 'dine_in' as const,
            name: 'Dine In',
            sellingPrice: 15.99,
            additionalIngredients: [],
            nonInventoryItems: []
          },
          {
            id: 'variant-2',
            type: 'takeaway' as const,
            name: 'Takeaway',
            sellingPrice: 14.99,
            additionalIngredients: [],
            nonInventoryItems: []
          }
        ],
        baseIngredients: []
      }

      console.log('🧪 Testing variant update with data:', testUpdate)

      const result = await itemsApi.update(testItemId, testUpdate)

      setTestResult(`✅ Success! Updated item: ${result.name} (ID: ${testItemId})`)
      console.log('✅ Test successful:', result)

    } catch (err: any) {
      console.error('❌ Test failed:', err)
      setError(err.message || 'Test failed')
      setTestResult(`❌ Failed: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  const testCreateItem = async () => {
    setLoading(true)
    setError('')
    setTestResult('')

    try {
      // Test creating a new item with variants
      const testItem = {
        name: `Test Item ${Date.now()}`,
        category: 'Test Category',
        description: 'Test item created for variant pricing test',
        variants: [
          {
            id: `variant-${Date.now()}-1`,
            type: 'dine_in' as const,
            name: 'Dine In',
            sellingPrice: 12.99,
            additionalIngredients: [],
            nonInventoryItems: []
          },
          {
            id: `variant-${Date.now()}-2`,
            type: 'takeaway' as const,
            name: 'Takeaway',
            sellingPrice: 11.99,
            additionalIngredients: [],
            nonInventoryItems: []
          }
        ],
        baseIngredients: []
      }

      console.log('🧪 Testing item creation with variants:', testItem)

      const result = await itemsApi.add(testItem)

      setTestResult(`✅ Success! Created item: ${result.name} (ID: ${result.id})`)
      console.log('✅ Creation test successful:', result)

    } catch (err: any) {
      console.error('❌ Creation test failed:', err)
      setError(err.message || 'Creation test failed')
      setTestResult(`❌ Creation failed: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Variant Pricing Test</h1>

        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">API Fix Status</h2>
          <div className="bg-blue-50 rounded-lg p-4 mb-4">
            <h3 className="font-medium text-blue-900 mb-2">🔧 Fixed Issues:</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Separated main item fields from variants in API</li>
              <li>• Added proper variant handling in item_variants table</li>
              <li>• Fixed 400 error when updating items with variants</li>
              <li>• Added proper error logging and debugging</li>
              <li>• Improved user profile error handling</li>
            </ul>
          </div>

          <div className="bg-green-50 rounded-lg p-4">
            <h3 className="font-medium text-green-900 mb-2">✅ Expected Behavior:</h3>
            <ul className="text-sm text-green-800 space-y-1">
              <li>• Menu items can be updated with variant pricing</li>
              <li>• Variants are stored in separate item_variants table</li>
              <li>• No more 400 errors when adding/updating variants</li>
              <li>• Proper error messages for debugging</li>
            </ul>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Test Item Creation</h3>
            <p className="text-sm text-gray-600 mb-4">
              Create a new menu item with dine-in and takeaway variants
            </p>
            <button
              onClick={testCreateItem}
              disabled={loading}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Test Create Item'}
            </button>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Test Item Update</h3>
            <p className="text-sm text-gray-600 mb-4">
              Update an existing item with new variant pricing (automatically finds first existing item)
            </p>
            <button
              onClick={testVariantUpdate}
              disabled={loading}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {loading ? 'Testing...' : 'Test Update Item'}
            </button>
          </div>
        </div>

        {/* Test Results */}
        {testResult && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Test Results</h3>
            <div className={`rounded-lg p-4 ${testResult.includes('✅') ? 'bg-green-50' : 'bg-red-50'}`}>
              <p className={`font-medium ${testResult.includes('✅') ? 'text-green-900' : 'text-red-900'}`}>
                {testResult}
              </p>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 className="text-lg font-semibold text-red-800 mb-4">Error Details</h3>
            <div className="bg-red-50 rounded-lg p-4">
              <p className="text-red-800 font-mono text-sm">{error}</p>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Testing Instructions</h3>
          <div className="text-sm text-gray-600 space-y-2">
            <p><strong>1. Test Creation:</strong> Click "Test Create Item" to create a new menu item with variants</p>
            <p><strong>2. Test Update:</strong> Go to any existing menu item edit page and try adding/updating variant prices</p>
            <p><strong>3. Check Console:</strong> Open browser dev tools to see detailed API logs</p>
            <p><strong>4. Verify Database:</strong> Check that variants are properly stored in item_variants table</p>
          </div>

          <div className="mt-4 pt-4 border-t border-gray-200">
            <h4 className="font-medium text-gray-800 mb-2">Quick Navigation:</h4>
            <div className="flex flex-wrap gap-2">
              <a
                href="/items"
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                Menu Items
              </a>
              <a
                href="/items/add"
                className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
              >
                Add Item
              </a>
              <a
                href="/dashboard-light"
                className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
              >
                Dashboard
              </a>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
