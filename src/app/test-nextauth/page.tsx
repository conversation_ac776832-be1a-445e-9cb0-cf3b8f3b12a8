'use client'

import { useSession, signOut } from 'next-auth/react'
import NextAuthProtectedRoute from '@/components/auth/NextAuthProtectedRoute'

export default function TestNextAuth() {
  const { data: session, status } = useSession()

  if (status === 'loading') {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600 text-sm">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <NextAuthProtectedRoute>
      <div className="min-h-screen bg-gray-100 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="bg-white rounded-lg shadow-md p-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-6">NextAuth.js Test Page</h1>
            
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Session Information</h2>
              <div className="bg-gray-50 rounded-lg p-4">
                <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                  {JSON.stringify(session, null, 2)}
                </pre>
              </div>
            </div>

            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">User Details</h2>
              {session?.user && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h3 className="font-medium text-blue-900 mb-2">Basic Info</h3>
                    <p><strong>ID:</strong> {session.user.id}</p>
                    <p><strong>Email:</strong> {session.user.email}</p>
                    <p><strong>Username:</strong> {session.user.username}</p>
                  </div>
                  <div className="bg-green-50 rounded-lg p-4">
                    <h3 className="font-medium text-green-900 mb-2">Profile</h3>
                    <p><strong>First Name:</strong> {session.user.firstName}</p>
                    <p><strong>Last Name:</strong> {session.user.lastName}</p>
                    <p><strong>Role:</strong> {session.user.role}</p>
                    <p><strong>Active:</strong> {session.user.isActive ? 'Yes' : 'No'}</p>
                  </div>
                </div>
              )}
            </div>

            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">NextAuth.js Benefits</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-yellow-50 rounded-lg p-4">
                  <h3 className="font-medium text-yellow-900 mb-2">✅ Session Management</h3>
                  <ul className="text-sm text-yellow-800 space-y-1">
                    <li>• HTTP-only cookies (secure)</li>
                    <li>• Automatic session refresh</li>
                    <li>• No tokens in browser storage</li>
                    <li>• Survives page refresh</li>
                  </ul>
                </div>
                <div className="bg-purple-50 rounded-lg p-4">
                  <h3 className="font-medium text-purple-900 mb-2">🔒 Security</h3>
                  <ul className="text-sm text-purple-800 space-y-1">
                    <li>• CSRF protection</li>
                    <li>• Secure cookie handling</li>
                    <li>• JWT token management</li>
                    <li>• Built-in security best practices</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="flex space-x-4">
              <button
                onClick={() => signOut({ callbackUrl: '/auth/nextauth-login' })}
                className="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                Sign Out
              </button>
              <a
                href="/dashboard-light"
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 inline-block"
              >
                Go to Dashboard
              </a>
            </div>
          </div>
        </div>
      </div>
    </NextAuthProtectedRoute>
  )
}
