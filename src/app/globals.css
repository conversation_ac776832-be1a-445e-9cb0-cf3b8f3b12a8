@tailwind base;
@tailwind components;
@tailwind utilities;

/* Basic variables without nesting */
:root {
  --background: #ffffff;
  --foreground: #402014; /* Vietnamese restaurant text color */
  --color-background: #ffffff;
  --color-foreground: #402014;
  --font-sans: var(--font-plus-jakarta-sans);
  --font-mono: var(--font-roboto-mono);

  /* Vietnamese restaurant color palette */
  --viet-red: #EA4E3D;
  --viet-yellow: #FFE194; /* Lighter yellow for the portal */
  --viet-yellow-dark: #FDB833;
  --viet-yellow-darker: #F9A826;
  --viet-cream: #EDE9D5;
  --viet-cream-dark: #E5DFC9;
  --viet-cream-darker: #D8D0B8;
  --viet-text: #402014;

  /* Sidebar colors - Dark professional design */
  --sidebar-default: #1F2937;
  --sidebar-light: #374151;
  --sidebar-medium: #4B5563;
  --sidebar-dark: #6B7280;
  --sidebar-darker: #9CA3AF;
  --sidebar-darkest: #D1D5DB;
}

body {
  background: #f3f4f6;
  color: var(--viet-text);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

/* Main content area styling */
main {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-top: 1rem;
  padding: 1.5rem;
}

/* Button styling with Vietnamese restaurant colors */
.btn-primary {
  background-color: var(--viet-red);
  color: white;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #d04535;
}

/* Table styling */
table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
}

table th {
  background-color: var(--viet-cream-dark);
  color: var(--viet-text);
  font-weight: 600;
  text-align: left;
  padding: 0.75rem 1rem;
}

table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--viet-cream);
}

table tr:hover {
  background-color: var(--viet-cream);
}
