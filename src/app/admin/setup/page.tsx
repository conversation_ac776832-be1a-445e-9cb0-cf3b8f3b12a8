'use client';

import { useState } from 'react';

export default function AdminSetupPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const createSampleData = async () => {
    try {
      setLoading(true);
      setError(null);
      setResult(null);

      const response = await fetch('/api/test/create-sample-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'Failed to create sample data');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create sample data');
    } finally {
      setLoading(false);
    }
  };

  const testDatabase = async () => {
    try {
      setLoading(true);
      setError(null);
      setResult(null);

      const response = await fetch('/api/test/supabase');
      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'Failed to test database');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to test database');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Admin Setup</h1>
          <p className="text-gray-600">Database management and sample data creation</p>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Database Setup</h2>

          <div className="space-y-4">
            <div className="flex flex-wrap gap-4">
              <button
                onClick={testDatabase}
                disabled={loading}
                className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-6 py-3 rounded-md font-medium transition-colors"
              >
                {loading ? '🔄 Testing...' : '🔍 Test Database Connection'}
              </button>

              <button
                onClick={createSampleData}
                disabled={loading}
                className="bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white px-6 py-3 rounded-md font-medium transition-colors"
              >
                {loading ? '🔄 Creating...' : '📦 Create Sample Data'}
              </button>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div className="text-sm text-blue-800 space-y-2">
                <p><strong>🔍 Test Database:</strong> Check if Supabase connection is working and see current data count</p>
                <p><strong>📦 Create Sample Data:</strong> Add sample inventory items and menu items to populate empty database</p>
              </div>
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="font-medium">Error:</h3>
                <p>{error}</p>
              </div>
            </div>
          </div>
        )}

        {result && (
          <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
            <h3 className="font-medium text-gray-800 mb-3 flex items-center">
              <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Result:
            </h3>
            <div className="bg-white rounded border p-3 max-h-96 overflow-auto">
              <pre className="text-sm text-gray-600">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          </div>
        )}

        <div className="mt-8 text-center">
          <a
            href="/inventory"
            className="text-blue-600 hover:text-blue-800 underline mr-6"
          >
            → Go to Inventory Page
          </a>
          <a
            href="/items"
            className="text-blue-600 hover:text-blue-800 underline"
          >
            → Go to Items Page
          </a>
        </div>
      </div>
    </div>
  );
}
