'use client';

import React, { useState } from 'react';

export default function FixPorkRollsPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const fixPorkRolls = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/square/simple-sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          itemId: '13303de5-ef0c-47d1-aa3a-7983947ac240',
          squareId: 'C4XTLDOHSNQHGFDYW66NFDTI'
        })
      });
      
      const data = await response.json();
      setResult(data);
      console.log('Fix result:', data);
    } catch (error) {
      console.error('Fix error:', error);
      setResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Fix Pork Spring Rolls Square ID</h1>
      
      <div className="bg-yellow-50 border border-yellow-200 rounded p-4 mb-6">
        <p className="text-sm">
          This will link the Pork Spring Rolls item (ID: 13303de5-ef0c-47d1-aa3a-7983947ac240) 
          to the working Square catalog ID (C4XTLDOHSNQHGFDYW66NFDTI).
        </p>
      </div>

      <button
        onClick={fixPorkRolls}
        disabled={loading}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
      >
        {loading ? 'Fixing...' : 'Fix Pork Spring Rolls Square ID'}
      </button>

      {result && (
        <div className="mt-6 p-4 bg-gray-50 rounded">
          <h3 className="font-semibold mb-2">Result:</h3>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}

      {result?.success && (
        <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded">
          <p className="text-green-800">
            ✅ Success! Now go to <a href="/reports" className="underline">/reports</a> and test the Pork Spring Rolls dropdown.
          </p>
        </div>
      )}
    </div>
  );
}
