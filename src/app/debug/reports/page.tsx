'use client';

import React, { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';

interface DebugInfo {
  itemsTest?: any;
  ordersTest?: any;
  itemOrdersTest?: any;
}

export default function DebugReportsPage() {
  const [debugInfo, setDebugInfo] = useState<DebugInfo>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedItemId, setSelectedItemId] = useState<string>('');

  const runDebugTests = async () => {
    setLoading(true);
    const results: DebugInfo = {};

    try {
      // Test 1: Items database
      console.log('🔍 Testing items database...');
      const itemsResponse = await fetch('/api/debug/test-items');
      if (itemsResponse.ok) {
        results.itemsTest = await itemsResponse.json();
      } else {
        results.itemsTest = { error: `HTTP ${itemsResponse.status}` };
      }

      // Test 2: Square orders API
      console.log('🔍 Testing Square orders API...');
      const ordersResponse = await fetch('/api/debug/test-square-orders');
      if (ordersResponse.ok) {
        results.ordersTest = await ordersResponse.json();
      } else {
        results.ordersTest = { error: `HTTP ${ordersResponse.status}` };
      }

      // Test 3: Item orders API (if we have a Square item ID)
      if (results.itemsTest?.analysis?.squareItemIds?.length > 0) {
        const testItemId = results.itemsTest.analysis.squareItemIds[0];
        console.log('🔍 Testing item orders API with:', testItemId);
        
        const itemOrdersResponse = await fetch(`/api/square/item-orders?itemId=${testItemId}&days=30`);
        if (itemOrdersResponse.ok) {
          results.itemOrdersTest = await itemOrdersResponse.json();
        } else {
          const errorData = await itemOrdersResponse.json().catch(() => null);
          results.itemOrdersTest = { 
            error: `HTTP ${itemOrdersResponse.status}`,
            details: errorData
          };
        }
      }

      setDebugInfo(results);
    } catch (error: any) {
      console.error('❌ Debug test error:', error);
      setDebugInfo({ 
        ...results, 
        error: error.message 
      });
    } finally {
      setLoading(false);
    }
  };

  const testSpecificItem = async () => {
    if (!selectedItemId) return;

    setLoading(true);
    try {
      console.log('🔍 Testing specific item:', selectedItemId);
      const response = await fetch(`/api/square/item-orders?itemId=${selectedItemId}&days=30`);
      
      let result;
      if (response.ok) {
        result = await response.json();
      } else {
        const errorData = await response.json().catch(() => null);
        result = { 
          error: `HTTP ${response.status}`,
          details: errorData
        };
      }

      setDebugInfo(prev => ({
        ...prev,
        specificItemTest: result
      }));
    } catch (error: any) {
      setDebugInfo(prev => ({
        ...prev,
        specificItemTest: { error: error.message }
      }));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    runDebugTests();
  }, []);

  return (
    <MainLayout>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Debug: Reports System</h1>
        <p className="text-gray-600 mt-1">
          Comprehensive debugging for the item order reports system
        </p>
      </div>

      {/* Controls */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex gap-4 items-end">
          <button
            onClick={runDebugTests}
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? 'Running Tests...' : 'Run All Tests'}
          </button>
          
          <div className="flex gap-2">
            <input
              type="text"
              placeholder="Enter Square Item ID to test"
              value={selectedItemId}
              onChange={(e) => setSelectedItemId(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md"
            />
            <button
              onClick={testSpecificItem}
              disabled={loading || !selectedItemId}
              className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50"
            >
              Test Item
            </button>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="space-y-6">
        {/* Items Test */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">1. Items Database Test</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(debugInfo.itemsTest, null, 2)}
          </pre>
        </div>

        {/* Orders Test */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">2. Square Orders API Test</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(debugInfo.ordersTest, null, 2)}
          </pre>
        </div>

        {/* Item Orders Test */}
        {debugInfo.itemOrdersTest && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">3. Item Orders API Test</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(debugInfo.itemOrdersTest, null, 2)}
            </pre>
          </div>
        )}

        {/* Specific Item Test */}
        {debugInfo.specificItemTest && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">4. Specific Item Test</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(debugInfo.specificItemTest, null, 2)}
            </pre>
          </div>
        )}
      </div>

      {/* Quick Analysis */}
      {debugInfo.itemsTest && debugInfo.ordersTest && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mt-6">
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">Quick Analysis</h3>
          <div className="space-y-2 text-sm">
            <p>
              <strong>Items in database:</strong> {debugInfo.itemsTest?.analysis?.totalItems || 0}
            </p>
            <p>
              <strong>Items with Square IDs:</strong> {debugInfo.itemsTest?.analysis?.itemsWithSquareId || 0}
            </p>
            <p>
              <strong>Orders found (last 7 days):</strong> {debugInfo.ordersTest?.analysis?.totalOrders || 0}
            </p>
            <p>
              <strong>Unique catalog IDs in orders:</strong> {debugInfo.ordersTest?.analysis?.allCatalogIds?.length || 0}
            </p>
            
            {debugInfo.itemsTest?.analysis?.squareItemIds?.length > 0 && debugInfo.ordersTest?.analysis?.allCatalogIds?.length > 0 && (
              <div className="mt-4">
                <p><strong>Potential matches:</strong></p>
                <ul className="list-disc list-inside ml-4">
                  {debugInfo.itemsTest.analysis.squareItemIds
                    .filter((id: string) => debugInfo.ordersTest?.analysis?.allCatalogIds?.includes(id))
                    .map((id: string) => (
                      <li key={id} className="text-green-600">
                        {id} ✓ (Found in both database and orders)
                      </li>
                    ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      )}
    </MainLayout>
  );
}
