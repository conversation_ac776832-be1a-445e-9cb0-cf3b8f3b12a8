'use client';

import React, { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';

interface SyncResult {
  success: boolean;
  message: string;
  results: {
    squareItemsFound: number;
    databaseItemsFound: number;
    updated: number;
    created: number;
    matched: number;
    unmatched: number;
    errors: string[];
    details: any[];
  };
}

export default function ForceSyncPage() {
  const [loading, setLoading] = useState<boolean>(false);
  const [result, setResult] = useState<SyncResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleForceSync = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('🔄 Starting force sync...');
      
      const response = await fetch('/api/square/force-sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Force sync completed:', data);
      setResult(data);
    } catch (err: any) {
      console.error('❌ Force sync error:', err);
      setError(err.message || 'Failed to sync with Square');
    } finally {
      setLoading(false);
    }
  };

  return (
    <MainLayout requiredRoles={['admin']}>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Force Sync with Square</h1>
        <p className="text-gray-600 mt-1">
          Force synchronize all items with Square catalog and update database
        </p>
      </div>

      {/* Sync Controls */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">⚠️ Force Sync Process</h3>
          <div className="text-sm text-yellow-700 space-y-2">
            <p><strong>This will:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>Fetch all items from Square catalog</li>
              <li>Match them with existing database items by name</li>
              <li>Update database items with correct Square item IDs</li>
              <li>Create new items for unmatched Square items</li>
              <li>Report unmatched database items</li>
            </ul>
            <p className="mt-3"><strong>Safe to run:</strong> This only updates/adds items, never deletes.</p>
          </div>
        </div>

        <button
          onClick={handleForceSync}
          disabled={loading}
          className="w-full px-6 py-3 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
        >
          {loading ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2"></div>
              Force Syncing with Square...
            </div>
          ) : (
            'Start Force Sync'
          )}
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-6">
          <h3 className="font-medium">Sync Error</h3>
          <p>{error}</p>
        </div>
      )}

      {/* Results Display */}
      {result && (
        <div className="space-y-6">
          {/* Summary */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-green-800 mb-4">✅ Sync Completed Successfully</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{result.results.squareItemsFound}</div>
                <div className="text-sm text-gray-600">Square Items</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{result.results.updated}</div>
                <div className="text-sm text-gray-600">Updated</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{result.results.created}</div>
                <div className="text-sm text-gray-600">Created</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{result.results.unmatched}</div>
                <div className="text-sm text-gray-600">Unmatched</div>
              </div>
            </div>
          </div>

          {/* Detailed Results */}
          {result.results.details.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Detailed Results</h3>
              <div className="space-y-4">
                {/* Updated Items */}
                {result.results.details.filter(d => d.action === 'updated').length > 0 && (
                  <div>
                    <h4 className="font-medium text-green-700 mb-2">✅ Updated Items ({result.results.updated})</h4>
                    <div className="space-y-2">
                      {result.results.details
                        .filter(d => d.action === 'updated')
                        .map((detail, index) => (
                          <div key={index} className="bg-green-50 p-3 rounded text-sm">
                            <div className="font-medium">{detail.dbItem}</div>
                            <div className="text-gray-600">Square ID: {detail.squareId}</div>
                          </div>
                        ))}
                    </div>
                  </div>
                )}

                {/* Created Items */}
                {result.results.details.filter(d => d.action === 'created').length > 0 && (
                  <div>
                    <h4 className="font-medium text-purple-700 mb-2">➕ Created Items ({result.results.created})</h4>
                    <div className="space-y-2">
                      {result.results.details
                        .filter(d => d.action === 'created')
                        .map((detail, index) => (
                          <div key={index} className="bg-purple-50 p-3 rounded text-sm">
                            <div className="font-medium">{detail.squareItem}</div>
                            <div className="text-gray-600">Square ID: {detail.squareId}</div>
                          </div>
                        ))}
                    </div>
                  </div>
                )}

                {/* Unmatched Items */}
                {result.results.details.filter(d => d.action === 'unmatched').length > 0 && (
                  <div>
                    <h4 className="font-medium text-orange-700 mb-2">⚠️ Unmatched Database Items ({result.results.unmatched})</h4>
                    <div className="space-y-2">
                      {result.results.details
                        .filter(d => d.action === 'unmatched')
                        .map((detail, index) => (
                          <div key={index} className="bg-orange-50 p-3 rounded text-sm">
                            <div className="font-medium">{detail.dbItem}</div>
                            <div className="text-gray-600">No Square ID found - manual review needed</div>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Errors */}
          {result.results.errors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-red-800 mb-4">❌ Errors ({result.results.errors.length})</h3>
              <div className="space-y-2">
                {result.results.errors.map((error, index) => (
                  <div key={index} className="text-sm text-red-700 bg-red-100 p-2 rounded">
                    {error}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Next Steps */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-800 mb-2">🚀 Next Steps</h3>
            <div className="text-sm text-blue-700 space-y-2">
              <p>1. <strong>Test Reports:</strong> Go to <a href="/reports" className="underline hover:text-blue-800">/reports</a> to test your updated items</p>
              <p>2. <strong>Check Items:</strong> Go to <a href="/items" className="underline hover:text-blue-800">/items</a> to see all synced items</p>
              <p>3. <strong>Review Unmatched:</strong> Manually review any unmatched items above</p>
              <p>4. <strong>Debug if Needed:</strong> Use <a href="/debug/reports" className="underline hover:text-blue-800">/debug/reports</a> to verify everything works</p>
            </div>
          </div>
        </div>
      )}
    </MainLayout>
  );
}
