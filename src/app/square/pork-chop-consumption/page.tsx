'use client';

import React, { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';

interface PorkChopOrder {
  orderId: string;
  orderDate: string;
  itemName: string;
  quantity: number;
  totalMoney: number;
}

interface ConsumptionItem {
  quantity: number;
  unit: string;
  description: string;
}

interface PorkChopConsumptionData {
  dateRange: {
    startDate: string;
    endDate: string;
    days: number;
  };
  summary: {
    totalOrders: number;
    totalPorkChopServings: number;
    porkChopOrdersCount: number;
  };
  porkChopOrders: PorkChopOrder[];
  totalConsumption: Record<string, ConsumptionItem>;
  recipe: Record<string, ConsumptionItem>;
}

export default function PorkChopConsumptionPage() {
  const [data, setData] = useState<PorkChopConsumptionData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('🟨 Fetching pork chop consumption data...');
        const response = await fetch('/api/square/pork-chop-consumption');

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `HTTP ${response.status}`);
        }

        const result = await response.json();
        console.log('✅ Pork chop consumption data fetched:', result);
        setData(result);
      } catch (err: any) {
        console.error('❌ Error fetching pork chop consumption:', err);
        setError(err.message || 'Failed to fetch pork chop consumption data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <MainLayout requiredRoles={['admin', 'manager']}>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout requiredRoles={['admin', 'manager']}>
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
          <h3 className="font-medium">Error Loading Data</h3>
          <p>{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-2 px-3 py-1 bg-red-100 text-red-800 rounded hover:bg-red-200"
          >
            Retry
          </button>
        </div>
      </MainLayout>
    );
  }

  if (!data) {
    return (
      <MainLayout requiredRoles={['admin', 'manager']}>
        <div className="text-center py-8 text-gray-500">
          No data available
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout requiredRoles={['admin', 'manager']}>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Pork Chop Consumption Analysis</h1>
        <p className="text-gray-600 mt-1">
          Charcoal Pork Chop (Cơm sườn heo) ingredient consumption from last 3 days
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Date Range</h3>
          <p className="text-sm text-gray-600">
            {new Date(data.dateRange.startDate).toLocaleDateString()} - {new Date(data.dateRange.endDate).toLocaleDateString()}
          </p>
          <p className="text-2xl font-bold text-blue-600 mt-2">{data.dateRange.days} Days</p>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Total Orders</h3>
          <p className="text-2xl font-bold text-green-600">{data.summary.totalOrders}</p>
          <p className="text-sm text-gray-600 mt-1">
            {data.summary.porkChopOrdersCount} pork chop orders
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Pork Chop Servings</h3>
          <p className="text-2xl font-bold text-orange-600">{data.summary.totalPorkChopServings}</p>
          <p className="text-sm text-gray-600 mt-1">Total servings sold</p>
        </div>
      </div>

      {/* Recipe per serving */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Recipe per Serving</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.entries(data.recipe).map(([key, item]) => (
            <div key={key} className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-medium text-gray-900">{item.description}</h4>
              <p className="text-lg font-bold text-blue-600">
                {item.quantity} {item.unit}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Total Consumption */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">
          Total Consumption ({data.summary.totalPorkChopServings} servings)
        </h2>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ingredient
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Quantity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Unit
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {Object.entries(data.totalConsumption).map(([key, item]) => (
                <tr key={key}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {item.description}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <span className="text-lg font-bold text-red-600">{item.quantity}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.unit}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Individual Orders */}
      {data.porkChopOrders.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Pork Chop Orders</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Order Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Item Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Quantity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.porkChopOrders.map((order, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(order.orderDate).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {order.itemName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {order.quantity}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${(order.totalMoney / 100).toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </MainLayout>
  );
}
