'use client';

import React, { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';

interface MappingResult {
  localItemId: string | null;
  localVariantId: string | null;
  squareItemId: string;
  squareVariationName: string;
  itemName: string;
  quantity: number;
  priceInCents: number;
  matched: boolean;
}

export default function TestMappingPage() {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<{
    mappings: MappingResult[];
    summary: string;
    sampleData: any;
  } | null>(null);
  const [error, setError] = useState<string | null>(null);

  const testMapping = async () => {
    try {
      setTesting(true);
      setError(null);
      setResults(null);

      console.log('🧪 Testing Square variant mapping...');

      const response = await fetch('/api/square/test-variant-mapping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to test mapping');
      }

      const data = await response.json();
      setResults(data);
      console.log('✅ Mapping test completed:', data);

    } catch (err: any) {
      console.error('❌ Error testing mapping:', err);
      setError(err.message || 'Unknown error');
    } finally {
      setTesting(false);
    }
  };

  return (
    <MainLayout requiredRoles={['admin']}>
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-800">Square Variant Mapping Test</h2>
        <p className="text-gray-600 mt-2">
          Test the mapping between Square order line items and local variants using sample data from result.json
        </p>
        <div className="mt-4 p-4 bg-blue-50 rounded-md">
          <h3 className="font-medium text-blue-900 mb-2">Mapping Logic:</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• <strong>Square "Regular"</strong> → Local "Dine In" variant (dine_in type)</li>
            <li>• <strong>Square "Large"</strong> → Local "Dine In" variant (larger sizes for dine-in)</li>
            <li>• <strong>Square "Small"</strong> → Local "Takeaway" variant (portable sizes for takeaway)</li>
            <li>• <strong>Fallback:</strong> Defaults to dine_in variant if no specific rule matches</li>
          </ul>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">Test Variant Mapping</h3>
          <button
            onClick={testMapping}
            disabled={testing}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
          >
            {testing ? 'Testing...' : 'Run Test'}
          </button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-4">
            <strong>Error:</strong> {error}
          </div>
        )}

        {results && (
          <div className="space-y-6">
            {/* Summary */}
            <div className="bg-gray-50 rounded-md p-4">
              <h4 className="font-medium mb-2">Mapping Summary</h4>
              <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                {results.summary}
              </pre>
            </div>

            {/* Detailed Results */}
            <div>
              <h4 className="font-medium mb-4">Detailed Mapping Results</h4>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Square Item
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Variation
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Local Match
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {results.mappings.map((mapping, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {mapping.itemName}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {mapping.squareItemId}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {mapping.squareVariationName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {mapping.localItemId ? (
                            <div className="text-sm">
                              <div className="text-gray-900">Item: {mapping.localItemId}</div>
                              <div className="text-gray-500">Variant: {mapping.localVariantId || 'None'}</div>
                            </div>
                          ) : (
                            <span className="text-gray-400">No match</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            mapping.matched
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {mapping.matched ? 'Matched' : 'Unmatched'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {mapping.quantity}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${(mapping.priceInCents / 100).toFixed(2)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Sample Data Info */}
            {results.sampleData && (
              <div className="bg-blue-50 rounded-md p-4">
                <h4 className="font-medium mb-2">Sample Data Used</h4>
                <p className="text-sm text-blue-700">
                  Tested with {results.sampleData.totalLineItems} line items from Square orders API result.json
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </MainLayout>
  );
}
