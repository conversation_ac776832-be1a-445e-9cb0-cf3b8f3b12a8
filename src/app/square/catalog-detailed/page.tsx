'use client';

import React, { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';

interface SquareItem {
  id: string;
  item_data: {
    name: string;
    description?: string;
    category_id?: string;
    variations?: any[];
  };
  variations: any[];
}

export default function CatalogDetailedPage() {
  const [loading, setLoading] = useState(false);
  const [catalogData, setCatalogData] = useState<{
    items: SquareItem[];
    categories: any[];
    summary: any;
  } | null>(null);
  const [error, setError] = useState<string | null>(null);

  const fetchDetailedCatalog = async () => {
    try {
      setLoading(true);
      setError(null);
      setCatalogData(null);

      console.log('🔍 Fetching detailed Square catalog...');

      const response = await fetch('/api/square/catalog-detailed');

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch catalog');
      }

      const data = await response.json();
      console.log('✅ Detailed catalog fetched:', data);

      // Set the catalog data with proper structure
      setCatalogData({
        items: data.data.items || [],
        categories: data.data.categories || [],
        summary: data.summary || {}
      });

    } catch (err: any) {
      console.error('❌ Error fetching detailed catalog:', err);
      setError(err.message || 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <MainLayout requiredRoles={['admin']}>
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-800">Square Catalog Detailed View</h2>
        <p className="text-gray-600 mt-2">
          View complete Square catalog data with all variations for mapping to local items
        </p>
      </div>

      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">Fetch Catalog Data</h3>
          <button
            onClick={fetchDetailedCatalog}
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? 'Fetching...' : 'Fetch Detailed Catalog'}
          </button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-4">
            <strong>Error:</strong> {error}
          </div>
        )}

        {catalogData && (
          <div className="space-y-6">
            {/* Summary */}
            <div className="bg-gray-50 rounded-md p-4">
              <h4 className="font-medium mb-2">Catalog Summary</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Items:</span>
                  <span className="ml-2 font-medium">{catalogData.items.length}</span>
                </div>
                <div>
                  <span className="text-gray-600">Categories:</span>
                  <span className="ml-2 font-medium">{catalogData.categories.length}</span>
                </div>
                <div>
                  <span className="text-gray-600">Total Variations:</span>
                  <span className="ml-2 font-medium">
                    {catalogData.items.reduce((sum, item) => sum + item.variations.length, 0)}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Avg Variations/Item:</span>
                  <span className="ml-2 font-medium">
                    {catalogData.items.length > 0
                      ? (catalogData.items.reduce((sum, item) => sum + item.variations.length, 0) / catalogData.items.length).toFixed(1)
                      : '0'
                    }
                  </span>
                </div>
              </div>
            </div>

            {/* Items with Variations */}
            <div>
              <h4 className="font-medium mb-4">Items and Variations</h4>
              <div className="space-y-4">
                {catalogData.items.map((item, index) => (
                  <div key={item.id} className="border rounded-md p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h5 className="font-medium text-gray-900">{item.item_data.name}</h5>
                        <p className="text-sm text-gray-600">ID: {item.id}</p>
                        {item.item_data.description && (
                          <p className="text-sm text-gray-600 mt-1">{item.item_data.description}</p>
                        )}
                      </div>
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                        {item.variations.length} variation{item.variations.length !== 1 ? 's' : ''}
                      </span>
                    </div>

                    {/* Variations */}
                    {item.variations.length > 0 && (
                      <div className="mt-3">
                        <h6 className="text-sm font-medium text-gray-700 mb-2">Variations:</h6>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                          {item.variations.map((variation, vIndex) => (
                            <div key={variation.id} className="bg-gray-50 rounded p-3">
                              <div className="text-sm">
                                <div className="font-medium">{variation.item_variation_data?.name || 'Unnamed'}</div>
                                <div className="text-gray-600">ID: {variation.id}</div>
                                {variation.item_variation_data?.price_money && (
                                  <div className="text-green-600 font-medium">
                                    ${(variation.item_variation_data.price_money.amount / 100).toFixed(2)}
                                  </div>
                                )}
                                {variation.item_variation_data?.sku && (
                                  <div className="text-gray-500 text-xs">SKU: {variation.item_variation_data.sku}</div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Categories */}
            {catalogData.categories.length > 0 && (
              <div>
                <h4 className="font-medium mb-4">Categories</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {catalogData.categories.map((category) => (
                    <div key={category.id} className="bg-gray-50 rounded p-3">
                      <div className="font-medium">{category.category_data?.name || 'Unnamed'}</div>
                      <div className="text-sm text-gray-600">ID: {category.id}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </MainLayout>
  );
}
