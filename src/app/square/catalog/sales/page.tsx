'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import { squareApi } from '@/services/apiService';
import Link from 'next/link';

// Create a client component that uses useSearchParams
function CatalogSalesContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get query parameters
  const startDateParam = searchParams.get('startDate');
  const endDateParam = searchParams.get('endDate');
  const catalogItemIdParam = searchParams.get('catalogItemId');
  const locationIdParam = searchParams.get('locationId');

  // State for date range picker - default to today only
  const [startDate, setStartDate] = useState<string>(
    startDateParam || new Date().toISOString().split('T')[0]
  );
  const [endDate, setEndDate] = useState<string>(
    endDateParam || new Date().toISOString().split('T')[0]
  );
  const [selectedCatalogItemId, setSelectedCatalogItemId] = useState<string | undefined>(
    catalogItemIdParam || undefined
  );
  const [selectedLocationId, setSelectedLocationId] = useState<string | undefined>(
    locationIdParam || undefined
  );

  // State for sales data
  const [salesData, setSalesData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // State for catalog items
  const [catalogItems, setCatalogItems] = useState<any[]>([]);
  const [loadingCatalog, setLoadingCatalog] = useState<boolean>(true);

  // State for Square locations
  const [locations, setLocations] = useState<any[]>([]);
  const [loadingLocations, setLoadingLocations] = useState<boolean>(true);

  // Fetch catalog items and locations on component mount
  useEffect(() => {
    const fetchCatalogItems = async () => {
      try {
        setLoadingCatalog(true);
        console.log('🟨 Starting catalog items fetch...');

        // Add timeout to prevent hanging
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Catalog fetch timeout')), 10000);
        });

        const catalogPromise = squareApi.getCatalog();
        const catalogData = await Promise.race([catalogPromise, timeoutPromise]);

        console.log('✅ Catalog items fetched successfully');
        setCatalogItems(catalogData.items || []);
      } catch (err: any) {
        console.error('❌ Error fetching catalog items:', err);
        setCatalogItems([]); // Set empty array on error
        // Don't set error here, as we still want to try fetching sales data
      } finally {
        setLoadingCatalog(false);
      }
    };

    const fetchLocations = async () => {
      try {
        setLoadingLocations(true);
        console.log('🟨 Starting locations fetch...');

        // Add timeout to prevent hanging
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Locations fetch timeout')), 10000);
        });

        const locationsPromise = squareApi.getLocations();
        const locationsData = await Promise.race([locationsPromise, timeoutPromise]);

        console.log('✅ Locations fetched successfully');
        setLocations(locationsData.locations || []);

        // If no location is selected and we have locations, select the first one
        if (!selectedLocationId && locationsData.locations && locationsData.locations.length > 0) {
          setSelectedLocationId(locationsData.locations[0].id);
        }
      } catch (err: any) {
        console.error('❌ Error fetching Square locations:', err);
        setLocations([]); // Set empty array on error
        // Don't set error here, as we still want to try fetching sales data
      } finally {
        setLoadingLocations(false);
      }
    };

    // Fetch both concurrently and don't block the page
    fetchCatalogItems();
    fetchLocations();
  }, [selectedLocationId]);

  // Fetch sales data when date range changes or on initial load
  useEffect(() => {
    const fetchSalesData = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log('🟨 Starting sales data fetch...');

        const params: {
          startDate: string;
          endDate?: string;
          catalogItemId?: string;
          locationId?: string;
        } = {
          startDate
        };

        if (endDate) {
          params.endDate = endDate;
        }

        if (selectedCatalogItemId) {
          params.catalogItemId = selectedCatalogItemId;
        }

        if (selectedLocationId) {
          params.locationId = selectedLocationId;
        }

        // Add timeout to prevent hanging
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Sales data fetch timeout after 15 seconds')), 15000);
        });

        const salesPromise = squareApi.getCatalogSales(params);
        const data = await Promise.race([salesPromise, timeoutPromise]);

        console.log('✅ Sales data fetched successfully');
        setSalesData(data);
      } catch (err: any) {
        console.error('❌ Error fetching sales data:', err);
        setError(err.message || 'Failed to load sales data');
        setSalesData(null);
      } finally {
        setLoading(false);
      }
    };

    // Fetch sales data if we have a start date
    // Don't require location ID - let the backend handle missing location
    if (startDate) {
      fetchSalesData();
    } else {
      setLoading(false);
      setError('Please select a start date');
    }
  }, [startDate, endDate, selectedCatalogItemId, selectedLocationId]);

  // Update URL when filters change
  useEffect(() => {
    // Only update URL if we have the required parameters
    if (startDate && selectedLocationId) {
      // Update URL with query parameters
      const params = new URLSearchParams();
      params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);
      if (selectedCatalogItemId) params.append('catalogItemId', selectedCatalogItemId);
      params.append('locationId', selectedLocationId);

      // Use replace instead of push to avoid adding to browser history
      router.replace(`/square/catalog/sales?${params.toString()}`, { scroll: false });
    }
  }, [startDate, endDate, selectedCatalogItemId, selectedLocationId, router]);

  // Calculate total sales
  const totalSales = salesData?.catalogSales?.reduce(
    (sum: number, item: any) => sum + item.revenue, 0
  ) || 0;

  // Calculate total quantity
  const totalQuantity = salesData?.catalogSales?.reduce(
    (sum: number, item: any) => sum + item.quantity, 0
  ) || 0;

  return (
    <>
      <div className="mb-6 flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-800">Square Catalog Sales</h2>
        <Link
          href="/square/catalog"
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
        >
          Back to Catalog
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <h3 className="text-lg font-medium mb-4">Sales Report</h3>

        <div className="mb-6">
          <h4 className="text-md font-medium text-gray-700 mb-3">Filter Sales Data</h4>
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-sm text-blue-800">
              📅 <strong>Default:</strong> Sales data is limited to today's date for optimal performance.
              You can adjust the date range below if needed.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <input
                type="date"
                id="startDate"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <input
                type="date"
                id="endDate"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label htmlFor="locationId" className="block text-sm font-medium text-gray-700 mb-1">
                Square Location
              </label>
              <select
                id="locationId"
                value={selectedLocationId || ''}
                onChange={(e) => setSelectedLocationId(e.target.value || undefined)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="" disabled>Select a location</option>
                {locations.map((location) => (
                  <option key={location.id} value={location.id}>
                    {location.name}
                  </option>
                ))}
              </select>
              {loadingLocations && (
                <div className="mt-1 text-sm text-gray-500">Loading locations...</div>
              )}
            </div>

            <div>
              <label htmlFor="catalogItem" className="block text-sm font-medium text-gray-700 mb-1">
                Catalog Item (Optional)
              </label>
              <select
                id="catalogItem"
                value={selectedCatalogItemId || ''}
                onChange={(e) => setSelectedCatalogItemId(e.target.value || undefined)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Items</option>
                {catalogItems.map((item) => (
                  <option key={item.id} value={item.id}>
                    {item.name}
                  </option>
                ))}
              </select>
              {loadingCatalog && (
                <div className="mt-1 text-sm text-gray-500">Loading catalog items...</div>
              )}
            </div>
          </div>
          <div className="mt-2 text-sm text-gray-500">
            Data updates automatically when filters change
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error loading sales data</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
                {error.includes('not configured') && (
                  <p className="text-sm text-red-600 mt-2">
                    Please go to <a href="/square/catalog" className="underline">Square Catalog</a> to configure your access token.
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {loading ? (
          <div className="flex flex-col justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
            <p className="text-gray-600 text-sm">Loading sales data...</p>
            <p className="text-gray-500 text-xs mt-1">This may take a few moments</p>
          </div>
        ) : salesData && salesData.catalogSales && salesData.catalogSales.length > 0 ? (
          <div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div className="bg-blue-50 border border-blue-100 rounded-lg p-4">
                <h4 className="text-sm font-medium text-blue-800 mb-1">Total Sales</h4>
                <p className="text-2xl font-bold text-blue-900">${totalSales.toFixed(2)}</p>
              </div>

              <div className="bg-green-50 border border-green-100 rounded-lg p-4">
                <h4 className="text-sm font-medium text-green-800 mb-1">Total Items Sold</h4>
                <p className="text-2xl font-bold text-green-900">{totalQuantity}</p>
              </div>

              <div className="bg-purple-50 border border-purple-100 rounded-lg p-4">
                <h4 className="text-sm font-medium text-purple-800 mb-1">Total Orders</h4>
                <p className="text-2xl font-bold text-purple-900">{salesData.totalOrders}</p>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Item Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity Sold
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Revenue
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Orders
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Average Price
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {salesData.catalogSales.map((item: any) => (
                    <tr key={item.catalogItemId}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{item.mealName}</div>
                        {item.vietnameseName && (
                          <div className="text-sm text-gray-500">{item.vietnameseName}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.quantity}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${item.revenue.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.orders}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${(item.revenue / item.quantity).toFixed(2)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            No sales data found for the selected period.
          </div>
        )}
      </div>
    </>
  );
}

export default function CatalogSalesPage() {
  return (
    <MainLayout requiredRoles={['admin', 'manager']}>
      <Suspense fallback={<div>Loading...</div>}>
        <CatalogSalesContent />
      </Suspense>
    </MainLayout>
  );
}
