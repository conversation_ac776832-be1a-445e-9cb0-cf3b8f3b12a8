'use client';

import React, { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { squareApi, itemsApi } from '@/services/apiService';
import { Item } from '@/types/inventory';

export default function SquareCatalogPage() {
  const [catalogItems, setCatalogItems] = useState<any[]>([]);
  const [items, setItems] = useState<Item[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [cursor, setCursor] = useState<string | null>(null);
  const [totalItems, setTotalItems] = useState(0);
  const pageSize = 50;
  const [syncStatus, setSyncStatus] = useState<{
    inProgress: boolean;
    results: any | null;
    error: string | null;
  }>({
    inProgress: false,
    results: null,
    error: null
  });
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [overwriteExisting, setOverwriteExisting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [accessToken, setAccessToken] = useState('');
  const [showAccessTokenInput, setShowAccessTokenInput] = useState(false);
  const [accessTokenSaved, setAccessTokenSaved] = useState(false);
  const [savingToken, setSavingToken] = useState(false);

  // State to track if Square is configured
  const [isConfigured, setIsConfigured] = useState<boolean>(false);

  // Fetch Square configuration status and catalog data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check if Square API is configured
        const configStatus = await squareApi.checkConfig();
        setIsConfigured(configStatus.configured);

        // If Square is configured, fetch catalog items
        if (configStatus.configured) {
          // Fetch Square catalog items WITHOUT pagination to avoid loops
          console.log('🟨 Fetching Square catalog items...');
          const catalogData = await squareApi.getCatalog();
          console.log('✅ Square catalog fetched:', catalogData.items?.length || 0, 'items');
          setCatalogItems(catalogData.items || []);
          setHasMore(false); // Disable pagination for now to prevent loops
          setTotalItems(catalogData.items?.length || 0);
        } else {
          // If not configured, show access token input
          setShowAccessTokenInput(true);
        }

        // Fetch our items to check which ones are already linked (no pagination needed)
        const itemsResult = await itemsApi.getAll({ limit: 1000 });
        setItems(itemsResult.data || []);
      } catch (err: any) {
        console.error('Error fetching data:', err);
        setError(err.message || 'Failed to load catalog data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []); // Only run once on mount to prevent loops

  // Filter catalog items based on search term
  const filteredItems = catalogItems.filter(item => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      item.name.toLowerCase().includes(searchLower) ||
      item.description?.toLowerCase().includes(searchLower) ||
      item.category?.toLowerCase().includes(searchLower)
    );
  });

  // Check if an item is already linked to our system
  const isItemLinked = (itemId: string) => {
    return items.some(item => item.squareItemId === itemId);
  };

  // Handle item selection
  const toggleItemSelection = (itemId: string) => {
    if (selectedItems.includes(itemId)) {
      setSelectedItems(selectedItems.filter(id => id !== itemId));
    } else {
      setSelectedItems([...selectedItems, itemId]);
    }
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedItems.length === filteredItems.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredItems.map(item => item.id));
    }
  };

  // Handle sync with items
  const handleSync = async () => {
    if (selectedItems.length === 0) {
      setSyncStatus({
        ...syncStatus,
        error: 'Please select at least one item to sync'
      });
      return;
    }

    try {
      setSyncStatus({
        inProgress: true,
        results: null,
        error: null
      });

      // Get selected items
      const itemsToSync = catalogItems.filter(item => selectedItems.includes(item.id));

      // Sync with items
      const results = await squareApi.syncCatalog(itemsToSync, overwriteExisting);

      setSyncStatus({
        inProgress: false,
        results,
        error: null
      });

      // Refresh items list
      const itemsResult = await itemsApi.getAll();
      setItems(itemsResult.data || []);
    } catch (err: any) {
      console.error('Error syncing catalog:', err);
      setSyncStatus({
        inProgress: false,
        results: null,
        error: err.message || 'Failed to sync catalog'
      });
    }
  };

  // Handle saving access token
  const handleSaveAccessToken = async () => {
    if (!accessToken) {
      setError('Please enter an access token');
      return;
    }

    try {
      console.log('🟨 Starting to save Square access token...');
      setError(null); // Clear any previous errors
      setSavingToken(true);

      // Use the API service to save the access token
      const result = await squareApi.saveConfig(accessToken);
      console.log('✅ Square access token saved successfully:', result);

      setAccessToken(''); // Clear the input for security
      setAccessTokenSaved(true);
      setIsConfigured(true);

      // Fetch catalog data now that we have a token
      setLoading(true);
      try {
        console.log('🟨 Fetching catalog data after token save...');
        const catalogData = await squareApi.getCatalog();
        setCatalogItems(catalogData.items || []);
        console.log('✅ Catalog data fetched successfully');
      } catch (catalogErr: any) {
        console.error('❌ Error fetching catalog after token save:', catalogErr);
        setError(`Failed to load catalog data: ${catalogErr.message}`);
      } finally {
        setLoading(false);
      }

      setTimeout(() => {
        setShowAccessTokenInput(false);
        setAccessTokenSaved(false);
      }, 2000);
    } catch (err: any) {
      console.error('❌ Error saving access token:', err);
      setError(`Failed to save access token: ${err.message || 'Unknown error'}`);
    } finally {
      setSavingToken(false);
    }
  };

  return (
    <MainLayout requiredRoles={['admin']}>
      <div className="mb-6 flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-800">Square Catalog Integration</h2>
        <div className="flex space-x-3">
          <a
            href="/square/catalog/sales"
            className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-700"
          >
            View Sales Data
          </a>
          <a
            href="/square/pork-chop-consumption"
            className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-700"
          >
            Pork Chop Analysis
          </a>
          <button
            onClick={() => setShowAccessTokenInput(!showAccessTokenInput)}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
          >
            {showAccessTokenInput ? 'Hide Access Token' : 'Set Access Token'}
          </button>
        </div>
      </div>

      {showAccessTokenInput && (
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h3 className="text-lg font-medium mb-4">Square Access Token</h3>
          <div className="flex items-center space-x-4">
            <input
              type="password"
              value={accessToken}
              onChange={(e) => setAccessToken(e.target.value)}
              placeholder="Enter your Square access token"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              onClick={handleSaveAccessToken}
              disabled={savingToken || !accessToken}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {savingToken ? 'Saving...' : 'Save'}
            </button>
          </div>
          {accessTokenSaved && (
            <p className="mt-2 text-green-600">Access token saved successfully!</p>
          )}
          <p className="mt-2 text-sm text-gray-500">
            Note: In a production environment, this token should be stored securely on the server.
          </p>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-6">
          <p className="font-medium">Error:</p>
          <p>{error}</p>
          <button
            onClick={() => setError(null)}
            className="mt-2 px-2 py-1 bg-red-100 text-red-800 rounded text-xs"
          >
            Dismiss
          </button>
        </div>
      )}

      {syncStatus.error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-6">
          {syncStatus.error}
        </div>
      )}

      {syncStatus.results && (
        <div className="bg-green-50 border border-green-200 text-green-800 rounded-md p-4 mb-6">
          <p>{syncStatus.results.message || 'Sync completed successfully!'}</p>
          {!syncStatus.results.message && (
            <ul className="mt-2">
              <li>Created: {syncStatus.results.created}</li>
              <li>Updated: {syncStatus.results.updated}</li>
              <li>Skipped: {syncStatus.results.skipped}</li>
              <li>Errors: {syncStatus.results.errors}</li>
            </ul>
          )}
        </div>
      )}

      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">Items</h3>
          {isConfigured && (
            <div className="flex items-center space-x-4">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search items..."
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                onClick={handleSelectAll}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
              >
                {selectedItems.length === filteredItems.length ? 'Deselect All' : 'Select All'}
              </button>
            </div>
          )}
        </div>

        {!isConfigured && !loading ? (
          <div className="text-center py-8">
            <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-md p-4 mb-6 inline-block">
              <p className="font-medium">Square API is not configured</p>
              <p className="mt-2">Please set your Square access token to view catalog items.</p>
              {!showAccessTokenInput && (
                <button
                  onClick={() => setShowAccessTokenInput(true)}
                  className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                >
                  Set Access Token
                </button>
              )}
            </div>
          </div>
        ) : loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : filteredItems.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {searchTerm ? 'No items match your search' : 'No catalog items found'}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <input
                      type="checkbox"
                      checked={selectedItems.length === filteredItems.length && filteredItems.length > 0}
                      onChange={handleSelectAll}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Description
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredItems.map((item) => (
                  <tr key={item.id} className={isItemLinked(item.id) ? 'bg-blue-50' : ''}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedItems.includes(item.id)}
                        onChange={() => toggleItemSelection(item.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{item.name}</div>
                      {item.variations && item.variations.length > 0 && (
                        <div className="text-xs text-gray-500 mt-1">
                          {item.variations.length} variation{item.variations.length !== 1 ? 's' : ''}
                          {item.variations.filter((v: any) => !v.is_deleted).length !== item.variations.length && (
                            <span className="text-red-500 ml-1">
                              ({item.variations.filter((v: any) => v.is_deleted).length} deleted)
                            </span>
                          )}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-500 truncate max-w-xs">{item.description || '-'}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">
                        {(() => {
                          if (!item.variations || item.variations.length === 0) return '-';

                          // Find the oldest, non-deleted variation
                          const validVariations = item.variations.filter((v: any) => !v.is_deleted);
                          if (validVariations.length === 0) return 'No active variations';

                          const oldestVariation = validVariations.sort((a: any, b: any) =>
                            new Date(a.created_at || a.updated_at).getTime() - new Date(b.created_at || b.updated_at).getTime()
                          )[0];

                          const price = oldestVariation.item_variation_data?.price_money?.amount;
                          return price ? `$${(price / 100).toFixed(2)}` : 'No price';
                        })()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {isItemLinked(item.id) ? (
                        <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          Linked
                        </span>
                      ) : (
                        <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                          Not Linked
                        </span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination temporarily disabled to prevent infinite loops */}
        {isConfigured && !loading && catalogItems.length > 0 && (
          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Showing all {catalogItems.length} catalog items
            </p>
          </div>
        )}
      </div>

      {isConfigured && (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium mb-4">Sync Selected Items</h3>
          <div className="mb-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={overwriteExisting}
                onChange={() => setOverwriteExisting(!overwriteExisting)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2"
              />
              <span className="text-sm text-gray-700">Overwrite existing items</span>
            </label>
            <p className="mt-1 text-xs text-gray-500">
              If checked, existing items with the same Square ID will be updated. Otherwise, they will be skipped.
            </p>
          </div>
          <button
            onClick={handleSync}
            disabled={selectedItems.length === 0 || syncStatus.inProgress}
            className="w-full px-4 py-2 bg-yellow-500 text-black rounded-md hover:bg-yellow-700 disabled:opacity-50"
          >
            {syncStatus.inProgress ? 'Syncing...' : `Sync ${selectedItems.length} Selected Items`}
          </button>
        </div>
      )}
    </MainLayout>
  );
}
