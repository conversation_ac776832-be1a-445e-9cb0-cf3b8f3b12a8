'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import Link from 'next/link';

export default function LightDashboardPage() {
  const [lowStockCount, setLowStockCount] = useState<number>(0);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [reloading, setReloading] = useState(false);

  // Fetch data function (can be called for initial load or reload)
  const fetchMinimalData = async (isReload = false) => {
    try {
      if (isReload) {
        setReloading(true);
        setError(null);
      } else {
        setLoading(true);
        setError(null); // Clear any previous errors
      }
      console.log('🔄 Loading minimal dashboard data...');

      // Add timeout to the fetch itself with shorter timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        console.warn('⚠️ Aborting fetch due to timeout');
        controller.abort();
      }, 3000); // 3 second timeout to prevent hanging

        try {
          // Get only essential inventory data with timeout
          const inventoryResponse = await fetch('/api/inventory/items?limit=100', {
            signal: controller.signal
          });

          clearTimeout(timeoutId);

          if (inventoryResponse.ok) {
            const inventoryResult = await inventoryResponse.json();
            const inventoryItems = inventoryResult.data || [];

            // Calculate low stock items
            const lowStock = inventoryItems.filter((item: any) =>
              item.minThreshold && item.quantity < item.minThreshold
            );

            setLowStockCount(lowStock.length);
            setTotalItems(inventoryItems.length);
            console.log('✅ Dashboard data loaded:', inventoryItems.length, 'items,', lowStock.length, 'low stock');
          } else {
            console.warn('Failed to fetch inventory data - using defaults');
            setTotalItems(0);
            setLowStockCount(0);
          }
        } catch (fetchError: any) {
          clearTimeout(timeoutId);
          if (fetchError.name === 'AbortError') {
            console.warn('⚠️ Dashboard fetch timed out - using defaults');
            setError('Dashboard loading timed out after 3 seconds. Using default values.');
          } else {
            console.warn('⚠️ Dashboard fetch failed - using defaults:', fetchError.message);
            setError('Failed to load data. Using default values.');
          }
          setTotalItems(0);
          setLowStockCount(0);
        }
      } catch (error: any) {
        console.error('❌ Error loading dashboard data:', error);
        setError(`Failed to load dashboard data: ${error.message}`);
        setTotalItems(0);
        setLowStockCount(0);
      } finally {
        setLoading(false);
        setReloading(false);
      }
    };

  // Load minimal data on component mount
  useEffect(() => {
    let isMounted = true;

    // Add a hard timeout to prevent hanging completely
    const hardTimeoutId = setTimeout(() => {
      if (isMounted) {
        console.warn('⚠️ Hard timeout reached - forcing loading to stop');
        setLoading(false);
        setReloading(false);
        setError('Dashboard loading took too long. Using default values.');
        setTotalItems(0);
        setLowStockCount(0);
      }
    }, 5000); // 5 second hard timeout

    // Fetch data with proper cleanup
    fetchMinimalData()
      .catch((error) => {
        if (isMounted) {
          console.error('Dashboard fetch error:', error);
          setLoading(false);
          setReloading(false);
          setError('Failed to load dashboard data.');
          setTotalItems(0);
          setLowStockCount(0);
        }
      })
      .finally(() => {
        if (isMounted) {
          clearTimeout(hardTimeoutId);
        }
      });

    // Cleanup on unmount
    return () => {
      isMounted = false;
      clearTimeout(hardTimeoutId);
    };
  }, []);

  // Quick navigation items with updated colors
  const navigationItems = [
    {
      title: 'Inventory Items',
      description: 'Manage your inventory',
      href: '/inventory',
      icon: '📦',
      color: 'bg-indigo-600',
      count: totalItems
    },
    {
      title: 'Menu Items',
      description: 'Manage menu items',
      href: '/items',
      icon: '🍽️',
      color: 'bg-emerald-600',
      count: null
    },
    {
      title: 'Square Integration',
      description: 'Sync with Square',
      href: '/square/catalog',
      icon: '🔄',
      color: 'bg-violet-600',
      count: null
    },
    {
      title: 'Reports',
      description: 'View analytics',
      href: '/reports',
      icon: '📊',
      color: 'bg-amber-600',
      count: null
    }
  ];

  // Emergency fallback if everything fails
  if (!loading && error && totalItems === 0 && lowStockCount === 0) {
    return (
      <MainLayout>
        <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8 py-4 sm:py-8">
          <div className="text-center py-12">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Dashboard</h1>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
              <p className="text-yellow-800 mb-4">Dashboard is having trouble loading data.</p>
              <div className="space-y-4">
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 mr-4"
                >
                  🔄 Refresh Page
                </button>
                <Link
                  href="/inventory"
                  className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                >
                  📦 Go to Inventory
                </Link>
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8 py-4 sm:py-8">
        {/* Header */}
        <div className="mb-6 sm:mb-8 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600 mt-1 sm:mt-2 text-sm sm:text-base">Quick overview and navigation</p>
          </div>
          <button
            onClick={() => fetchMinimalData(true)}
            disabled={loading || reloading}
            className="flex items-center px-3 sm:px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base w-full sm:w-auto justify-center"
          >
            {reloading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                Reloading...
              </>
            ) : (
              <>
                🔄 Refresh Data
              </>
            )}
          </button>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex flex-col sm:flex-row justify-center items-center py-8 sm:py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            <span className="ml-0 sm:ml-2 mt-2 sm:mt-0 text-gray-600 text-sm sm:text-base">Loading dashboard...</span>
            <div className="ml-0 sm:ml-4 mt-1 sm:mt-0 text-xs sm:text-sm text-gray-500">
              (Max 3 seconds)
            </div>
          </div>
        )}

        {/* Error State - Less intrusive */}
        {error && !loading && (
          <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-md p-3 mb-4 sm:mb-6">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
              <div className="flex items-start">
                <span className="text-lg mr-2 flex-shrink-0">⚠️</span>
                <div>
                  <p className="text-sm">{error}</p>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
                <button
                  onClick={() => fetchMinimalData(true)}
                  disabled={reloading}
                  className="flex items-center justify-center px-3 py-1 bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200 disabled:opacity-50 text-sm w-full sm:w-auto"
                >
                  {reloading ? (
                    <>
                      <div className="animate-spin rounded-full h-3 w-3 border-t border-b border-yellow-800 mr-1"></div>
                      Reloading...
                    </>
                  ) : (
                    <>
                      🔄 Reload Data
                    </>
                  )}
                </button>
                <button
                  onClick={() => window.location.reload()}
                  className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200 text-xs w-full sm:w-auto"
                >
                  Full Refresh
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Main Content - Show even with errors for graceful degradation */}
        {!loading && (
          <>
            {/* Alert Section */}
            {lowStockCount > 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6">
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <span className="text-xl sm:text-2xl">⚠️</span>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-base sm:text-lg font-medium text-yellow-800">
                        Low Stock Alert
                      </h3>
                      <p className="text-yellow-700 text-sm sm:text-base">
                        {lowStockCount} item{lowStockCount !== 1 ? 's' : ''} running low on stock
                      </p>
                    </div>
                  </div>
                  <Link
                    href="/inventory?filter=lowStock"
                    className="mt-0 sm:mt-2 inline-flex items-center justify-center px-3 py-2 border border-yellow-300 text-sm font-medium rounded-md text-yellow-800 bg-yellow-100 hover:bg-yellow-200 w-full sm:w-auto"
                  >
                    View Low Stock Items
                  </Link>
                </div>
              </div>
            )}

            {/* Quick Stats */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6 mb-6 sm:mb-8">
              <div className="bg-white rounded-lg shadow-sm p-3 sm:p-6 border">
                <div className="flex flex-col sm:flex-row items-center sm:items-start">
                  <div className="flex-shrink-0 mb-2 sm:mb-0">
                    <span className="text-2xl sm:text-3xl">📦</span>
                  </div>
                  <div className="sm:ml-4 text-center sm:text-left">
                    <p className="text-xs sm:text-sm font-medium text-gray-600">Total Items</p>
                    <p className="text-lg sm:text-2xl font-bold text-gray-900">{totalItems}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-3 sm:p-6 border">
                <div className="flex flex-col sm:flex-row items-center sm:items-start">
                  <div className="flex-shrink-0 mb-2 sm:mb-0">
                    <span className="text-2xl sm:text-3xl">⚠️</span>
                  </div>
                  <div className="sm:ml-4 text-center sm:text-left">
                    <p className="text-xs sm:text-sm font-medium text-gray-600">Low Stock</p>
                    <p className="text-lg sm:text-2xl font-bold text-red-600">{lowStockCount}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-3 sm:p-6 border">
                <div className="flex flex-col sm:flex-row items-center sm:items-start">
                  <div className="flex-shrink-0 mb-2 sm:mb-0">
                    <span className="text-2xl sm:text-3xl">✅</span>
                  </div>
                  <div className="sm:ml-4 text-center sm:text-left">
                    <p className="text-xs sm:text-sm font-medium text-gray-600">In Stock</p>
                    <p className="text-lg sm:text-2xl font-bold text-green-600">{totalItems - lowStockCount}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-3 sm:p-6 border">
                <div className="flex flex-col sm:flex-row items-center sm:items-start">
                  <div className="flex-shrink-0 mb-2 sm:mb-0">
                    <span className="text-2xl sm:text-3xl">🔄</span>
                  </div>
                  <div className="sm:ml-4 text-center sm:text-left">
                    <p className="text-xs sm:text-sm font-medium text-gray-600">Status</p>
                    <p className="text-base sm:text-lg font-bold text-blue-600">Active</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Navigation */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
              {navigationItems.map((item, index) => (
                <Link
                  key={index}
                  href={item.href}
                  className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200 p-4 sm:p-6 block"
                >
                  <div className="flex flex-col sm:flex-row items-center sm:items-start justify-between gap-3">
                    <div className="flex flex-col sm:flex-row items-center sm:items-start w-full">
                      <div className={`${item.color} rounded-lg p-2 sm:p-3 text-white text-xl sm:text-2xl flex-shrink-0`}>
                        {item.icon}
                      </div>
                      <div className="sm:ml-4 text-center sm:text-left flex-grow">
                        <h3 className="text-base sm:text-lg font-medium text-gray-900">{item.title}</h3>
                        <p className="text-xs sm:text-sm text-gray-600 mt-1">{item.description}</p>
                      </div>
                    </div>
                    {item.count !== null && (
                      <div className="text-center sm:text-right flex-shrink-0">
                        <p className="text-xl sm:text-2xl font-bold text-gray-900">{item.count}</p>
                      </div>
                    )}
                  </div>
                </Link>
              ))}
            </div>
          </>
        )}
      </div>
    </MainLayout>
  );
}
