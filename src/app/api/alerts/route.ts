import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

// GET /api/alerts - MIGRATED TO SUPABASE
export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Alerts API has been migrated to Supabase',
    status: 'migration_complete',
    note: 'Please use Supabase client directly for alerts operations.'
  }, { status: 410 });
}
