import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

/**
 * GET /api/reports
 * Get all reports - MIGRATED TO SUPABASE
 */
export async function GET(_request: NextRequest) {
  return NextResponse.json({
    message: 'Reports API has been migrated to Supabase',
    status: 'migration_complete',
    note: 'Please use Supabase client directly for reports operations.'
  }, { status: 410 });
}
