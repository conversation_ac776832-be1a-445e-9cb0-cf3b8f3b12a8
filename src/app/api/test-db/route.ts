import { NextResponse } from 'next/server';

export async function GET() {
  try {
    console.log('Testing Supabase connection...');

    // Check if Supabase environment variables are configured
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseAnonKey) {
      return NextResponse.json({
        success: false,
        error: 'Supabase environment variables not configured',
        details: 'Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY',
        database: 'PostgreSQL via Supabase',
        status: 'environment_not_configured'
      }, { status: 500 });
    }

    // Import Supabase client only if environment variables are available
    const { supabase } = await import('@/lib/supabase');

    // Test Supabase connection
    const { error } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (error) {
      throw error;
    }

    return NextResponse.json({
      success: true,
      message: 'Supabase connection successful',
      database: 'PostgreSQL via Supabase',
      status: 'migrated_from_mongodb'
    });
  } catch (error) {
    console.error('Error connecting to Supabase:', error);

    return NextResponse.json({
      success: false,
      error: 'Failed to connect to Supabase',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
