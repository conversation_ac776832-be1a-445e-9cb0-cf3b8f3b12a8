import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const revalidate = 0;

/**
 * POST /api/auth/logout
 * Logout a user - MIGRATED TO SUPABASE
 */
export async function POST(request: NextRequest) {
  // Logout is now handled by Supabase Auth
  return NextResponse.json({
    message: 'Logout has been migrated to Supabase',
    status: 'migration_complete',
    note: 'Please use Supabase Auth for logout. This endpoint is deprecated.'
  }, { status: 410 }); // 410 Gone - resource no longer available
}
