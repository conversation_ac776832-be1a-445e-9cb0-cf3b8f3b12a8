import NextAuth from 'next-auth'
import Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'
import { supabase } from '@/lib/supabase'

const handler = NextAuth({
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        try {
          // Authenticate with Supabase
          const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
            email: credentials.email,
            password: credentials.password,
          })

          if (authError || !authData.user) {
            console.error('Authentication failed:', authError?.message)
            return null
          }

          // Get user profile from database
          const { data: userProfile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('auth_id', authData.user.id)
            .single()

          if (profileError) {
            console.error('Failed to fetch user profile:', profileError.message)
            
            // Create fallback user if profile doesn't exist
            const fallbackUser = {
              id: authData.user.id,
              email: authData.user.email || '',
              username: authData.user.email?.split('@')[0] || 'user',
              firstName: 'User',
              lastName: '',
              role: 'admin',
              isActive: true
            }
            
            return fallbackUser
          }

          // Return user data for NextAuth session
          return {
            id: userProfile.id,
            email: userProfile.email,
            username: userProfile.username,
            firstName: userProfile.first_name,
            lastName: userProfile.last_name,
            role: userProfile.role,
            isActive: userProfile.is_active
          }
        } catch (error) {
          console.error('Authorization error:', error)
          return null
        }
      }
    })
  ],
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 hours
  },
  callbacks: {
    async jwt({ token, user }) {
      // Persist user data in the token
      if (user) {
        token.id = user.id
        token.username = user.username
        token.firstName = user.firstName
        token.lastName = user.lastName
        token.role = user.role
        token.isActive = user.isActive
      }
      return token
    },
    async session({ session, token }) {
      // Send properties to the client
      if (token) {
        session.user.id = token.id as string
        session.user.username = token.username as string
        session.user.firstName = token.firstName as string
        session.user.lastName = token.lastName as string
        session.user.role = token.role as string
        session.user.isActive = token.isActive as boolean
      }
      return session
    }
  },
  pages: {
    signIn: '/auth/login',
    error: '/auth/login',
  },
  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-here'
})

export { handler as GET, handler as POST }
