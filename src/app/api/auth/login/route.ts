import { NextRequest, NextResponse } from 'next/server';

/**
 * POST /api/auth/login
 * Login a user - MIGRATED TO SUPABASE
 *
 * This endpoint is no longer used. Authentication is now handled by Supabase.
 * Please use the Supabase Auth context in your React components.
 */
export async function POST(request: NextRequest) {
  return NextResponse.json({
    message: 'Authentication has been migrated to Supabase',
    status: 'migration_complete',
    note: 'Please use Supabase Auth for login. This endpoint is deprecated.'
  }, { status: 410 }); // 410 Gone - resource no longer available
}
