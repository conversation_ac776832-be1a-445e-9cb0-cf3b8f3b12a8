import { NextRequest, NextResponse } from 'next/server';

/**
 * POST /api/auth/register
 * Register a new user - MIGRATED TO SUPABASE
 */
export async function POST(request: NextRequest) {
  // User registration is now handled by Supabase Auth
  return NextResponse.json({
    message: 'User registration has been migrated to Supabase',
    status: 'migration_complete',
    note: 'Please use Supabase Auth for user registration. This endpoint is deprecated.'
  }, { status: 410 }); // 410 Gone - resource no longer available
}
