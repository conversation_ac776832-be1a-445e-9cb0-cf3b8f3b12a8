import type { Metada<PERSON> } from "next";
import { Plus_Jakarta_Sans, Roboto_Mono } from "next/font/google";
import { SupabaseAuthProvider } from "@/contexts/SupabaseAuthContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import NextAuthProvider from "@/components/providers/NextAuthProvider";
import "./globals.css";

const plusJakartaSans = Plus_Jakarta_Sans({
  variable: "--font-plus-jakarta-sans",
  subsets: ["latin"],
  display: "swap",
});

const robotoMono = Roboto_Mono({
  variable: "--font-roboto-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Restaurant Inventory Management",
  description: "A comprehensive inventory management system for Vietnamese restaurants",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${plusJakartaSans.variable} ${robotoMono.variable} antialiased`}
      >
        <NextAuthProvider>
          <SupabaseAuthProvider>
            <ThemeProvider>
              {children}
            </ThemeProvider>
          </SupabaseAuthProvider>
        </NextAuthProvider>
      </body>
    </html>
  );
}
