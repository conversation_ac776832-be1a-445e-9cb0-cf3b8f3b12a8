'use client';

import { useState } from 'react';

export default function TestDatabasePage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testDatabase = async () => {
    try {
      setLoading(true);
      setError(null);
      setResult(null);

      const response = await fetch('/api/test/supabase');
      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'Failed to test database');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to test database');
    } finally {
      setLoading(false);
    }
  };

  const createSampleData = async () => {
    try {
      setLoading(true);
      setError(null);
      setResult(null);

      const response = await fetch('/api/test/create-sample-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'Failed to create sample data');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create sample data');
    } finally {
      setLoading(false);
    }
  };

  const checkTables = async () => {
    try {
      setLoading(true);
      setError(null);
      setResult(null);

      const response = await fetch('/api/debug/tables');
      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'Failed to check tables');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to check tables');
    } finally {
      setLoading(false);
    }
  };

  const createInventoryItems = async () => {
    try {
      setLoading(true);
      setError(null);
      setResult(null);

      const response = await fetch('/api/debug/create-inventory', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'Failed to create inventory items');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create inventory items');
    } finally {
      setLoading(false);
    }
  };

  const createDirectInventory = async () => {
    try {
      setLoading(true);
      setError(null);
      setResult(null);

      const response = await fetch('/api/debug/direct-inventory', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'Failed to create inventory items directly');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create inventory items directly');
    } finally {
      setLoading(false);
    }
  };

  const testInventoryAPI = async () => {
    try {
      setLoading(true);
      setError(null);
      setResult(null);

      const response = await fetch('/api/debug/test-inventory-api');
      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'Failed to test inventory API');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to test inventory API');
    } finally {
      setLoading(false);
    }
  };

  const fixRLSPolicies = async () => {
    try {
      setLoading(true);
      setError(null);
      setResult(null);

      const response = await fetch('/api/debug/fix-rls-policies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'Failed to fix RLS policies');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fix RLS policies');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Database Test & Setup</h1>
          <p className="text-gray-600 mb-8">Test your database connection and create sample data</p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-7 gap-3 mb-8">
            <button
              onClick={testDatabase}
              disabled={loading}
              className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Testing...
                </>
              ) : (
                <>
                  🔍 Test Database
                </>
              )}
            </button>

            <button
              onClick={checkTables}
              disabled={loading}
              className="bg-purple-500 hover:bg-purple-600 disabled:bg-purple-300 text-white px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Checking...
                </>
              ) : (
                <>
                  🗂️ Check Tables
                </>
              )}
            </button>

            <button
              onClick={createInventoryItems}
              disabled={loading}
              className="bg-orange-500 hover:bg-orange-600 disabled:bg-orange-300 text-white px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  📦 Create Inventory
                </>
              )}
            </button>

            <button
              onClick={createDirectInventory}
              disabled={loading}
              className="bg-red-500 hover:bg-red-600 disabled:bg-red-300 text-white px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  🔧 Direct Create
                </>
              )}
            </button>

            <button
              onClick={testInventoryAPI}
              disabled={loading}
              className="bg-indigo-500 hover:bg-indigo-600 disabled:bg-indigo-300 text-white px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Testing...
                </>
              ) : (
                <>
                  🔬 Test API
                </>
              )}
            </button>

            <button
              onClick={fixRLSPolicies}
              disabled={loading}
              className="bg-yellow-500 hover:bg-yellow-600 disabled:bg-yellow-300 text-white px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Fixing...
                </>
              ) : (
                <>
                  🔧 Fix RLS
                </>
              )}
            </button>

            <button
              onClick={createSampleData}
              disabled={loading}
              className="bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  🍽️ Create All Data
                </>
              )}
            </button>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
            <h3 className="font-semibold text-blue-900 mb-2">What these buttons do:</h3>
            <ul className="text-blue-800 text-sm space-y-1">
              <li><strong>🔍 Test Database:</strong> Checks Supabase connection and shows current data count</li>
              <li><strong>🗂️ Check Tables:</strong> Shows detailed table contents and field structures</li>
              <li><strong>📦 Create Inventory:</strong> Creates inventory items via backend API</li>
              <li><strong>🔧 Direct Create:</strong> Creates inventory items directly with service role key</li>
              <li><strong>🔬 Test API:</strong> Compares direct Supabase vs backend API responses</li>
              <li><strong>🔧 Fix RLS:</strong> Creates RLS policies to allow anon key access to inventory_items</li>
              <li><strong>🍽️ Create All Data:</strong> Creates both inventory items and menu items</li>
            </ul>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <div className="text-red-400 mr-3">❌</div>
                <div>
                  <h3 className="font-medium text-red-900">Error</h3>
                  <p className="text-red-700">{error}</p>
                </div>
              </div>
            </div>
          )}

          {result && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="flex items-center mb-3">
                <div className="text-green-500 mr-3">✅</div>
                <h3 className="font-medium text-gray-900">Result</h3>
              </div>
              <div className="bg-white rounded border p-4 max-h-96 overflow-auto">
                <pre className="text-sm text-gray-600 whitespace-pre-wrap">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </div>
            </div>
          )}

          <div className="mt-8 pt-6 border-t border-gray-200">
            <h3 className="font-medium text-gray-900 mb-4">Quick Links:</h3>
            <div className="flex flex-wrap gap-4">
              <a
                href="/inventory"
                className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded-md transition-colors"
              >
                📦 Inventory Page
              </a>
              <a
                href="/items"
                className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded-md transition-colors"
              >
                🍽️ Items Page
              </a>
              <a
                href="/api/test/supabase"
                target="_blank"
                className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded-md transition-colors"
              >
                🔗 Direct API Test
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
