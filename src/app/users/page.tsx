'use client';

import React from 'react';
import MainLayout from '@/components/layout/MainLayout';

export default function UsersPage() {
  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-8 w-8 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium text-blue-800">
                User Management Migrated to Supabase
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  User management has been migrated to Supabase Authentication. 
                  The user management interface is no longer available through this application.
                </p>
                <div className="mt-4">
                  <p className="font-medium">To manage users:</p>
                  <ul className="mt-2 list-disc list-inside space-y-1">
                    <li>Go to your Supabase dashboard</li>
                    <li>Navigate to Authentication → Users</li>
                    <li>View, create, edit, and manage user accounts</li>
                    <li>Set user roles and permissions in the users table</li>
                  </ul>
                </div>
                <div className="mt-4">
                  <p className="font-medium">Benefits of Supabase Auth:</p>
                  <ul className="mt-2 list-disc list-inside space-y-1">
                    <li>Built-in security and authentication</li>
                    <li>Email verification and password reset</li>
                    <li>Social login providers (Google, GitHub, etc.)</li>
                    <li>Row Level Security (RLS) for data protection</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-6 text-center">
          <button
            onClick={() => window.history.back()}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Go Back
          </button>
        </div>
      </div>
    </MainLayout>
  );
}
