/* Global text styles for better readability */
body {
  color: #333333; /* Default text color for better readability */
  background-color: var(--warm-yellow); /* Use theme background color */
}

/* Main layout background */
.main-layout-bg {
  background-color: var(--warm-yellow) !important;
}

/* Header/top background */
.header-bg {
  background-color: var(--light-cream) !important;
}

/* Primary text and borders */
.primary-color {
  color: var(--vibrant-red) !important;
}

.primary-border {
  border-color: var(--vibrant-red) !important;
}

/* Accent elements */
.accent-color {
  color: var(--soft-coral) !important;
}

.accent-bg {
  background-color: var(--soft-coral) !important;
}

/* Subtext background */
.subtext-bg {
  background-color: var(--light-red) !important;
  color: white !important;
}

/* Sidebar styles */
.sidebar-link {
  transition: background-color 0.2s ease;
}

.sidebar-link:hover {
  background-color: var(--sidebar-active-color) !important;
}

.sidebar-bg {
  background-color: var(--sidebar-color) !important;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.sidebar-active {
  background-color: var(--sidebar-active-color) !important;
}

/* Vietnamese restaurant theme colors */
:root {
  /* Default theme colors - will be overridden by ThemeContext */
  --warm-yellow: #FFC845;
  --light-cream: #EAE7D6;
  --vibrant-red: #E63946;
  --soft-coral: #F76C5E;
  --light-red: #F75F48;
  --sidebar-color: #E9E7D1;
  --sidebar-active-color: #D0CEB9;

  /* Legacy variables - mapped to new theme */
  --restaurant-bg: #EAE7D6;
  --restaurant-text-primary: #333333;
  --restaurant-text-secondary: #555555;
  --restaurant-accent: #E63946;
  --restaurant-border: #F76C5E;
  --restaurant-hover: #f5f5f5;
}

/* Dashboard card styles */
.text-gray-400 {
  color: #555555 !important; /* Medium gray for better readability */
}

.text-gray-500 {
  color: #555555 !important; /* Medium gray for better readability */
}

/* Make stat card values more readable */
.text-3xl.font-semibold,
.text-3xl.font-bold {
  color: #222222 !important; /* Very dark gray for important numbers */
}

/* Report page styles */
.bg-white h3.text-lg.font-medium {
  color: #222222 !important; /* Very dark gray for report titles */
}

.bg-white p.text-3xl.font-bold {
  color: #222222 !important; /* Very dark gray for report values */
}

/* Card backgrounds */
.bg-white {
  background-color: #ffffff !important; /* White background */
  color: #333333 !important; /* Dark gray text for good contrast */
  border-color: #e0e0e0 !important; /* Light gray border */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important; /* Subtle shadow for depth */
}

/* Make stat card descriptions more readable */
.text-sm.text-gray-500,
.text-sm.text-gray-400 {
  color: #555555 !important; /* Medium gray for descriptions */
}

/* Table text styles */
table {
  color: #333333 !important; /* Dark gray text for tables */
  background-color: #ffffff !important; /* White background */
  border-color: #e0e0e0 !important; /* Light gray border */
}

table th {
  color: #222222 !important; /* Very dark gray for headers */
  font-weight: 600 !important; /* Make headers slightly bolder */
  background-color: #f5f5f5 !important; /* Very light gray background for headers */
}

table td {
  color: #333333 !important; /* Dark gray for table cells */
  border-color: #e0e0e0 !important; /* Light gray border */
}

/* Table row hover */
tr:hover {
  background-color: #f8f8f8 !important; /* Very light gray hover effect */
}

/* Custom styles for input fields */
input[type="text"],
input[type="number"],
input[type="email"],
input[type="password"],
textarea,
select {
  color: #333333 !important; /* Dark gray text */
  background-color: #ffffff !important; /* White background */
  border: 1px solid #cccccc !important; /* Medium gray border */
}

/* Style for placeholder text */
input::placeholder,
textarea::placeholder {
  color: #777777 !important; /* Medium gray for placeholder text */
  opacity: 1;
}

/* Focus styles */
input:focus,
textarea:focus,
select:focus {
  border-color: var(--restaurant-accent) !important; /* Orange accent border on focus */
  background-color: #ffffff !important; /* White background on focus */
  color: #333333 !important; /* Dark gray text on focus */
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(211, 84, 0, 0.2) !important; /* Subtle orange glow */
}

/* Button styles */
.btn-primary,
button[type="submit"],
.bg-yellow-500 {
  background-color: var(--restaurant-accent) !important; /* Orange accent color */
  color: #ffffff !important; /* White text for contrast on orange */
  border: none !important;
  font-weight: 500 !important;
}

.btn-primary:hover,
button[type="submit"]:hover,
.bg-yellow-700:hover {
  background-color: #b94700 !important; /* Slightly darker orange on hover */
  color: #ffffff !important;
}

/* Secondary buttons */
.btn-secondary,
.bg-gray-200 {
  background-color: #e0e0e0 !important; /* Light gray background */
  color: #333333 !important; /* Dark gray text */
  border: 1px solid #cccccc !important;
}

.btn-secondary:hover,
.bg-gray-200:hover {
  background-color: #d0d0d0 !important; /* Slightly darker on hover */
}
