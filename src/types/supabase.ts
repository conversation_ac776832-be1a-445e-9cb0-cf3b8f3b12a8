export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      alerts: {
        Row: {
          created_at: string
          id: string
          inventory_item_id: string | null
          is_read: boolean | null
          message: string
          type: Database["public"]["Enums"]["alert_type"]
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          inventory_item_id?: string | null
          is_read?: boolean | null
          message: string
          type: Database["public"]["Enums"]["alert_type"]
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          inventory_item_id?: string | null
          is_read?: boolean | null
          message?: string
          type?: Database["public"]["Enums"]["alert_type"]
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "alerts_inventory_item_id_fkey"
            columns: ["inventory_item_id"]
            isOneToOne: false
            referencedRelation: "inventory_items"
            referencedColumns: ["id"]
          }
        ]
      }
      config: {
        Row: {
          created_at: string
          id: string
          key: string
          updated_at: string
          value: Json | null
        }
        Insert: {
          created_at?: string
          id?: string
          key: string
          updated_at?: string
          value?: Json | null
        }
        Update: {
          created_at?: string
          id?: string
          key?: string
          updated_at?: string
          value?: Json | null
        }
        Relationships: []
      }
      inventory_items: {
        Row: {
          category: string | null
          cost_per_unit: number
          created_at: string
          id: string
          last_restock_date: string | null
          min_threshold: number | null
          name: string
          quantity: number
          storage: string | null
          supplier_id: string | null
          unit: string
          updated_at: string
          vietnamese_name: string | null
        }
        Insert: {
          category?: string | null
          cost_per_unit: number
          created_at?: string
          id?: string
          last_restock_date?: string | null
          min_threshold?: number | null
          name: string
          quantity?: number
          storage?: string | null
          supplier_id?: string | null
          unit: string
          updated_at?: string
          vietnamese_name?: string | null
        }
        Update: {
          category?: string | null
          cost_per_unit?: number
          created_at?: string
          id?: string
          last_restock_date?: string | null
          min_threshold?: number | null
          name?: string
          quantity?: number
          storage?: string | null
          supplier_id?: string | null
          unit?: string
          updated_at?: string
          vietnamese_name?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "inventory_items_supplier_id_fkey"
            columns: ["supplier_id"]
            isOneToOne: false
            referencedRelation: "suppliers"
            referencedColumns: ["id"]
          }
        ]
      }
      item_ingredients: {
        Row: {
          created_at: string
          id: string
          inventory_item_id: string | null
          is_base_ingredient: boolean | null
          item_id: string | null
          quantity_per_serving: number
          variant_id: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          inventory_item_id?: string | null
          is_base_ingredient?: boolean | null
          item_id?: string | null
          quantity_per_serving: number
          variant_id?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          inventory_item_id?: string | null
          is_base_ingredient?: boolean | null
          item_id?: string | null
          quantity_per_serving?: number
          variant_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "item_ingredients_inventory_item_id_fkey"
            columns: ["inventory_item_id"]
            isOneToOne: false
            referencedRelation: "inventory_items"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "item_ingredients_item_id_fkey"
            columns: ["item_id"]
            isOneToOne: false
            referencedRelation: "items"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "item_ingredients_variant_id_fkey"
            columns: ["variant_id"]
            isOneToOne: false
            referencedRelation: "item_variants"
            referencedColumns: ["id"]
          }
        ]
      }
      item_variants: {
        Row: {
          created_at: string
          id: string
          item_id: string | null
          name: string
          selling_price: number
          type: Database["public"]["Enums"]["variant_type"]
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          item_id?: string | null
          name: string
          selling_price: number
          type: Database["public"]["Enums"]["variant_type"]
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          item_id?: string | null
          name?: string
          selling_price?: number
          type?: Database["public"]["Enums"]["variant_type"]
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "item_variants_item_id_fkey"
            columns: ["item_id"]
            isOneToOne: false
            referencedRelation: "items"
            referencedColumns: ["id"]
          }
        ]
      }
      items: {
        Row: {
          category: string | null
          created_at: string
          description: string | null
          id: string
          image_url: string | null
          name: string
          preparation_time: number | null
          square_item_id: string | null
          updated_at: string
          vietnamese_name: string | null
        }
        Insert: {
          category?: string | null
          created_at?: string
          description?: string | null
          id?: string
          image_url?: string | null
          name: string
          preparation_time?: number | null
          square_item_id?: string | null
          updated_at?: string
          vietnamese_name?: string | null
        }
        Update: {
          category?: string | null
          created_at?: string
          description?: string | null
          id?: string
          image_url?: string | null
          name?: string
          preparation_time?: number | null
          square_item_id?: string | null
          updated_at?: string
          vietnamese_name?: string | null
        }
        Relationships: []
      }
      users: {
        Row: {
          auth_id: string | null
          created_at: string
          email: string
          first_name: string | null
          id: string
          is_active: boolean | null
          last_login: string | null
          last_name: string | null
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string
          username: string
        }
        Insert: {
          auth_id?: string | null
          created_at?: string
          email: string
          first_name?: string | null
          id?: string
          is_active?: boolean | null
          last_login?: string | null
          last_name?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string
          username: string
        }
        Update: {
          auth_id?: string | null
          created_at?: string
          email?: string
          first_name?: string | null
          id?: string
          is_active?: boolean | null
          last_login?: string | null
          last_name?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string
          username?: string
        }
        Relationships: [
          {
            foreignKeyName: "users_auth_id_fkey"
            columns: ["auth_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_user_role: {
        Args: {
          user_auth_id: string
        }
        Returns: Database["public"]["Enums"]["user_role"]
      }
      is_admin_or_manager: {
        Args: {
          user_auth_id: string
        }
        Returns: boolean
      }
    }
    Enums: {
      alert_type: "low_stock" | "expiration" | "price_change"
      transaction_type: "purchase" | "usage" | "waste" | "adjustment"
      user_role: "admin" | "manager" | "staff" | "kitchen"
      variant_type: "dine_in" | "takeaway"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
