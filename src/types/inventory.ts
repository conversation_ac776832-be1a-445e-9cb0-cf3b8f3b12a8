export interface InventoryItem {
  id: string;
  name: string;
  vietnameseName?: string;
  quantity: number;
  unit: string;
  minThreshold?: number;
  costPerUnit: number;
  supplierId?: string;
  lastRestockDate?: Date;
  category?: string;
  storage?: string;
}

export interface Item {
  id: string;
  name: string;
  vietnameseName?: string;
  baseIngredients: ItemIngredient[];
  variants: ItemVariant[];
  category?: string;
  description?: string;
  preparationTime?: number; // in minutes
  image?: string;
  squareItemId?: string; // ID of the corresponding item in Square catalog
}

export interface ItemVariant {
  id: string;
  type: 'dine_in' | 'takeaway';
  name: string;
  sellingPrice: number;
  additionalIngredients: ItemIngredient[];
  nonInventoryItems: NonInventoryItem[];
}

export interface ItemIngredient {
  inventoryItemId: string;
  inventoryItemName?: string; // Optional for backward compatibility
  quantityPerServing: number;
}

export interface NonInventoryItem {
  name: string;
  quantity: number;
  type: 'utensil' | 'container' | 'packaging' | 'other';
}

export interface Supplier {
  id: string;
  name: string;
  contactPerson?: string;
  email?: string;
  phone?: string;
  address?: string;
}

export interface InventoryTransaction {
  id: string;
  inventoryItemId: string;
  type: 'purchase' | 'usage' | 'waste' | 'adjustment';
  quantity: number;
  date: Date;
  cost?: number;
  notes?: string;
  menuItemId?: string; // If the transaction is related to menu item preparation
  userId?: string; // Person who performed the transaction

  // For backward compatibility
  materialId?: string;
}

export interface ItemProduction {
  id: string;
  itemId: string;
  variantId: string;
  variantType: 'dine_in' | 'takeaway';
  quantity: number;
  date: Date;
  notes?: string;
  userId?: string;
  tableNumber?: string; // For dine-in orders
  customerName?: string; // For takeaway orders
}

export interface InventoryAlert {
  id: string;
  inventoryItemId: string;
  type: 'low_stock' | 'expiration' | 'price_change';
  message: string;
  date: Date;
  isRead: boolean;

  // For backward compatibility
  materialId?: string;
}

export interface InventoryReport {
  id: string;
  type: 'valuation' | 'usage' | 'cost_analysis' | 'waste';
  startDate: Date;
  endDate: Date;
  generatedDate: Date;
  data: any; // This would be replaced with specific report data structures
}

// User authentication types
export interface User {
  id: string;
  username: string;
  email: string;
  passwordHash: string;
  role: UserRole;
  firstName?: string;
  lastName?: string;
  createdAt: Date;
  lastLogin?: Date;
  isActive: boolean;
}

export type UserRole = 'admin' | 'manager' | 'staff' | 'kitchen';

export interface UserSession {
  id: string;
  userId: string;
  token: string;
  createdAt: Date;
  expiresAt: Date;
  ipAddress?: string;
  userAgent?: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  user?: Omit<User, 'passwordHash'>;
  token?: string;
  error?: string;
}

// Utility types for filtering and querying
export type InventoryItemFilter = {
  category?: string;
  belowThreshold?: boolean;
  supplierId?: string;
  search?: string;
};

export type ItemFilter = {
  category?: string;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  variantType?: 'dine_in' | 'takeaway';
};

export type UserFilter = {
  role?: UserRole;
  isActive?: boolean;
  search?: string;
};

// Type aliases
export type MenuItem = Item;
export type MenuItemVariant = ItemVariant;
export type MenuItemIngredient = ItemIngredient;
export type MenuItemProduction = ItemProduction;
export type MenuItemFilter = ItemFilter;