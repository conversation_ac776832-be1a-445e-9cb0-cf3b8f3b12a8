import React from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { usePathname } from 'next/navigation';
import Sidebar from './Sidebar';
import NextAuthHeader from './NextAuthHeader';
import { UserRole } from '@/types/inventory';

interface NextAuthMainLayoutProps {
  children: React.ReactNode;
  requiredRoles?: UserRole[];
}

const NextAuthMainLayout: React.FC<NextAuthMainLayoutProps> = ({ children, requiredRoles }) => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const pathname = usePathname();

  // Fast loading - no delays
  if (status === 'loading') {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500 mx-auto mb-2"></div>
          <p className="text-gray-600 text-xs">Loading...</p>
        </div>
      </div>
    );
  }

  // Redirect if not authenticated - immediate, no delays
  if (status === 'unauthenticated') {
    router.push('/auth/nextauth-login');
    return null;
  }

  // Check role-based access - immediate redirect
  if (requiredRoles && session?.user && !requiredRoles.includes(session.user.role as UserRole)) {
    if (session.user.role === 'staff') {
      router.push('/inventory/receive');
    } else {
      router.push('/dashboard-light');
    }
    return null;
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-100">
      <NextAuthHeader />
      <div className="flex flex-1 pt-16">
        <Sidebar activeRoute={pathname || ''} />
        <div className="flex-1 ml-48">
          <main className="m-2 sm:m-6 lg:m-10 p-3 sm:p-4 lg:p-5 bg-white rounded-lg shadow-sm">
            {children}
          </main>
        </div>
      </div>
    </div>
  );
};

export default NextAuthMainLayout;
