import React, { useState, useEffect } from 'react';
import Sidebar from './Sidebar';
import Header from './Header';
import { usePathname } from 'next/navigation';
import ProtectedRoute from '../auth/ProtectedRoute';
import { UserRole } from '@/types/inventory';

interface MainLayoutProps {
  children: React.ReactNode;
  requiredRoles?: UserRole[];
}

const MainLayout: React.FC<MainLayoutProps> = ({ children, requiredRoles }) => {
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);

  // Wait for component to mount
  useEffect(() => {
    setMounted(true);
  }, []);

  // Show loading until mounted to prevent hydration issues
  if (!mounted) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
        <span className="ml-2 text-gray-600 text-sm">Loading...</span>
      </div>
    );
  }

  return (
    <ProtectedRoute allowedRoles={requiredRoles}>
      <div className="flex flex-col min-h-screen bg-gray-100">
        <Header />
        {/* Add padding-top to account for fixed header */}
        <div className="flex flex-1 pt-16">
          <Sidebar activeRoute={pathname || ''} />
          <div className="flex-1 ml-48">
            <main className="m-2 sm:m-6 lg:m-10 p-3 sm:p-4 lg:p-5 bg-white rounded-lg shadow-sm">
              {children}
            </main>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
};

export default MainLayout;
