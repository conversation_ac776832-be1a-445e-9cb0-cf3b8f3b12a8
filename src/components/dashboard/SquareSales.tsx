import React, { useState } from 'react';
import { getDailySales, syncItemSales } from '@/services/squareService';

interface SquareSalesProps {
  onDataLoaded?: (data: any) => void;
}

const SquareSales: React.FC<SquareSalesProps> = ({ onDataLoaded }) => {
  const [loading, setLoading] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [salesData, setSalesData] = useState<any>(null);
  const [selectedDate, setSelectedDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );

  const fetchSalesData = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await getDailySales(new Date(selectedDate));

      if (result.success && result.data) {
        setSalesData(result.data);
        if (onDataLoaded) {
          onDataLoaded(result.data);
        }
      } else {
        setError(result.error || 'Failed to load sales data');
      }
    } catch (err: any) {
      console.error('Error fetching sales data:', err);
      setError(err.message || 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleSyncData = async () => {
    setSyncing(true);
    setError(null);

    try {
      const result = await syncItemSales(new Date(selectedDate));

      if (result.success) {
        // Refresh sales data after sync
        await fetchSalesData();
      } else {
        setError(result.error || 'Failed to sync sales data');
      }
    } catch (err: any) {
      console.error('Error syncing sales data:', err);
      setError(err.message || 'An unexpected error occurred');
    } finally {
      setSyncing(false);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-4">Square Sales</h2>
      <div className="mb-4">
        <label htmlFor="date" className="block mb-2">Select Date:</label>
        <input
          type="date"
          id="date"
          className="border rounded px-3 py-2 w-full"
          value={selectedDate}
          onChange={(e) => setSelectedDate(e.target.value)}
        />
      </div>
      <div className="flex space-x-2 mb-4">
        <button
          onClick={fetchSalesData}
          disabled={loading || syncing}
          className="bg-blue-500 text-white px-4 py-2 rounded disabled:bg-blue-300"
        >
          {loading ? 'Loading...' : 'Fetch Sales'}
        </button>
        <button
          onClick={handleSyncData}
          disabled={loading || syncing}
          className="bg-green-500 text-white px-4 py-2 rounded disabled:bg-green-300"
        >
          {syncing ? 'Syncing...' : 'Sync with Square'}
        </button>
      </div>
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-4">
          {error}
        </div>
      )}
      {salesData && (
        <div className="mt-4">
          <h3 className="text-lg font-medium mb-2">Sales Data for {selectedDate}</h3>
          <div className="border rounded p-4 bg-gray-50">
            <div className="mb-4">
              <p className="font-semibold">Total Sales: ${salesData.totalSales.toFixed(2)}</p>
              <p>Orders: {salesData.orderCount}</p>
            </div>
            <h4 className="font-medium mb-2">Items Sold:</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-4 py-2 text-left">Item</th>
                    <th className="px-4 py-2 text-right">Quantity</th>
                    <th className="px-4 py-2 text-right">Revenue</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {salesData.itemsSold.map((item: any) => (
                    <tr key={item.itemId}>
                      <td className="px-4 py-2">{item.itemName}</td>
                      <td className="px-4 py-2 text-right">{item.quantity}</td>
                      <td className="px-4 py-2 text-right">${item.revenue.toFixed(2)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SquareSales;
