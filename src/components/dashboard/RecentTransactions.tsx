import React from 'react';
import { InventoryTransaction, InventoryItem } from '@/types/inventory';

interface RecentTransactionsProps {
  transactions: InventoryTransaction[];
  inventoryItems?: Record<string, InventoryItem>;
}

const RecentTransactions: React.FC<RecentTransactionsProps> = ({ transactions, inventoryItems = {} }) => {
  if (transactions.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-gray-500 text-sm font-medium mb-4">Recent Transactions</h3>
        <p className="text-gray-400 text-center py-4">No recent transactions</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <h3 className="text-gray-500 text-sm font-medium mb-4">Recent Transactions</h3>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Inventory Item
              </th>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Quantity
              </th>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {transactions.map((transaction) => {
              // Use inventoryItemId if available, fall back to materialId for backward compatibility
              const itemId = (transaction as any).inventoryItemId || (transaction as any).materialId;
              const item = inventoryItems[itemId];

              return (
                <tr key={transaction.id}>
                  <td className="px-4 py-3 text-sm">
                    {item?.name || 'Unknown Item'}
                  </td>
                  <td className="px-4 py-3 text-sm">
                    <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getTransactionTypeColor(transaction.type)}`}>
                      {formatTransactionType(transaction.type)}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-sm">
                    {transaction.quantity} {item?.unit}
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-500">
                    {new Date(transaction.date).toLocaleString()}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// Helper function to format transaction type
const formatTransactionType = (type: InventoryTransaction['type']): string => {
  switch (type) {
    case 'purchase':
      return 'Purchase';
    case 'usage':
      return 'Usage';
    case 'waste':
      return 'Waste';
    case 'adjustment':
      return 'Adjustment';
    default:
      return type;
  }
};

// Helper function to get color based on transaction type
const getTransactionTypeColor = (type: InventoryTransaction['type']): string => {
  switch (type) {
    case 'purchase':
      return 'bg-green-100 text-green-800';
    case 'usage':
      return 'bg-blue-100 text-blue-800';
    case 'waste':
      return 'bg-red-100 text-red-800';
    case 'adjustment':
      return 'bg-yellow-100 text-yellow-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export default RecentTransactions;
