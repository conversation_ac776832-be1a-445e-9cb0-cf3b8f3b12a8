import React from 'react';
import { InventoryAlert, InventoryItem } from '@/types/inventory';

interface AlertsListProps {
  alerts: InventoryAlert[];
  onAlertRead: (alertId: string) => void;
  inventoryItems?: Record<string, InventoryItem>;
}

const AlertsList: React.FC<AlertsListProps> = ({ alerts, onAlertRead, inventoryItems = {} }) => {
  if (alerts.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-gray-500 text-sm font-medium mb-4">Alerts</h3>
        <p className="text-gray-400 text-center py-4">No alerts at this time</p>
      </div>
    );
  }

  const handleMarkAsRead = (alertId: string) => {
    onAlertRead(alertId);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <h3 className="text-gray-500 text-sm font-medium mb-4">Alerts</h3>
      <ul className="divide-y divide-gray-200">
        {alerts.map((alert) => {
          // Use inventoryItemId
          const itemId = (alert as any).inventoryItemId;
          const item = inventoryItems[itemId];

          return (
            <li key={alert.id} className="py-3">
              <div className="flex items-start justify-between">
                <div>
                  <p className="text-sm font-medium">
                    {getAlertTypeIcon(alert.type)} {item?.name || 'Unknown Item'}
                  </p>
                  <p className="text-sm text-gray-500 mt-1">{alert.message}</p>
                  <p className="text-xs text-gray-400 mt-1">
                    {new Date(alert.date).toLocaleString()}
                  </p>
                </div>
                {!alert.isRead && (
                  <button
                    onClick={() => handleMarkAsRead(alert.id)}
                    className="text-xs text-blue-500 hover:text-blue-700"
                  >
                    Mark as read
                  </button>
                )}
              </div>
            </li>
          );
        })}
      </ul>
    </div>
  );
};

// Helper function to get an icon based on alert type
const getAlertTypeIcon = (type: InventoryAlert['type']): string => {
  switch (type) {
    case 'low_stock':
      return '⚠️';
    case 'expiration':
      return '⏱️';
    case 'price_change':
      return '💰';
    default:
      return '📢';
  }
};

export default AlertsList;
