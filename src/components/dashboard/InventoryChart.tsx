'use client';

import React, { useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';

interface InventoryChartProps {
  data: {
    labels: string[];
    values: number[];
    colors?: string[];
  };
  title: string;
  type: 'bar' | 'pie' | 'line';
  height?: number;
}

const InventoryChart: React.FC<InventoryChartProps> = ({ 
  data, 
  title, 
  type,
  height = 300
}) => {
  const chartRef = useRef<HTMLCanvasElement>(null);
  const chartInstance = useRef<Chart | null>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    // Destroy previous chart if it exists
    if (chartInstance.current) {
      chartInstance.current.destroy();
    }

    const ctx = chartRef.current.getContext('2d');
    if (!ctx) return;

    // Default colors if not provided
    const defaultColors = [
      'rgba(54, 162, 235, 0.6)',
      'rgba(255, 99, 132, 0.6)',
      'rgba(255, 206, 86, 0.6)',
      'rgba(75, 192, 192, 0.6)',
      'rgba(153, 102, 255, 0.6)',
      'rgba(255, 159, 64, 0.6)',
    ];

    // Create the chart
    chartInstance.current = new Chart(ctx, {
      type,
      data: {
        labels: data.labels,
        datasets: [
          {
            label: title,
            data: data.values,
            backgroundColor: data.colors || defaultColors,
            borderColor: data.colors?.map(color => color.replace('0.6', '1')) || 
              defaultColors.map(color => color.replace('0.6', '1')),
            borderWidth: 1,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top',
          },
          title: {
            display: true,
            text: title,
            font: {
              size: 16,
            },
          },
        },
        scales: type !== 'pie' ? {
          y: {
            beginAtZero: true,
          },
        } : undefined,
      },
    });

    // Cleanup function
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [data, title, type]);

  return (
    <div className="bg-white rounded-lg shadow p-4" style={{ height }}>
      <canvas ref={chartRef} />
    </div>
  );
};

export default InventoryChart;
