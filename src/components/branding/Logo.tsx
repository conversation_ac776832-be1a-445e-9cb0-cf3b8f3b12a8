'use client';

import React from 'react';
import { useTheme } from '@/contexts/ThemeContext';

interface LogoProps {
  size?: 'small' | 'medium' | 'large';
}

const Logo: React.FC<LogoProps> = ({ size = 'medium' }) => {
  const { colors } = useTheme();
  
  // Determine size dimensions
  const dimensions = {
    small: { width: 100, height: 40 },
    medium: { width: 150, height: 60 },
    large: { width: 200, height: 80 },
  };
  
  const { width, height } = dimensions[size];
  
  return (
    <div 
      className="relative flex items-center justify-center"
      style={{ 
        width, 
        height: height * 0.8,
        backgroundColor: colors.lightCream,
        border: `2px solid ${colors.vibrantRed}`,
        borderRadius: '4px',
      }}
    >
      <div className="text-center">
        <div 
          className="font-bold tracking-wider"
          style={{ 
            color: colors.vibrantRed,
            fontSize: size === 'small' ? '16px' : size === 'medium' ? '20px' : '24px',
          }}
        >
          VIỆT STR-EAT
        </div>
        <div 
          className="text-xs mt-1 px-2 py-0.5 rounded-sm inline-block"
          style={{ 
            backgroundColor: colors.lightRed,
            color: 'white',
            fontSize: size === 'small' ? '8px' : size === 'medium' ? '10px' : '12px',
          }}
        >
          A TASTE OF VIETNAM
        </div>
      </div>
    </div>
  );
};

export default Logo;
