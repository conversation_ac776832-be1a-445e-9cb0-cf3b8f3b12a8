'use client';

import React, { useState, useEffect } from 'react';
import { InventoryItem } from '@/types/inventory';
import { useRouter } from 'next/navigation';
import { inventoryItemsApi } from '@/services/apiService';

interface InventoryItemFormProps {
  initialData?: Partial<InventoryItem>;
  onSubmit?: (data: InventoryItem) => void;
  onCancel?: () => void;
  isEditMode?: boolean;
}

const InventoryItemForm: React.FC<InventoryItemFormProps> = ({
  initialData = {},
  onSubmit,
  onCancel,
  isEditMode = false
}) => {
  const router = useRouter();
  const [formData, setFormData] = useState<Partial<InventoryItem>>({
    name: '',
    vietnameseName: '',
    quantity: 0,
    unit: '',
    minThreshold: 0,
    costPerUnit: 0, // Keep for compatibility but not displayed
    category: '',
    storage: '',
    supplierId: '',
    lastRestockDate: undefined,
    ...initialData
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [storageOptions, setStorageOptions] = useState<string[]>([
    'FRIDGE_1',
    'FRIDGE_2',
    'FREEZER_1',
    'DRINK_FRIDGE',
    'PACKAGING',
    'DRY_STORAGE',
    'CHEMICAL_STORAGE'
  ]);

  useEffect(() => {
    // Only update form data if initialData has actual content (edit mode)
    if (initialData && Object.keys(initialData).length > 0 && initialData.id) {
      console.log('📝 Setting form data from initialData:', initialData);
      setFormData({
        name: '',
        vietnameseName: '',
        quantity: 0,
        unit: '',
        minThreshold: 0,
        costPerUnit: 0, // Keep for compatibility but not displayed
        category: '',
        storage: '',
        supplierId: '',
        lastRestockDate: undefined,
        ...initialData
      });
    }
  }, [initialData]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    // Handle number inputs
    if (type === 'number') {
      setFormData({
        ...formData,
        [name]: value === '' ? '' : Number(value)
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }

    // Clear error when field is edited
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };



  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name) {
      newErrors.name = 'Name is required';
    }

    if (formData.quantity === undefined || formData.quantity === null) {
      newErrors.quantity = 'Quantity is required';
    }

    if (!formData.unit) {
      newErrors.unit = 'Unit is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      let result;

      if (isEditMode && formData.id) {
        // Update existing inventory item
        result = await inventoryItemsApi.update(formData.id, formData);
      } else {
        // Add new inventory item
        result = await inventoryItemsApi.add(formData as Omit<InventoryItem, 'id'>);
      }

      if (onSubmit) {
        onSubmit(result);
      } else {
        router.push('/inventory');
      }
    } catch (error) {
      console.error('Error saving inventory item:', error);
      setErrors({
        form: 'Failed to save inventory item. Please try again.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {errors.form && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{errors.form}</p>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Name */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700">
            Name <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            name="name"
            id="name"
            value={formData.name || ''}
            onChange={handleChange}
            autoComplete="off"
            className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 px-2 ${
              errors.name ? 'border-red-300' : ''
            }`}
          />
          {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
        </div>

        {/* Vietnamese Name */}
        <div>
          <label htmlFor="vietnameseName" className="block text-sm font-medium text-gray-700">
            Vietnamese Name
          </label>
          <input
            type="text"
            name="vietnameseName"
            id="vietnameseName"
            value={formData.vietnameseName || ''}
            onChange={handleChange}
            autoComplete="off"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 px-2"
          />
        </div>

        {/* Quantity */}
        <div>
          <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">
            Quantity <span className="text-red-500">*</span>
          </label>
          <input
            type="number"
            name="quantity"
            id="quantity"
            min="0"
            step="0.01"
            value={formData.quantity === undefined || formData.quantity === null ? '' : formData.quantity}
            onChange={handleChange}
            autoComplete="off"
            placeholder="Enter quantity"
            className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 px-2 ${
              errors.quantity ? 'border-red-300' : ''
            }`}
          />
          {errors.quantity && <p className="mt-1 text-sm text-red-600">{errors.quantity}</p>}
        </div>

        {/* Unit */}
        <div>
          <label htmlFor="unit" className="block text-sm font-medium text-gray-700">
            Unit <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            name="unit"
            id="unit"
            value={formData.unit || ''}
            onChange={handleChange}
            autoComplete="off"
            className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 px-2 ${
              errors.unit ? 'border-red-300' : ''
            }`}
          />
          {errors.unit && <p className="mt-1 text-sm text-red-600">{errors.unit}</p>}
        </div>



        {/* Min Threshold */}
        <div>
          <label htmlFor="minThreshold" className="block text-sm font-medium text-gray-700">
            Minimum Threshold
          </label>
          <input
            type="number"
            name="minThreshold"
            id="minThreshold"
            min="0"
            step="0.01"
            value={formData.minThreshold === undefined ? '' : formData.minThreshold}
            onChange={handleChange}
            autoComplete="off"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 px-2"
          />
        </div>

        {/* Category */}
        <div>
          <label htmlFor="category" className="block text-sm font-medium text-gray-700">
            Category
          </label>
          <select
            name="category"
            id="category"
            value={formData.category || ''}
            onChange={handleChange}
            autoComplete="off"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 px-2"
          >
            <option value="">Select Category</option>
            <option value="proteins">Proteins</option>
            <option value="vegetables">Vegetables</option>
            <option value="herbs">Herbs</option>
            <option value="seafood">Seafood</option>
            <option value="fruits">Fruits</option>
            <option value="sauces">Sauces</option>
            <option value="condiments">Condiments</option>
            <option value="beverages">Beverages</option>
            <option value="frozen_foods">Frozen Foods</option>
            <option value="dry_goods">Dry Goods</option>
            <option value="packaging">Packaging</option>
            <option value="chemicals">Chemicals</option>
            <option value="fuel">Fuel</option>
            <option value="supplies">Supplies</option>
          </select>
        </div>

        {/* Storage Location */}
        <div>
          <label htmlFor="storage" className="block text-sm font-medium text-gray-700">
            Storage Location
          </label>
          <select
            name="storage"
            id="storage"
            value={formData.storage || ''}
            onChange={handleChange}
            autoComplete="off"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 px-2"
          >
            <option value="">Select Storage Location</option>
            {storageOptions.map((option) => (
              <option key={option} value={option}>
                {option.replace(/_/g, ' ')}
              </option>
            ))}
          </select>
        </div>

        {/* Last Restock Date */}
        <div>
          <p className="block text-sm font-medium text-gray-700 mb-1">
            Last Restock Date
          </p>
          <div
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm px-2 py-2 bg-gray-50"
            aria-label="Last Restock Date display"
          >
            {formData.lastRestockDate
              ? new Date(formData.lastRestockDate).toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                })
              : 'No stock yet'}
          </div>
          <input
            type="hidden"
            name="lastRestockDate"
            id="lastRestockDate"
            value={formData.lastRestockDate ? new Date(formData.lastRestockDate).toISOString().split('T')[0] : ''}
            readOnly
            autoComplete="off"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-5">
        <button
          type="button"
          onClick={() => {
            console.log('Cancel button clicked');
            if (onCancel) {
              onCancel();
            } else {
              console.log('Navigating to /inventory');
              router.push('/inventory');
            }
          }}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {isSubmitting ? 'Saving...' : isEditMode ? 'Update' : 'Save'}
        </button>
      </div>
    </form>
  );
};

export default InventoryItemForm;
