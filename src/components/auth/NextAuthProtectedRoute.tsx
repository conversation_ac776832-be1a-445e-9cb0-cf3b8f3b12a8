'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, ReactNode } from 'react'
import { UserRole } from '@/types/inventory'

interface NextAuthProtectedRouteProps {
  children: ReactNode
  allowedRoles?: UserRole[]
}

export default function NextAuthProtectedRoute({ 
  children, 
  allowedRoles 
}: NextAuthProtectedRouteProps) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'loading') return // Still loading

    if (status === 'unauthenticated') {
      router.push('/auth/nextauth-login')
      return
    }

    // Check role-based access
    if (allowedRoles && session?.user && !allowedRoles.includes(session.user.role as UserRole)) {
      if (session.user.role === 'staff') {
        router.push('/inventory/receive')
      } else {
        router.push('/dashboard-light')
      }
      return
    }
  }, [status, session, allowedRoles, router])

  if (status === 'loading') {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600 text-sm">Loading...</p>
        </div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600 text-sm">Redirecting to login...</p>
        </div>
      </div>
    )
  }

  // Check role access
  if (allowedRoles && session?.user && !allowedRoles.includes(session.user.role as UserRole)) {
    return (
      <div className="flex flex-col justify-center items-center h-screen bg-gray-100">
        <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
        <p className="text-gray-600 mb-6">You don't have permission to access this page.</p>
        <button
          onClick={() => router.push('/dashboard-light')}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Go to Dashboard
        </button>
      </div>
    )
  }

  return <>{children}</>
}
