'use client';

import React, { ReactNode, useEffect, useState } from 'react';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { useRouter } from 'next/navigation';
import { UserRole } from '@/types/inventory';

interface ProtectedRouteProps {
  children: ReactNode;
  allowedRoles?: UserRole[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  allowedRoles
}) => {
  const { isAuthenticated, user, loading } = useSupabaseAuth();
  const router = useRouter();
  const [forceRender, setForceRender] = useState(false);

  // Faster timeout to prevent hanging
  useEffect(() => {
    const hardTimeout = setTimeout(() => {
      setForceRender(true);
    }, 1000); // 1 second timeout - much faster

    return () => clearTimeout(hardTimeout);
  }, []);

  // Redirect if not authenticated - SAFELY RE-ENABLED
  useEffect(() => {
    console.log('ProtectedRoute check:', { isAuthenticated, loading, user: !!user });

    // Only redirect if:
    // 1. Not loading
    // 2. Not authenticated
    // 3. We've given enough time for auth to initialize
    if (!loading && !isAuthenticated) {
      console.log('ProtectedRoute: User not authenticated, will redirect');

      // Add a small delay to prevent race conditions
      const redirectTimer = setTimeout(() => {
        if (!isAuthenticated && !loading) {
          console.log('ProtectedRoute: Confirmed redirect needed');
          router.push('/auth/login');
        } else {
          console.log('ProtectedRoute: User authenticated during delay, canceling redirect');
        }
      }, 500); // Shorter delay since auth context already has its own delay

      return () => clearTimeout(redirectTimer);
    }
  }, [isAuthenticated, loading, router, user]);

  // Show loading state (but not if force render is active)
  if (loading && !forceRender) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <span className="text-gray-600 text-sm">Loading...</span>
        </div>
      </div>
    );
  }

  // Return null while redirecting if not authenticated (unless force render is active)
  if (!isAuthenticated && !loading && !forceRender) {
    console.log('ProtectedRoute: Blocking access - user not authenticated');
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-sm">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  // If force render is active and still no auth, show emergency fallback
  if (forceRender && !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Authentication Required</h2>
          <p className="text-gray-600 mb-6">Please log in to continue.</p>
          <button
            onClick={() => router.push('/auth/login')}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  // Check role-based access if roles are specified
  if (allowedRoles && user && !allowedRoles.includes(user.role as UserRole)) {
    // For staff users, automatically redirect to inventory page without showing access denied
    if (user.role === 'staff') {
      useEffect(() => {
        router.push('/inventory');
      }, [router]);

      // Show loading while redirecting
      return (
        <div className="flex justify-center items-center h-screen bg-gray-100">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600 text-sm">Redirecting...</p>
          </div>
        </div>
      );
    }

    // For other roles, show access denied message
    return (
      <div className="flex flex-col justify-center items-center h-screen bg-gray-100">
        <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
        <p className="text-gray-600 mb-6">
          You don't have permission to access this page.
        </p>
        <button
          onClick={() => router.push('/')}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Go to Dashboard
        </button>
      </div>
    );
  }

  // Render children if authenticated and authorized
  return <>{children}</>;
};

export default ProtectedRoute;
