/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  // Add CORS headers for all requests
  async headers() {
    return [
      {
        // Apply these headers to all routes
        source: '/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; connect-src 'self' https://ayzppqpygicvbptdqrnt.supabase.co https://*.supabase.co https://connect.squareup.com https://*.squareup.com; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; frame-src 'self';",
          },
        ],
      },
      {
        // Specific headers for Next.js internal paths
        source: '/_next/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
        ],
      },
    ];
  },
  // Configure webpack to handle webpack-internal URLs properly
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // Client-side specific config
      config.module.rules.push({
        test: /webpack-internal:/,
        loader: 'ignore-loader',
      });
    }

    // Disable webpack caching to fix build issues
    config.cache = false;

    return config;
  },
  // Production optimizations
  // Remove standalone output for Vercel deployment
  // output: 'standalone',
  // Ensure static assets are included in the output
  outputFileTracing: true,
  experimental: {
    serverComponentsExternalPackages: ['mongodb'],
    // Disable features that might cause build issues
    esmExternals: false,
  },
  // Configure for custom Express server
  poweredByHeader: false,
  // Completely disable static generation
  staticPageGenerationTimeout: 1000,
  reactStrictMode: true,
  swcMinify: true,
  // Force dynamic rendering for all pages
  trailingSlash: false,
  // Configure images
  images: {
    unoptimized: true,
  },
  // Force all pages to be server-side rendered
  compiler: {
    styledComponents: true,
  },
  // Ensure proper build output
  generateBuildId: async () => {
    return 'build-id-' + Date.now();
  },
};

module.exports = nextConfig;
